"""
Targeted tests to push cot_reports/models.py to 100% coverage.
"""

from datetime import date
from src.forex_bot.cot_reports.models import COTReport, COTPositioning


def test_cot_report_zero_open_interest():
    """Test COTReport percentage calculations with zero open interest - covers lines 82, 94, 106."""
    # Create COTReport with zero open interest
    report = COTReport(
        report_date=date(2023, 1, 1),
        currency='EUR',
        contract='EURO FX',
        non_commercial_long=0,
        non_commercial_short=0,
        non_commercial_spreading=0,
        commercial_long=0,
        commercial_short=0,
        total_reportable_long=0,
        total_reportable_short=0,
        non_reportable_long=0,
        non_reportable_short=0
    )

    # Check that open_interest is zero
    assert report.open_interest == 0

    # Check that percentage calculations return 0.0 for zero division protection
    assert report.non_commercial_net_percentage == 0.0  # Tests line 82
    assert report.commercial_net_percentage == 0.0      # Tests line 94
    assert report.non_reportable_net_percentage == 0.0  # Tests line 106


def test_cot_report_net_calculations():
    """Test COTReport net position calculations - covers lines 41, 51, 61."""
    report = COTReport(
        report_date=date(2023, 1, 1),
        currency='EUR',
        contract='EURO FX',
        non_commercial_long=1000,
        non_commercial_short=800,
        non_commercial_spreading=100,
        commercial_long=500,
        commercial_short=700,
        total_reportable_long=1500,
        total_reportable_short=1500,
        non_reportable_long=200,
        non_reportable_short=150
    )

    # Test net calculations (covers lines 41, 51, 61)
    assert report.non_commercial_net == 200  # 1000 - 800 (line 41)
    assert report.commercial_net == -200     # 500 - 700 (line 51)
    assert report.non_reportable_net == 50   # 200 - 150 (line 61)


def test_cot_report_percentage_calculations():
    """Test COTReport percentage calculations with non-zero open interest - covers lines 83, 95, 107."""
    report = COTReport(
        report_date=date(2023, 1, 1),
        currency='EUR',
        contract='EURO FX',
        non_commercial_long=1000,
        non_commercial_short=800,
        non_commercial_spreading=100,
        commercial_long=500,
        commercial_short=700,
        total_reportable_long=1500,
        total_reportable_short=1500,
        non_reportable_long=200,
        non_reportable_short=150
    )

    # Open interest = total_reportable_long + non_reportable_long = 1500 + 200 = 1700
    assert report.open_interest == 1700

    # Test percentage calculations (covers lines 83, 95, 107)
    assert abs(report.non_commercial_net_percentage - (200 / 1700 * 100)) < 0.01  # line 83
    assert abs(report.commercial_net_percentage - (-200 / 1700 * 100)) < 0.01     # line 95
    assert abs(report.non_reportable_net_percentage - (50 / 1700 * 100)) < 0.01   # line 107


def test_cot_positioning_sentiment_extremely_bullish():
    """Test COTPositioning sentiment for extremely bullish case - covers lines 143-144."""
    positioning = COTPositioning(
        currency='EUR',
        latest_report_date=date(2023, 1, 1),
        net_speculator_position=75.0,
        net_hedger_position=-75.0,
        net_small_trader_position=0.0,
        z_score=2.5,
        percentile=95.0,
        position_change_4_weeks=10.0,
        position_change_12_weeks=20.0,
        is_extreme_long=True,
        is_extreme_short=False,
        is_crowded_long=False,
        is_crowded_short=False
    )

    assert positioning.positioning_sentiment == "Extremely Bullish"  # line 143-144


def test_cot_positioning_sentiment_bullish():
    """Test COTPositioning sentiment for bullish case - covers lines 145-146."""
    positioning = COTPositioning(
        currency='EUR',
        latest_report_date=date(2023, 1, 1),
        net_speculator_position=60.0,
        net_hedger_position=-60.0,
        net_small_trader_position=0.0,
        z_score=1.5,
        percentile=80.0,
        position_change_4_weeks=5.0,
        position_change_12_weeks=10.0,
        is_extreme_long=False,
        is_extreme_short=False,
        is_crowded_long=True,
        is_crowded_short=False
    )

    assert positioning.positioning_sentiment == "Bullish"  # line 145-146


def test_cot_positioning_sentiment_extremely_bearish():
    """Test COTPositioning sentiment for extremely bearish case - covers lines 147-148."""
    positioning = COTPositioning(
        currency='EUR',
        latest_report_date=date(2023, 1, 1),
        net_speculator_position=-75.0,
        net_hedger_position=75.0,
        net_small_trader_position=0.0,
        z_score=-2.5,
        percentile=5.0,
        position_change_4_weeks=-10.0,
        position_change_12_weeks=-20.0,
        is_extreme_long=False,
        is_extreme_short=True,
        is_crowded_long=False,
        is_crowded_short=False
    )

    assert positioning.positioning_sentiment == "Extremely Bearish"  # line 147-148


def test_cot_positioning_sentiment_bearish():
    """Test COTPositioning sentiment for bearish case - covers lines 149-150."""
    positioning = COTPositioning(
        currency='EUR',
        latest_report_date=date(2023, 1, 1),
        net_speculator_position=-60.0,
        net_hedger_position=60.0,
        net_small_trader_position=0.0,
        z_score=-1.5,
        percentile=20.0,
        position_change_4_weeks=-5.0,
        position_change_12_weeks=-10.0,
        is_extreme_long=False,
        is_extreme_short=False,
        is_crowded_long=False,
        is_crowded_short=True
    )

    assert positioning.positioning_sentiment == "Bearish"  # line 149-150


def test_cot_positioning_sentiment_neutral():
    """Test COTPositioning sentiment for neutral case - covers lines 151-152."""
    positioning = COTPositioning(
        currency='EUR',
        latest_report_date=date(2023, 1, 1),
        net_speculator_position=10.0,
        net_hedger_position=-10.0,
        net_small_trader_position=0.0,
        z_score=0.2,
        percentile=55.0,
        position_change_4_weeks=1.0,
        position_change_12_weeks=2.0,
        is_extreme_long=False,
        is_extreme_short=False,
        is_crowded_long=False,
        is_crowded_short=False
    )

    assert positioning.positioning_sentiment == "Neutral"  # line 151-152


def test_cot_positioning_contrarian_signal_sell():
    """Test COTPositioning contrarian signal for SELL case - covers lines 162-163."""
    positioning = COTPositioning(
        currency='EUR',
        latest_report_date=date(2023, 1, 1),
        net_speculator_position=75.0,
        net_hedger_position=-75.0,
        net_small_trader_position=0.0,
        z_score=2.5,
        percentile=95.0,
        position_change_4_weeks=10.0,
        position_change_12_weeks=20.0,
        is_extreme_long=True,
        is_extreme_short=False,
        is_crowded_long=False,
        is_crowded_short=False
    )

    assert positioning.contrarian_signal == "SELL"  # line 162-163


def test_cot_positioning_contrarian_signal_buy():
    """Test COTPositioning contrarian signal for BUY case - covers lines 164-165."""
    positioning = COTPositioning(
        currency='EUR',
        latest_report_date=date(2023, 1, 1),
        net_speculator_position=-75.0,
        net_hedger_position=75.0,
        net_small_trader_position=0.0,
        z_score=-2.5,
        percentile=5.0,
        position_change_4_weeks=-10.0,
        position_change_12_weeks=-20.0,
        is_extreme_long=False,
        is_extreme_short=True,
        is_crowded_long=False,
        is_crowded_short=False
    )

    assert positioning.contrarian_signal == "BUY"  # line 164-165


def test_cot_positioning_contrarian_signal_none():
    """Test COTPositioning contrarian signal for None case - covers lines 166-167."""
    positioning = COTPositioning(
        currency='EUR',
        latest_report_date=date(2023, 1, 1),
        net_speculator_position=10.0,
        net_hedger_position=-10.0,
        net_small_trader_position=0.0,
        z_score=0.2,
        percentile=55.0,
        position_change_4_weeks=1.0,
        position_change_12_weeks=2.0,
        is_extreme_long=False,
        is_extreme_short=False,
        is_crowded_long=False,
        is_crowded_short=False
    )

    assert positioning.contrarian_signal is None  # line 166-167