"""
Phase 6C: Simple test for market_depth_visualizer/models.py to push from 59% to 90%+ coverage.
"""

import pytest
from datetime import datetime, timezone
from unittest.mock import patch
from src.forex_bot.market_depth_visualizer.models import (
    MarketDepthSnapshot, MarketDepthVisualization, MarketDepthDashboard,
    DashboardSettings, VisualizationType
)


class TestMarketDepthPhase6CSimple:
    """Simple test to push market_depth_visualizer/models.py to 90%+ coverage"""

    def test_comprehensive_fallback_classes(self):
        """Test all fallback classes when pydantic is unavailable"""
        
        # Test with pydantic unavailable to trigger fallback classes
        with patch('src.forex_bot.market_depth_visualizer.models.PYDANTIC_AVAILABLE', False):
            import importlib
            import src.forex_bot.market_depth_visualizer.models as models_module
            importlib.reload(models_module)
            
            # Test fallback BaseModel
            base_model = models_module.BaseModel()
            assert base_model is not None
            
            # Test fallback DashboardSettings
            dashboard_settings = models_module.DashboardSettings()
            assert dashboard_settings is not None
            
            # Test fallback VisualizationSettings
            viz_settings = models_module.VisualizationSettings()
            assert viz_settings is not None
            
            # Test fallback MarketDepthSnapshot
            now = datetime.now(timezone.utc)
            snapshot = models_module.MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340],
                bid_volumes=[1000.0],
                ask_prices=[1.2341],
                ask_volumes=[800.0]
            )
            assert snapshot.symbol == "EURUSD"
            
            # Test fallback MarketDepthVisualization
            visualization = models_module.MarketDepthVisualization(
                type=models_module.VisualizationType.DEPTH_CHART,
                image_data=b"fake_image_data",
                metadata={}
            )
            assert visualization.type == models_module.VisualizationType.DEPTH_CHART
            
            # Test fallback MarketDepthDashboard
            dashboard = models_module.MarketDepthDashboard(
                symbol="GBPUSD",
                timestamp=now,
                visualizations={
                    models_module.VisualizationType.DEPTH_CHART: visualization
                },
                settings=dashboard_settings
            )
            assert dashboard.symbol == "GBPUSD"