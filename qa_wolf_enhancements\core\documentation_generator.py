#!/usr/bin/env python3
"""
QA Wolf Enhancement: Documentation Generator

This module automatically generates comprehensive documentation for the Forex Trading Bot,
including API references, configuration guides, and usage documentation.

SAFETY LEVEL: MAXIMUM - Documentation generation only, no trading logic modification
"""

import ast
import inspect
import json
import re
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class DocumentationGenerator:
    """
    Comprehensive documentation generator for the Forex Trading Bot.
    
    SAFETY GUARANTEE: This class only reads code and generates documentation,
    never modifies trading logic or core functionality.
    """
    
    def __init__(self, project_root: str):
        self.project_root = Path(project_root)
        self.src_path = self.project_root / "src" / "forex_bot"
        self.docs_path = self.project_root / "qa_wolf_enhancements" / "docs"
        
        # Ensure docs directory exists
        self.docs_path.mkdir(exist_ok=True)
        
        # Protected modules - document but mark as protected
        self.protected_modules = {
            'signal_generator.py',
            'trade_executor.py', 
            'position_sizer.py',
            'mt5_client.py',
            'gemini_client.py',
            'bot_orchestrator.py'
        }
        
        logger.info("Documentation Generator initialized - SAFE MODE")
    
    def generate_api_reference(self) -> str:
        """Generate comprehensive API reference documentation."""
        
        logger.info("📚 Generating API reference documentation...")
        
        api_doc = []
        api_doc.append("# 📚 Forex Trading Bot - API Reference")
        api_doc.append("")
        api_doc.append("**Generated automatically by QA Wolf Documentation Generator**")
        api_doc.append(f"**Generated on:** {datetime.now(timezone.utc).strftime('%Y-%m-%d %H:%M:%S UTC')}")
        api_doc.append("")
        api_doc.append("## 🛡️ Safety Notice")
        api_doc.append("")
        api_doc.append("This documentation is generated from code analysis only.")
        api_doc.append("Protected trading modules are documented but marked as **READ-ONLY**.")
        api_doc.append("")
        
        # Analyze each Python file in the src directory
        if self.src_path.exists():
            for py_file in self.src_path.rglob("*.py"):
                if py_file.name == "__init__.py":
                    continue
                
                relative_path = py_file.relative_to(self.src_path)
                module_name = str(relative_path).replace("/", ".").replace("\\", ".").replace(".py", "")
                
                # Check if this is a protected module
                is_protected = any(protected in py_file.name for protected in self.protected_modules)
                
                api_doc.append(f"## 📦 Module: `{module_name}`")
                api_doc.append("")
                
                if is_protected:
                    api_doc.append("🛡️ **PROTECTED MODULE** - Core trading logic, read-only documentation")
                    api_doc.append("")
                
                # Analyze the file
                module_info = self._analyze_python_file(py_file)
                
                if module_info['docstring']:
                    api_doc.append("### Description")
                    api_doc.append("")
                    api_doc.append(module_info['docstring'])
                    api_doc.append("")
                
                # Document classes
                if module_info['classes']:
                    api_doc.append("### Classes")
                    api_doc.append("")
                    
                    for class_info in module_info['classes']:
                        api_doc.append(f"#### `{class_info['name']}`")
                        api_doc.append("")
                        
                        if class_info['docstring']:
                            api_doc.append(class_info['docstring'])
                            api_doc.append("")
                        
                        # Document methods
                        if class_info['methods']:
                            api_doc.append("**Methods:**")
                            api_doc.append("")
                            
                            for method in class_info['methods']:
                                api_doc.append(f"- `{method['name']}({method['signature']})`")
                                if method['docstring']:
                                    api_doc.append(f"  - {method['docstring']}")
                                api_doc.append("")
                
                # Document functions
                if module_info['functions']:
                    api_doc.append("### Functions")
                    api_doc.append("")
                    
                    for func_info in module_info['functions']:
                        api_doc.append(f"#### `{func_info['name']}({func_info['signature']})`")
                        api_doc.append("")
                        
                        if func_info['docstring']:
                            api_doc.append(func_info['docstring'])
                            api_doc.append("")
                
                api_doc.append("---")
                api_doc.append("")
        
        # Save API reference
        api_ref_path = self.docs_path / "api_reference.md"
        with open(api_ref_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(api_doc))
        
        logger.info(f"✅ API reference saved to {api_ref_path}")
        return str(api_ref_path)
    
    def generate_installation_guide(self) -> str:
        """Generate installation and setup guide."""
        
        logger.info("📖 Generating installation guide...")
        
        install_doc = []
        install_doc.append("# 🚀 QA Wolf Enhancement Suite - Installation Guide")
        install_doc.append("")
        install_doc.append("**Professional-grade testing and monitoring for your Forex Trading Bot**")
        install_doc.append("")
        
        install_doc.append("## 📋 Prerequisites")
        install_doc.append("")
        install_doc.append("- **Python 3.8+** (recommended: Python 3.9 or higher)")
        install_doc.append("- **50MB disk space** for QA Wolf enhancements")
        install_doc.append("- **Windows, Linux, or macOS** operating system")
        install_doc.append("- **Existing Forex Trading Bot** (your current project)")
        install_doc.append("")
        
        install_doc.append("## 🔧 Installation Steps")
        install_doc.append("")
        install_doc.append("### Step 1: Verify Python Installation")
        install_doc.append("```bash")
        install_doc.append("python --version")
        install_doc.append("# Should show Python 3.8 or higher")
        install_doc.append("```")
        install_doc.append("")
        
        install_doc.append("### Step 2: Navigate to Project Directory")
        install_doc.append("```bash")
        install_doc.append("cd /path/to/your/Forex_Metatrader_Bot")
        install_doc.append("```")
        install_doc.append("")
        
        install_doc.append("### Step 3: Install Dependencies")
        install_doc.append("```bash")
        install_doc.append("cd qa_wolf_enhancements")
        install_doc.append("pip install -r requirements.txt")
        install_doc.append("```")
        install_doc.append("")
        
        install_doc.append("### Step 4: Verify Installation")
        install_doc.append("```bash")
        install_doc.append("python scripts/verify_installation.py")
        install_doc.append("```")
        install_doc.append("")
        
        install_doc.append("## 🚀 Quick Start")
        install_doc.append("")
        install_doc.append("### Start Monitoring (Terminal 1)")
        install_doc.append("```bash")
        install_doc.append("python scripts/start_monitoring.py")
        install_doc.append("```")
        install_doc.append("")
        
        install_doc.append("### Start Dashboard (Terminal 2)")
        install_doc.append("```bash")
        install_doc.append("python scripts/start_dashboard.py")
        install_doc.append("```")
        install_doc.append("")
        
        install_doc.append("### Access Dashboard")
        install_doc.append("Open your browser and go to: **http://localhost:5000**")
        install_doc.append("")
        
        install_doc.append("## 🧪 Run Tests")
        install_doc.append("```bash")
        install_doc.append("python scripts/run_api_tests.py")
        install_doc.append("```")
        install_doc.append("")
        
        install_doc.append("## 🛡️ Safety Verification")
        install_doc.append("")
        install_doc.append("After installation, verify that:")
        install_doc.append("- ✅ Your trading bot continues to operate normally")
        install_doc.append("- ✅ No trading logic has been modified")
        install_doc.append("- ✅ System performance impact is minimal (<2%)")
        install_doc.append("- ✅ All QA Wolf services run in parallel")
        install_doc.append("")
        
        install_doc.append("## 🔧 Troubleshooting")
        install_doc.append("")
        install_doc.append("### Common Issues")
        install_doc.append("")
        install_doc.append("**1. Import Errors**")
        install_doc.append("```bash")
        install_doc.append("# Solution: Install missing dependencies")
        install_doc.append("pip install flask flask-socketio psutil requests")
        install_doc.append("```")
        install_doc.append("")
        
        install_doc.append("**2. Port Already in Use**")
        install_doc.append("```bash")
        install_doc.append("# Solution: Change dashboard port")
        install_doc.append("# Edit scripts/start_dashboard.py and change port=5000 to port=5001")
        install_doc.append("```")
        install_doc.append("")
        
        install_doc.append("**3. Permission Errors**")
        install_doc.append("```bash")
        install_doc.append("# Solution: Run with appropriate permissions")
        install_doc.append("# On Windows: Run as Administrator if needed")
        install_doc.append("# On Linux/Mac: Use sudo if required")
        install_doc.append("```")
        install_doc.append("")
        
        install_doc.append("## 📞 Support")
        install_doc.append("")
        install_doc.append("If you encounter issues:")
        install_doc.append("1. Check the troubleshooting guide: `docs/troubleshooting.md`")
        install_doc.append("2. Review log files in: `reports/` directory")
        install_doc.append("3. Verify system requirements are met")
        install_doc.append("")
        
        install_doc.append("## ✅ Success Indicators")
        install_doc.append("")
        install_doc.append("Installation is successful when you see:")
        install_doc.append("- 🚀 Monitoring overlay starts without errors")
        install_doc.append("- 🌐 Dashboard loads at http://localhost:5000")
        install_doc.append("- 📊 Real-time charts display system metrics")
        install_doc.append("- 🛡️ Trading bot continues normal operation")
        install_doc.append("")
        
        # Save installation guide
        install_path = self.docs_path / "installation_guide.md"
        with open(install_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(install_doc))
        
        logger.info(f"✅ Installation guide saved to {install_path}")
        return str(install_path)
    
    def generate_user_manual(self) -> str:
        """Generate comprehensive user manual."""
        
        logger.info("📘 Generating user manual...")
        
        manual_doc = []
        manual_doc.append("# 📘 QA Wolf Enhancement Suite - User Manual")
        manual_doc.append("")
        manual_doc.append("**Complete guide to using the QA Wolf Enhancement Suite**")
        manual_doc.append("")
        
        manual_doc.append("## 🎯 Overview")
        manual_doc.append("")
        manual_doc.append("The QA Wolf Enhancement Suite provides:")
        manual_doc.append("- 📊 **Real-time monitoring** of your trading bot")
        manual_doc.append("- 🌐 **Professional web dashboard** with live charts")
        manual_doc.append("- 🧪 **Comprehensive testing** for API reliability")
        manual_doc.append("- 📈 **Performance analysis** and optimization")
        manual_doc.append("- 🛡️ **Zero trading impact** guarantee")
        manual_doc.append("")
        
        manual_doc.append("## 🚀 Getting Started")
        manual_doc.append("")
        manual_doc.append("### 1. Start Monitoring")
        manual_doc.append("```bash")
        manual_doc.append("python scripts/start_monitoring.py")
        manual_doc.append("```")
        manual_doc.append("")
        manual_doc.append("This starts real-time system monitoring with:")
        manual_doc.append("- CPU, Memory, and Disk usage tracking")
        manual_doc.append("- Trading bot status detection")
        manual_doc.append("- Alert threshold monitoring")
        manual_doc.append("- Data storage in SQLite database")
        manual_doc.append("")
        
        manual_doc.append("### 2. Launch Web Dashboard")
        manual_doc.append("```bash")
        manual_doc.append("python scripts/start_dashboard.py")
        manual_doc.append("```")
        manual_doc.append("")
        manual_doc.append("Access the dashboard at: **http://localhost:5000**")
        manual_doc.append("")
        
        manual_doc.append("## 🌐 Web Dashboard Features")
        manual_doc.append("")
        manual_doc.append("### Real-Time Monitoring")
        manual_doc.append("- **System Performance Charts**: Live CPU, Memory, Disk usage")
        manual_doc.append("- **Trading Bot Status**: Active/Idle/Inactive with timestamps")
        manual_doc.append("- **Alert System**: Color-coded notifications")
        manual_doc.append("- **Connection Status**: Real-time connection indicator")
        manual_doc.append("")
        
        manual_doc.append("### Dashboard Sections")
        manual_doc.append("")
        manual_doc.append("#### 1. Header Section")
        manual_doc.append("- QA Wolf branding and safety indicators")
        manual_doc.append("- Last update timestamp")
        manual_doc.append("- Connection status indicator")
        manual_doc.append("")
        
        manual_doc.append("#### 2. Status Cards")
        manual_doc.append("- **Trading Bot Status**: Current operational state")
        manual_doc.append("- **CPU Usage**: Real-time processor utilization")
        manual_doc.append("- **Memory Usage**: RAM consumption tracking")
        manual_doc.append("- **System Health**: Overall system status")
        manual_doc.append("")
        
        manual_doc.append("#### 3. Performance Chart")
        manual_doc.append("- Interactive line chart with CPU and Memory trends")
        manual_doc.append("- 30-point rolling window (last 15 minutes)")
        manual_doc.append("- Automatic scaling and updates")
        manual_doc.append("")
        
        manual_doc.append("#### 4. Alerts Panel")
        manual_doc.append("- Real-time system alerts and notifications")
        manual_doc.append("- Color-coded severity levels")
        manual_doc.append("- Timestamp tracking")
        manual_doc.append("")
        
        manual_doc.append("## 🧪 Testing Features")
        manual_doc.append("")
        manual_doc.append("### Run API Integration Tests")
        manual_doc.append("```bash")
        manual_doc.append("python scripts/run_api_tests.py")
        manual_doc.append("```")
        manual_doc.append("")
        
        manual_doc.append("### Test Categories")
        manual_doc.append("1. **MT5 Connection Reliability**")
        manual_doc.append("   - Connection establishment time")
        manual_doc.append("   - Success rate measurement")
        manual_doc.append("   - Performance grading")
        manual_doc.append("")
        
        manual_doc.append("2. **Broker API Response Times**")
        manual_doc.append("   - Market data API testing")
        manual_doc.append("   - Account info API validation")
        manual_doc.append("   - Historical data performance")
        manual_doc.append("")
        
        manual_doc.append("3. **Network Resilience**")
        manual_doc.append("   - High latency scenarios")
        manual_doc.append("   - Connection timeout handling")
        manual_doc.append("   - Recovery time measurement")
        manual_doc.append("")
        
        manual_doc.append("4. **Concurrent Operations**")
        manual_doc.append("   - Multiple simultaneous API calls")
        manual_doc.append("   - Thread safety validation")
        manual_doc.append("   - Performance under load")
        manual_doc.append("")
        
        manual_doc.append("5. **Error Handling**")
        manual_doc.append("   - Connection failure recovery")
        manual_doc.append("   - Timeout error handling")
        manual_doc.append("   - Rate limiting scenarios")
        manual_doc.append("")
        
        manual_doc.append("## 📊 Understanding Metrics")
        manual_doc.append("")
        manual_doc.append("### Performance Grades")
        manual_doc.append("- **EXCELLENT**: >95% success rate, <200ms response time")
        manual_doc.append("- **GOOD**: >80% success rate, <500ms response time")
        manual_doc.append("- **NEEDS_IMPROVEMENT**: <80% success rate or >500ms response time")
        manual_doc.append("")
        
        manual_doc.append("### Alert Thresholds")
        manual_doc.append("- **CPU Warning**: >70% usage")
        manual_doc.append("- **CPU Critical**: >85% usage")
        manual_doc.append("- **Memory Warning**: >75% usage")
        manual_doc.append("- **Memory Critical**: >90% usage")
        manual_doc.append("")
        
        manual_doc.append("### Trading Bot Status")
        manual_doc.append("- **ACTIVE**: Recent log activity (<5 minutes)")
        manual_doc.append("- **IDLE**: Some recent activity (<30 minutes)")
        manual_doc.append("- **INACTIVE**: No recent activity (>30 minutes)")
        manual_doc.append("- **NO_LOGS**: No log files found")
        manual_doc.append("")
        
        manual_doc.append("## 🔧 Configuration")
        manual_doc.append("")
        manual_doc.append("### Monitoring Intervals")
        manual_doc.append("- **Default**: 60 seconds for monitoring, 30 seconds for dashboard")
        manual_doc.append("- **Customization**: Edit scripts to change intervals")
        manual_doc.append("- **Performance**: Lower intervals = more frequent updates, higher resource usage")
        manual_doc.append("")
        
        manual_doc.append("### Dashboard Port")
        manual_doc.append("- **Default**: Port 5000")
        manual_doc.append("- **Change**: Edit `scripts/start_dashboard.py`")
        manual_doc.append("- **Access**: http://localhost:[PORT]")
        manual_doc.append("")
        
        manual_doc.append("## 📁 File Locations")
        manual_doc.append("")
        manual_doc.append("### Important Directories")
        manual_doc.append("- **Scripts**: `qa_wolf_enhancements/scripts/`")
        manual_doc.append("- **Documentation**: `qa_wolf_enhancements/docs/`")
        manual_doc.append("- **Reports**: `qa_wolf_enhancements/reports/`")
        manual_doc.append("- **Data**: `qa_wolf_enhancements/data/`")
        manual_doc.append("- **Core Modules**: `qa_wolf_enhancements/core/`")
        manual_doc.append("")
        
        manual_doc.append("### Log Files")
        manual_doc.append("- **Monitoring Database**: `data/monitoring.db`")
        manual_doc.append("- **Test Reports**: `reports/api_test_*.json`")
        manual_doc.append("- **Daily Reports**: `reports/daily_status_reports/`")
        manual_doc.append("")
        
        manual_doc.append("## 🛡️ Safety Features")
        manual_doc.append("")
        manual_doc.append("### Protected Modules")
        manual_doc.append("The following trading modules are **NEVER** modified:")
        manual_doc.append("- `signal_generator.py` - Trading signal logic")
        manual_doc.append("- `trade_executor.py` - Trade execution")
        manual_doc.append("- `position_sizer.py` - Position sizing")
        manual_doc.append("- `mt5_client.py` - MT5 connection")
        manual_doc.append("- `gemini_client.py` - AI client")
        manual_doc.append("- `bot_orchestrator.py` - Main trading loop")
        manual_doc.append("")
        
        manual_doc.append("### Emergency Procedures")
        manual_doc.append("If you need to stop all QA Wolf services immediately:")
        manual_doc.append("1. Press **Ctrl+C** in monitoring terminal")
        manual_doc.append("2. Press **Ctrl+C** in dashboard terminal")
        manual_doc.append("3. Run: `python scripts/emergency_shutdown.py` (if available)")
        manual_doc.append("")
        
        manual_doc.append("## 📈 Best Practices")
        manual_doc.append("")
        manual_doc.append("### Daily Usage")
        manual_doc.append("1. Start monitoring when you begin trading")
        manual_doc.append("2. Check dashboard periodically for system health")
        manual_doc.append("3. Run API tests weekly to validate performance")
        manual_doc.append("4. Review reports monthly for optimization opportunities")
        manual_doc.append("")
        
        manual_doc.append("### Performance Optimization")
        manual_doc.append("- Monitor resource usage and adjust intervals if needed")
        manual_doc.append("- Keep dashboard open only when actively monitoring")
        manual_doc.append("- Archive old reports to save disk space")
        manual_doc.append("- Update dependencies regularly")
        manual_doc.append("")
        
        manual_doc.append("## 🔍 Troubleshooting")
        manual_doc.append("")
        manual_doc.append("### Common Issues")
        manual_doc.append("")
        manual_doc.append("**Dashboard not loading**")
        manual_doc.append("- Check if port 5000 is available")
        manual_doc.append("- Verify Flask is installed: `pip install flask flask-socketio`")
        manual_doc.append("- Check firewall settings")
        manual_doc.append("")
        
        manual_doc.append("**Monitoring not starting**")
        manual_doc.append("- Verify psutil is installed: `pip install psutil`")
        manual_doc.append("- Check file permissions")
        manual_doc.append("- Ensure SQLite database can be created")
        manual_doc.append("")
        
        manual_doc.append("**High resource usage**")
        manual_doc.append("- Increase monitoring intervals")
        manual_doc.append("- Close unnecessary browser tabs")
        manual_doc.append("- Reduce chart data points")
        manual_doc.append("")
        
        manual_doc.append("For detailed troubleshooting, see: `docs/troubleshooting.md`")
        manual_doc.append("")
        
        # Save user manual
        manual_path = self.docs_path / "user_manual.md"
        with open(manual_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(manual_doc))
        
        logger.info(f"✅ User manual saved to {manual_path}")
        return str(manual_path)
    
    def _analyze_python_file(self, file_path: Path) -> Dict[str, Any]:
        """Analyze a Python file and extract documentation information."""
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Parse the AST
            tree = ast.parse(content)
            
            # Extract module docstring
            module_docstring = ast.get_docstring(tree)
            
            # Extract classes and functions
            classes = []
            functions = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.ClassDef):
                    class_info = {
                        'name': node.name,
                        'docstring': ast.get_docstring(node),
                        'methods': []
                    }
                    
                    # Extract methods
                    for item in node.body:
                        if isinstance(item, ast.FunctionDef):
                            method_info = {
                                'name': item.name,
                                'docstring': ast.get_docstring(item),
                                'signature': self._get_function_signature(item)
                            }
                            class_info['methods'].append(method_info)
                    
                    classes.append(class_info)
                
                elif isinstance(node, ast.FunctionDef) and not any(isinstance(parent, ast.ClassDef) for parent in ast.walk(tree)):
                    func_info = {
                        'name': node.name,
                        'docstring': ast.get_docstring(node),
                        'signature': self._get_function_signature(node)
                    }
                    functions.append(func_info)
            
            return {
                'docstring': module_docstring,
                'classes': classes,
                'functions': functions
            }
            
        except Exception as e:
            logger.error(f"Error analyzing {file_path}: {e}")
            return {
                'docstring': None,
                'classes': [],
                'functions': []
            }
    
    def _get_function_signature(self, func_node: ast.FunctionDef) -> str:
        """Extract function signature from AST node."""
        
        try:
            args = []
            
            # Regular arguments
            for arg in func_node.args.args:
                args.append(arg.arg)
            
            # Default arguments
            defaults_offset = len(func_node.args.args) - len(func_node.args.defaults)
            for i, default in enumerate(func_node.args.defaults):
                arg_index = defaults_offset + i
                if arg_index < len(args):
                    args[arg_index] += "=..."
            
            # *args
            if func_node.args.vararg:
                args.append(f"*{func_node.args.vararg.arg}")
            
            # **kwargs
            if func_node.args.kwarg:
                args.append(f"**{func_node.args.kwarg.arg}")
            
            return ", ".join(args)
            
        except Exception:
            return "..."
    
    def generate_all_documentation(self) -> Dict[str, str]:
        """Generate all documentation files."""
        
        logger.info("📚 Generating comprehensive documentation...")
        
        docs_generated = {}
        
        try:
            # Generate API reference
            docs_generated['api_reference'] = self.generate_api_reference()
            
            # Generate installation guide
            docs_generated['installation_guide'] = self.generate_installation_guide()
            
            # Generate user manual
            docs_generated['user_manual'] = self.generate_user_manual()
            
            logger.info("✅ All documentation generated successfully")
            
        except Exception as e:
            logger.error(f"Error generating documentation: {e}")
        
        return docs_generated


def main():
    """Main function for testing documentation generation."""
    
    # Get project root
    project_root = Path(__file__).parent.parent.parent
    
    logger.info("📚 QA Wolf Documentation Generator")
    logger.info("🛡️ SAFETY: Documentation generation only, no code modification")
    
    # Initialize generator
    generator = DocumentationGenerator(str(project_root))
    
    # Generate all documentation
    docs = generator.generate_all_documentation()
    
    # Display results
    print("\n" + "="*60)
    print("📚 DOCUMENTATION GENERATION COMPLETE")
    print("="*60)
    
    for doc_type, file_path in docs.items():
        print(f"✅ {doc_type.replace('_', ' ').title()}: {file_path}")
    
    print("\n📁 All documentation saved to: qa_wolf_enhancements/docs/")
    print("✅ Documentation generation completed successfully!")
    
    return docs


if __name__ == "__main__":
    main()