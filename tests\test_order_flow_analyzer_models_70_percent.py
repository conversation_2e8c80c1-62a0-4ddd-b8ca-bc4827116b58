"""
Targeted tests to push order_flow_analyzer/models.py to 70%+ coverage.
"""

import pytest
from datetime import datetime, timezone
from src.forex_bot.order_flow_analyzer.models import (
    ImbalanceLevel, OrderFlowImbalance, LargeOrder, SupportResistanceLevel,
    OrderFlowSignal, OrderFlowContext
)


class TestOrderFlowImbalance:
    """Test OrderFlowImbalance model."""
    
    def test_order_flow_imbalance_creation_valid(self):
        """Test valid OrderFlowImbalance creation."""
        imbalance = OrderFlowImbalance(
            symbol="EURUSD",
            timestamp=datetime.now(timezone.utc),
            price_level=1.1000,
            bid_volume=100.0,
            ask_volume=50.0,
            imbalance_ratio=0.5,
            imbalance_level=ImbalanceLevel.MODERATE_BUY,
            is_significant=True,
            distance=0.001
        )
        
        assert imbalance.symbol == "EURUSD"
        assert imbalance.price_level == 1.1000
        assert imbalance.bid_volume == 100.0
        assert imbalance.ask_volume == 50.0
        assert imbalance.imbalance_ratio == 0.5
        assert imbalance.imbalance_level == ImbalanceLevel.MODERATE_BUY
        assert imbalance.is_significant is True
        assert imbalance.distance == 0.001    
    def test_order_flow_imbalance_invalid_ratio_high(self):
        """Test OrderFlowImbalance with invalid high ratio."""
        with pytest.raises(ValueError, match="Imbalance ratio must be between -1.0 and 1.0"):
            OrderFlowImbalance(
                symbol="EURUSD",
                timestamp=datetime.now(timezone.utc),
                price_level=1.1000,
                bid_volume=100.0,
                ask_volume=50.0,
                imbalance_ratio=1.5,  # Invalid: > 1.0
                imbalance_level=ImbalanceLevel.EXTREME_BUY,
                is_significant=True
            )
    
    def test_order_flow_imbalance_invalid_ratio_low(self):
        """Test OrderFlowImbalance with invalid low ratio."""
        with pytest.raises(ValueError, match="Imbalance ratio must be between -1.0 and 1.0"):
            OrderFlowImbalance(
                symbol="EURUSD",
                timestamp=datetime.now(timezone.utc),
                price_level=1.1000,
                bid_volume=100.0,
                ask_volume=50.0,
                imbalance_ratio=-1.5,  # Invalid: < -1.0
                imbalance_level=ImbalanceLevel.EXTREME_SELL,
                is_significant=True
            )


class TestLargeOrder:
    """Test LargeOrder model."""
    
    def test_large_order_creation_valid(self):
        """Test valid LargeOrder creation."""
        large_order = LargeOrder(
            symbol="EURUSD",
            timestamp=datetime.now(timezone.utc),
            price_level=1.1000,
            volume=1000.0,
            type="bid",
            is_market_moving=True,
            standard_deviations=2.5
        )
        
        assert large_order.symbol == "EURUSD"
        assert large_order.price_level == 1.1000
        assert large_order.volume == 1000.0
        assert large_order.type == "bid"
        assert large_order.is_market_moving is True
        assert large_order.standard_deviations == 2.5    
    def test_large_order_invalid_volume(self):
        """Test LargeOrder with invalid volume."""
        with pytest.raises(ValueError, match="Volume must be positive"):
            LargeOrder(
                symbol="EURUSD",
                timestamp=datetime.now(timezone.utc),
                price_level=1.1000,
                volume=-100.0,  # Invalid: negative volume
                type="bid",
                is_market_moving=True,
                standard_deviations=2.5
            )
    
    def test_large_order_invalid_standard_deviations(self):
        """Test LargeOrder with invalid standard deviations."""
        with pytest.raises(ValueError, match="Standard deviations must be positive"):
            LargeOrder(
                symbol="EURUSD",
                timestamp=datetime.now(timezone.utc),
                price_level=1.1000,
                volume=1000.0,
                type="bid",
                is_market_moving=True,
                standard_deviations=-1.0  # Invalid: negative
            )


class TestSupportResistanceLevel:
    """Test SupportResistanceLevel model."""
    
    def test_support_resistance_level_creation_valid(self):
        """Test valid SupportResistanceLevel creation."""
        level = SupportResistanceLevel(
            symbol="EURUSD",
            timestamp=datetime.now(timezone.utc),
            price_level=1.1000,
            type="support",
            strength=0.8,
            volume_concentration=500.0,
            is_active=True
        )
        
        assert level.symbol == "EURUSD"
        assert level.price_level == 1.1000
        assert level.type == "support"
        assert level.strength == 0.8
        assert level.volume_concentration == 500.0
        assert level.is_active is True    
    def test_support_resistance_level_invalid_strength_high(self):
        """Test SupportResistanceLevel with invalid high strength."""
        with pytest.raises(ValueError, match="Strength must be between 0.0 and 1.0"):
            SupportResistanceLevel(
                symbol="EURUSD",
                timestamp=datetime.now(timezone.utc),
                price_level=1.1000,
                type="support",
                strength=1.5,  # Invalid: > 1.0
                volume_concentration=500.0,
                is_active=True
            )
    
    def test_support_resistance_level_invalid_strength_low(self):
        """Test SupportResistanceLevel with invalid low strength."""
        with pytest.raises(ValueError, match="Strength must be between 0.0 and 1.0"):
            SupportResistanceLevel(
                symbol="EURUSD",
                timestamp=datetime.now(timezone.utc),
                price_level=1.1000,
                type="support",
                strength=-0.1,  # Invalid: < 0.0
                volume_concentration=500.0,
                is_active=True
            )
    
    def test_support_resistance_level_invalid_volume_concentration(self):
        """Test SupportResistanceLevel with invalid volume concentration."""
        with pytest.raises(ValueError, match="Volume concentration must be positive"):
            SupportResistanceLevel(
                symbol="EURUSD",
                timestamp=datetime.now(timezone.utc),
                price_level=1.1000,
                type="support",
                strength=0.8,
                volume_concentration=-100.0,  # Invalid: negative
                is_active=True
            )


class TestOrderFlowSignal:
    """Test OrderFlowSignal model."""
    
    def test_order_flow_signal_creation_valid(self):
        """Test valid OrderFlowSignal creation."""
        signal = OrderFlowSignal(
            symbol="EURUSD",
            timestamp=datetime.now(timezone.utc),
            signal_type="buy",
            confidence=0.85,
            price_level=1.1000,
            reason="Strong bid imbalance detected"
        )
        
        assert signal.symbol == "EURUSD"
        assert signal.signal_type == "buy"
        assert signal.confidence == 0.85
        assert signal.price_level == 1.1000
        assert signal.reason == "Strong bid imbalance detected"    
    def test_order_flow_signal_invalid_confidence_high(self):
        """Test OrderFlowSignal with invalid high confidence."""
        with pytest.raises(ValueError, match="Confidence must be between 0.0 and 1.0"):
            OrderFlowSignal(
                symbol="EURUSD",
                timestamp=datetime.now(timezone.utc),
                signal_type="buy",
                confidence=1.5,  # Invalid: > 1.0
                price_level=1.1000,
                reason="Strong bid imbalance detected"
            )
    
    def test_order_flow_signal_invalid_confidence_low(self):
        """Test OrderFlowSignal with invalid low confidence."""
        with pytest.raises(ValueError, match="Confidence must be between 0.0 and 1.0"):
            OrderFlowSignal(
                symbol="EURUSD",
                timestamp=datetime.now(timezone.utc),
                signal_type="buy",
                confidence=-0.1,  # Invalid: < 0.0
                price_level=1.1000,
                reason="Strong bid imbalance detected"
            )


class TestOrderFlowContext:
    """Test OrderFlowContext model."""
    
    def test_order_flow_context_creation_valid(self):
        """Test valid OrderFlowContext creation."""
        # Create sample data
        imbalance = OrderFlowImbalance(
            symbol="EURUSD",
            timestamp=datetime.now(timezone.utc),
            price_level=1.1000,
            bid_volume=100.0,
            ask_volume=50.0,
            imbalance_ratio=0.5,
            imbalance_level=ImbalanceLevel.MODERATE_BUY,
            is_significant=True
        )
        
        large_order = LargeOrder(
            symbol="EURUSD",
            timestamp=datetime.now(timezone.utc),
            price_level=1.1000,
            volume=1000.0,
            type="bid",
            is_market_moving=True,
            standard_deviations=2.5
        )
        
        level = SupportResistanceLevel(
            symbol="EURUSD",
            timestamp=datetime.now(timezone.utc),
            price_level=1.1000,
            type="support",
            strength=0.8,
            volume_concentration=500.0,
            is_active=True
        )        
        signal = OrderFlowSignal(
            symbol="EURUSD",
            timestamp=datetime.now(timezone.utc),
            signal_type="buy",
            confidence=0.85,
            price_level=1.1000,
            reason="Strong bid imbalance detected"
        )
        
        context = OrderFlowContext(
            symbol="EURUSD",
            timestamp=datetime.now(timezone.utc),
            imbalances=[imbalance],
            large_orders=[large_order],
            support_resistance_levels=[level],
            signals=[signal],
            overall_bias="bullish",
            confidence=0.75
        )
        
        assert context.symbol == "EURUSD"
        assert len(context.imbalances) == 1
        assert len(context.large_orders) == 1
        assert len(context.support_resistance_levels) == 1
        assert len(context.signals) == 1
        assert context.overall_bias == "bullish"
        assert context.confidence == 0.75
    
    def test_order_flow_context_invalid_confidence_high(self):
        """Test OrderFlowContext with invalid high confidence."""
        with pytest.raises(ValueError, match="Confidence must be between 0.0 and 1.0"):
            OrderFlowContext(
                symbol="EURUSD",
                timestamp=datetime.now(timezone.utc),
                imbalances=[],
                large_orders=[],
                support_resistance_levels=[],
                signals=[],
                overall_bias="neutral",
                confidence=1.5  # Invalid: > 1.0
            )
    
    def test_order_flow_context_invalid_confidence_low(self):
        """Test OrderFlowContext with invalid low confidence."""
        with pytest.raises(ValueError, match="Confidence must be between 0.0 and 1.0"):
            OrderFlowContext(
                symbol="EURUSD",
                timestamp=datetime.now(timezone.utc),
                imbalances=[],
                large_orders=[],
                support_resistance_levels=[],
                signals=[],
                overall_bias="neutral",
                confidence=-0.1  # Invalid: < 0.0
            )