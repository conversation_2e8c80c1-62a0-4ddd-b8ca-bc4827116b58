"""
Comprehensive tests to push order_book/models.py to 100% coverage.
"""

import pytest
from unittest.mock import patch
from datetime import datetime, timezone

class TestOrderBookModels100Percent:
    """Comprehensive tests to achieve 100% coverage for order_book/models.py."""

    def test_import_error_handling_pydantic(self):
        """Test pydantic import error handling."""
        from src.forex_bot.order_book import models
        assert hasattr(models, 'PYDANTIC_AVAILABLE')
        assert isinstance(models.PYDANTIC_AVAILABLE, bool)

    def test_order_book_properties(self):
        """Test OrderBook properties and methods."""
        from src.forex_bot.order_book.models import OrderBook, OrderBookEntry
        
        timestamp = datetime.now(timezone.utc)
        
        # Create test entries
        bids = [
            OrderBookEntry(price=1.1000, volume=100.0, type="bid"),
            OrderBookEntry(price=1.0999, volume=200.0, type="bid"),
            OrderBookEntry(price=1.0998, volume=150.0, type="bid")
        ]
        
        asks = [
            OrderBookEntry(price=1.1001, volume=120.0, type="ask"),
            OrderBookEntry(price=1.1002, volume=180.0, type="ask"),
            OrderBookEntry(price=1.1003, volume=160.0, type="ask")
        ]
        
        order_book = OrderBook(
            symbol="EURUSD",
            timestamp=timestamp,
            bids=bids,
            asks=asks,
            is_stale=False,
            source="MT5"
        )
        
        # Test basic properties
        assert order_book.symbol == "EURUSD"
        assert order_book.timestamp == timestamp
        assert len(order_book.bids) == 3
        assert len(order_book.asks) == 3
        
        # Test price properties
        assert order_book.bid_prices == [1.1000, 1.0999, 1.0998]
        assert order_book.ask_prices == [1.1001, 1.1002, 1.1003]
        assert order_book.best_bid == 1.1000
        assert order_book.best_ask == 1.1001
        assert abs(order_book.spread - 0.0001) < 1e-10  # Fix floating point precision
        assert abs(order_book.mid_price - 1.10005) < 1e-10
        
        # Test volume properties
        assert order_book.total_bid_volume == 450.0
        assert order_book.total_ask_volume == 460.0
        assert abs(order_book.imbalance_ratio - (-10.0 / 910.0)) < 1e-10