"""
Phase 5A comprehensive tests to push order_flow_analyzer/models.py to 90%+ coverage.
"""

import pytest
from datetime import datetime, timezone

class TestOrderFlowAnalyzerModelsPhase5Fixed:
    """Phase 5A tests to achieve 90%+ coverage for order_flow_analyzer/models.py."""

    def test_pydantic_order_flow_imbalance_comprehensive_validation(self):
        """Test all pydantic OrderFlowImbalance validation scenarios."""
        from src.forex_bot.order_flow_analyzer import models
        
        timestamp = datetime.now(timezone.utc)
        
        # Test successful creation with all enum values
        for level in [models.ImbalanceLevel.EXTREME_BUY, models.ImbalanceLevel.STRONG_BUY, 
                     models.ImbalanceLevel.MODERATE_BUY, models.ImbalanceLevel.NEUTRAL, 
                     models.ImbalanceLevel.MODERATE_SELL, models.ImbalanceLevel.STRONG_SELL]:
            imbalance = models.OrderFlowImbalance(
                symbol='EURUSD',
                timestamp=timestamp,
                price_level=1.1000,
                bid_volume=1000.0,
                ask_volume=500.0,
                imbalance_ratio=0.5,
                imbalance_level=level,
                is_significant=True,
                distance=0.001
            )
            assert imbalance.imbalance_level == level
        
        # Test boundary values for imbalance_ratio
        boundary_tests = [
            (-1.0, True),   # Valid boundary
            (1.0, True),    # Valid boundary  
            (0.0, True),    # Valid middle
            (-0.999, True), # Valid near boundary
            (0.999, True),  # Valid near boundary
            (-1.1, False),  # Invalid
            (1.1, False),   # Invalid
        ]
        
        for ratio, should_pass in boundary_tests:
            if should_pass:
                models.OrderFlowImbalance(
                    symbol='EUR', timestamp=timestamp, price_level=1.0,
                    bid_volume=100, ask_volume=100, imbalance_ratio=ratio,
                    imbalance_level=models.ImbalanceLevel.NEUTRAL, is_significant=True
                )
            else:
                with pytest.raises(ValueError, match="Imbalance ratio must be between -1.0 and 1.0"):
                    models.OrderFlowImbalance(
                        symbol='EUR', timestamp=timestamp, price_level=1.0,
                        bid_volume=100, ask_volume=100, imbalance_ratio=ratio,
                        imbalance_level=models.ImbalanceLevel.NEUTRAL, is_significant=True
                    )    def test_all_model_classes_comprehensive_validation(self):
        """Test all model classes with comprehensive validation scenarios."""
        from src.forex_bot.order_flow_analyzer import models
        
        timestamp = datetime.now(timezone.utc)
        
        # Test LargeOrder comprehensive validation
        # Valid cases
        for order_type in ['bid', 'ask']:
            large_order = models.LargeOrder(
                symbol='GBPUSD',
                timestamp=timestamp,
                price_level=1.3000,
                volume=5000.0,
                type=order_type,
                is_market_moving=True,
                standard_deviations=3.5
            )
            assert large_order.type == order_type
        
        # Test volume validation
        with pytest.raises(ValueError, match="Volume must be positive"):
            models.LargeOrder(
                symbol='GBP', timestamp=timestamp, price_level=1.0,
                volume=0.0, type='bid', is_market_moving=True, standard_deviations=2.0
            )
        
        with pytest.raises(ValueError, match="Standard deviations must be positive"):
            models.LargeOrder(
                symbol='GBP', timestamp=timestamp, price_level=1.0,
                volume=100.0, type='bid', is_market_moving=True, standard_deviations=0.0
            )
        
        with pytest.raises(ValueError, match='Type must be "bid" or "ask"'):
            models.LargeOrder(
                symbol='GBP', timestamp=timestamp, price_level=1.0,
                volume=100.0, type='invalid', is_market_moving=True, standard_deviations=2.0
            )
        
        # Test SupportResistanceLevel comprehensive validation
        for sr_type in ['support', 'resistance']:
            sr_level = models.SupportResistanceLevel(
                symbol='USDJPY',
                timestamp=timestamp,
                price_level=110.00,
                type=sr_type,
                strength=0.8,
                volume_concentration=1500.0,
                is_active=True
            )
            assert sr_level.type == sr_type
        
        # Test strength validation
        with pytest.raises(ValueError, match="Strength must be between 0.0 and 1.0"):
            models.SupportResistanceLevel(
                symbol='USD', timestamp=timestamp, price_level=110.0,
                type='support', strength=-0.1, volume_concentration=100.0, is_active=True
            )
        
        with pytest.raises(ValueError, match="Volume concentration must be positive"):
            models.SupportResistanceLevel(
                symbol='USD', timestamp=timestamp, price_level=110.0,
                type='support', strength=0.5, volume_concentration=0.0, is_active=True
            )
        
        with pytest.raises(ValueError, match='Type must be "support" or "resistance"'):
            models.SupportResistanceLevel(
                symbol='USD', timestamp=timestamp, price_level=110.0,
                type='invalid', strength=0.5, volume_concentration=100.0, is_active=True
            )    def test_order_flow_analysis_and_edge_cases(self):
        """Test OrderFlowAnalysis and all edge cases for maximum coverage."""
        from src.forex_bot.order_flow_analyzer import models
        
        timestamp = datetime.now(timezone.utc)
        
        # Create sample objects for testing
        imbalance = models.OrderFlowImbalance(
            symbol='TEST',
            timestamp=timestamp,
            price_level=1.0,
            bid_volume=100.0,
            ask_volume=50.0,
            imbalance_ratio=0.33,
            imbalance_level=models.ImbalanceLevel.MODERATE_BUY,
            is_significant=True
        )
        
        large_order = models.LargeOrder(
            symbol='TEST',
            timestamp=timestamp,
            price_level=1.0,
            volume=1000.0,
            type='bid',
            is_market_moving=True,
            standard_deviations=2.5
        )
        
        sr_level = models.SupportResistanceLevel(
            symbol='TEST',
            timestamp=timestamp,
            price_level=1.0,
            type='support',
            strength=0.8,
            volume_concentration=500.0,
            is_active=True
        )
        
        # Test OrderFlowAnalysis with all components
        analysis = models.OrderFlowAnalysis(
            symbol='EURUSD',
            timestamp=timestamp,
            imbalances=[imbalance],
            large_orders=[large_order],
            support_resistance_levels=[sr_level],
            overall_sentiment='bullish',
            confidence=0.85
        )
        
        assert analysis.symbol == 'EURUSD'
        assert analysis.confidence == 0.85
        assert len(analysis.imbalances) == 1
        assert len(analysis.large_orders) == 1
        assert len(analysis.support_resistance_levels) == 1
        
        # Test confidence validation
        with pytest.raises(ValueError, match="Confidence must be between 0.0 and 1.0"):
            models.OrderFlowAnalysis(
                symbol='TEST', timestamp=timestamp, imbalances=[], large_orders=[],
                support_resistance_levels=[], overall_sentiment='neutral', confidence=-0.1
            )
        
        with pytest.raises(ValueError, match="Confidence must be between 0.0 and 1.0"):
            models.OrderFlowAnalysis(
                symbol='TEST', timestamp=timestamp, imbalances=[], large_orders=[],
                support_resistance_levels=[], overall_sentiment='neutral', confidence=1.1
            )
        
        # Test with empty lists and boundary values
        empty_analysis = models.OrderFlowAnalysis(
            symbol='EMPTY',
            timestamp=timestamp,
            imbalances=[],
            large_orders=[],
            support_resistance_levels=[],
            overall_sentiment='neutral',
            confidence=0.0
        )
        
        assert len(empty_analysis.imbalances) == 0
        assert len(empty_analysis.large_orders) == 0
        assert len(empty_analysis.support_resistance_levels) == 0
        
        # Test maximum confidence
        max_analysis = models.OrderFlowAnalysis(
            symbol='MAX',
            timestamp=timestamp,
            imbalances=[],
            large_orders=[],
            support_resistance_levels=[],
            overall_sentiment='bullish',
            confidence=1.0
        )
        
        assert max_analysis.confidence == 1.0