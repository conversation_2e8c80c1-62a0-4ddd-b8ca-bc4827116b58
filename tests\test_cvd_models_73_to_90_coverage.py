"""
Test file to push cvd/models.py from 73% to 90%+ coverage.
Focuses on the remaining 13 missing lines: 39-55, 80, 90, 100, 110.
"""

import pytest
import pandas as pd
from datetime import datetime, timezone
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

import numpy as np
from forex_bot.cvd.models import CVDResult, CVDDivergence


class TestCVDResultMissingLines:
    """Tests to cover the missing lines in CVDResult class."""

    def test_to_dataframe_with_all_optional_data(self):
        """Test to_dataframe method with all optional data (covers lines 44-52)."""
        timestamps = np.array([
            datetime(2023, 1, 1, 12, 0, 0, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 12, 1, 0, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 12, 2, 0, tzinfo=timezone.utc)
        ])
        cvd_values = np.array([100.0, 150.0, 200.0])
        buying_volume = np.array([50.0, 75.0, 100.0])
        selling_volume = np.array([30.0, 45.0, 60.0])
        delta_volume = np.array([20.0, 30.0, 40.0])

        cvd_result = CVDResult(
            timestamps=timestamps,
            cvd_values=cvd_values,
            symbol="EURUSD",
            timeframe=1,
            buying_volume=buying_volume,
            selling_volume=selling_volume,
            delta_volume=delta_volume
        )

        df = cvd_result.to_dataframe()

        # Verify all columns are present
        assert 'timestamp' in df.columns
        assert 'cvd' in df.columns
        assert 'buying_volume' in df.columns
        assert 'selling_volume' in df.columns
        assert 'delta_volume' in df.columns

        # Verify data integrity
        assert len(df) == 3
        assert df['cvd'].tolist() == cvd_values.tolist()
        assert df['buying_volume'].tolist() == buying_volume.tolist()
        assert df['selling_volume'].tolist() == selling_volume.tolist()
        assert df['delta_volume'].tolist() == delta_volume.tolist()

    def test_to_dataframe_with_partial_optional_data(self):
        """Test to_dataframe method with partial optional data (covers lines 44-52)."""
        timestamps = np.array([
            datetime(2023, 1, 1, 12, 0, 0, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 12, 1, 0, tzinfo=timezone.utc)
        ])
        cvd_values = np.array([100.0, 150.0])
        buying_volume = np.array([50.0, 75.0])  # Only buying_volume provided

        cvd_result = CVDResult(
            timestamps=timestamps,
            cvd_values=cvd_values,
            symbol="EURUSD",
            timeframe=1,
            buying_volume=buying_volume,
            selling_volume=None,
            delta_volume=None
        )

        df = cvd_result.to_dataframe()

        # Verify only buying_volume is added
        assert 'timestamp' in df.columns
        assert 'cvd' in df.columns
        assert 'buying_volume' in df.columns
        assert 'selling_volume' not in df.columns
        assert 'delta_volume' not in df.columns

    def test_to_dataframe_with_mismatched_lengths(self):
        """Test to_dataframe method with mismatched optional data lengths (covers lines 44-52)."""
        timestamps = np.array([
            datetime(2023, 1, 1, 12, 0, 0, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 12, 1, 0, tzinfo=timezone.utc),
            datetime(2023, 1, 1, 12, 2, 0, tzinfo=timezone.utc)
        ])
        cvd_values = np.array([100.0, 150.0, 200.0])
        buying_volume = np.array([50.0, 75.0])  # Mismatched length (2 vs 3)
        selling_volume = np.array([30.0, 45.0, 60.0, 75.0])  # Mismatched length (4 vs 3)

        cvd_result = CVDResult(
            timestamps=timestamps,
            cvd_values=cvd_values,
            symbol="EURUSD",
            timeframe=1,
            buying_volume=buying_volume,
            selling_volume=selling_volume,
            delta_volume=None
        )

        df = cvd_result.to_dataframe()

        # Verify mismatched data is not added
        assert 'timestamp' in df.columns
        assert 'cvd' in df.columns
        assert 'buying_volume' not in df.columns  # Length mismatch
        assert 'selling_volume' not in df.columns  # Length mismatch
        assert 'delta_volume' not in df.columns


class TestCVDDivergenceMissingLines:
    """Tests to cover the missing lines in CVDDivergence class."""

    def test_is_bullish_property_true(self):
        """Test is_bullish property returns True for bullish divergence (covers line 80)."""
        divergence = CVDDivergence(
            start_time=pd.Timestamp('2023-01-01 12:00:00', tz='UTC'),
            end_time=pd.Timestamp('2023-01-01 12:05:00', tz='UTC'),
            start_price=1.1000,
            end_price=1.1050,
            start_cvd=100.0,
            end_cvd=80.0,
            divergence_type='bullish',
            strength=0.8
        )

        assert divergence.is_bullish is True
        assert divergence.is_bearish is False

    def test_is_bearish_property_true(self):
        """Test is_bearish property returns True for bearish divergence (covers line 90)."""
        divergence = CVDDivergence(
            start_time=pd.Timestamp('2023-01-01 12:00:00', tz='UTC'),
            end_time=pd.Timestamp('2023-01-01 12:05:00', tz='UTC'),
            start_price=1.1000,
            end_price=1.1050,
            start_cvd=100.0,
            end_cvd=120.0,
            divergence_type='bearish',
            strength=0.7
        )

        assert divergence.is_bearish is True
        assert divergence.is_bullish is False

    def test_price_change_property(self):
        """Test price_change property calculation (covers line 100)."""
        divergence = CVDDivergence(
            start_time=pd.Timestamp('2023-01-01 12:00:00', tz='UTC'),
            end_time=pd.Timestamp('2023-01-01 12:05:00', tz='UTC'),
            start_price=1.1000,
            end_price=1.1050,
            start_cvd=100.0,
            end_cvd=80.0,
            divergence_type='bullish',
            strength=0.8
        )

        expected_price_change = 1.1050 - 1.1000
        assert divergence.price_change == expected_price_change
        assert abs(divergence.price_change - 0.0050) < 1e-10

    def test_cvd_change_property(self):
        """Test cvd_change property calculation (covers line 110)."""
        divergence = CVDDivergence(
            start_time=pd.Timestamp('2023-01-01 12:00:00', tz='UTC'),
            end_time=pd.Timestamp('2023-01-01 12:05:00', tz='UTC'),
            start_price=1.1000,
            end_price=1.1050,
            start_cvd=100.0,
            end_cvd=80.0,
            divergence_type='bullish',
            strength=0.8
        )

        expected_cvd_change = 80.0 - 100.0
        assert divergence.cvd_change == expected_cvd_change
        assert divergence.cvd_change == -20.0

    def test_comprehensive_divergence_properties(self):
        """Test all properties together for comprehensive coverage."""
        # Test with negative price change and positive CVD change
        divergence = CVDDivergence(
            start_time=pd.Timestamp('2023-01-01 12:00:00', tz='UTC'),
            end_time=pd.Timestamp('2023-01-01 12:05:00', tz='UTC'),
            start_price=1.1050,
            end_price=1.1000,
            start_cvd=80.0,
            end_cvd=120.0,
            divergence_type='bearish',
            strength=0.9
        )

        # Test all properties
        assert divergence.is_bearish is True
        assert divergence.is_bullish is False
        assert abs(divergence.price_change - (-0.0050)) < 1e-10
        assert divergence.cvd_change == 40.0
