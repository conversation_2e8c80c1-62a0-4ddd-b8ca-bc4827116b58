"""
Phase 5B comprehensive tests to push correlation_matrix/models.py to 90%+ coverage.
"""

import pytest
import numpy as np
from datetime import datetime, timezone

class TestCorrelationMatrixModelsPhase5:
    """Phase 5B tests to achieve 90%+ coverage for correlation_matrix/models.py."""

    def test_comprehensive_correlation_config_validation(self):
        """Test comprehensive CorrelationConfig validation scenarios."""
        from src.forex_bot.correlation_matrix import models
        
        # Test successful creation with all parameters
        config = models.CorrelationConfig(
            symbols=['EURUSD', 'GBPUSD', 'USDJPY'],
            timeframe='1H',
            lookback_periods=100,
            min_periods=50,
            method='pearson'
        )
        assert config.symbols == ['EURUSD', 'GBPUSD', 'USDJPY']
        assert config.timeframe == '1H'
        assert config.lookback_periods == 100
        assert config.min_periods == 50
        assert config.method == 'pearson'
        
        # Test min_periods validation
        with pytest.raises(ValueError, match="min_periods must be positive"):
            models.CorrelationConfig(
                symbols=['EURUSD'], timeframe='1H', lookback_periods=100,
                min_periods=0, method='pearson'
            )
        
        with pytest.raises(ValueError, match="min_periods must be positive"):
            models.CorrelationConfig(
                symbols=['EURUSD'], timeframe='1H', lookback_periods=100,
                min_periods=-1, method='pearson'
            )
        
        # Test edge cases for min_periods
        valid_min_periods = [1, 10, 50, 100, 1000]
        for min_period in valid_min_periods:
            config = models.CorrelationConfig(
                symbols=['EURUSD'], timeframe='1H', lookback_periods=100,
                min_periods=min_period, method='pearson'
            )
            assert config.min_periods == min_period    def test_comprehensive_correlation_pair_validation(self):
        """Test comprehensive CorrelationPair validation scenarios."""
        from src.forex_bot.correlation_matrix import models
        
        timestamp = datetime.now(timezone.utc)
        
        # Test successful creation
        pair = models.CorrelationPair(
            symbol1='EURUSD',
            symbol2='GBPUSD',
            correlation=0.75,
            strength='strong',
            timestamp=timestamp
        )
        assert pair.symbol1 == 'EURUSD'
        assert pair.symbol2 == 'GBPUSD'
        assert pair.correlation == 0.75
        assert pair.strength == 'strong'
        
        # Test correlation boundary validation
        correlation_tests = [
            (-1.0, True),     # Valid boundary
            (1.0, True),      # Valid boundary
            (0.0, True),      # Valid middle
            (-0.999, True),   # Valid near boundary
            (0.999, True),    # Valid near boundary
            (-1.1, False),    # Invalid
            (1.1, False),     # Invalid
        ]
        
        for correlation, should_pass in correlation_tests:
            if should_pass:
                models.CorrelationPair(
                    symbol1='EUR', symbol2='GBP', correlation=correlation,
                    strength='weak', timestamp=timestamp
                )
            else:
                with pytest.raises(ValueError, match="Correlation must be between -1.0 and 1.0"):
                    models.CorrelationPair(
                        symbol1='EUR', symbol2='GBP', correlation=correlation,
                        strength='weak', timestamp=timestamp
                    )
        
        # Test strength boundary validation
        strength_tests = [
            (0.0, True),      # Valid boundary
            (1.0, True),      # Valid boundary
            (0.5, True),      # Valid middle
            (0.001, True),    # Valid small
            (0.999, True),    # Valid near boundary
            (-0.1, False),    # Invalid negative
            (1.1, False),     # Invalid over 1.0
        ]
        
        for strength, should_pass in strength_tests:
            if should_pass:
                models.CorrelationPair(
                    symbol1='EUR', symbol2='GBP', correlation=0.5,
                    strength=strength, timestamp=timestamp
                )
            else:
                with pytest.raises(ValueError, match="Strength must be between 0.0 and 1.0"):
                    models.CorrelationPair(
                        symbol1='EUR', symbol2='GBP', correlation=0.5,
                        strength=strength, timestamp=timestamp
                    )    def test_comprehensive_correlation_matrix_validation(self):
        """Test comprehensive CorrelationMatrix validation scenarios."""
        from src.forex_bot.correlation_matrix import models
        
        timestamp = datetime.now(timezone.utc)
        
        # Create valid matrix data
        symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
        matrix_data = np.array([
            [1.0, 0.8, -0.3],
            [0.8, 1.0, -0.2],
            [-0.3, -0.2, 1.0]
        ])
        
        # Test successful creation
        matrix = models.CorrelationMatrix(
            symbols=symbols,
            matrix=matrix_data,
            timestamp=timestamp,
            timeframe='1H'
        )
        assert matrix.symbols == symbols
        assert np.array_equal(matrix.matrix, matrix_data)
        assert matrix.timeframe == '1H'
        
        # Test matrix validation - must be square
        invalid_matrix = np.array([
            [1.0, 0.8],
            [0.8, 1.0],
            [-0.3, -0.2]  # Extra row makes it non-square
        ])
        
        with pytest.raises(ValueError, match="Matrix must be square"):
            models.CorrelationMatrix(
                symbols=symbols, matrix=invalid_matrix,
                timestamp=timestamp, timeframe='1H'
            )
        
        # Test matrix validation - diagonal must be 1.0
        invalid_diagonal = np.array([
            [0.9, 0.8, -0.3],  # Diagonal not 1.0
            [0.8, 1.0, -0.2],
            [-0.3, -0.2, 1.0]
        ])
        
        with pytest.raises(ValueError, match="Matrix diagonal must be 1.0"):
            models.CorrelationMatrix(
                symbols=symbols, matrix=invalid_diagonal,
                timestamp=timestamp, timeframe='1H'
            )
        
        # Test edge cases with different matrix sizes
        for size in [2, 3, 4, 5]:
            symbols_test = [f'SYM{i}' for i in range(size)]
            matrix_test = np.eye(size)  # Identity matrix
            
            matrix = models.CorrelationMatrix(
                symbols=symbols_test, matrix=matrix_test,
                timestamp=timestamp, timeframe='1H'
            )
            assert len(matrix.symbols) == size
            assert matrix.matrix.shape == (size, size)    def test_comprehensive_correlation_analysis_validation(self):
        """Test comprehensive CorrelationAnalysis validation scenarios."""
        from src.forex_bot.correlation_matrix import models
        
        timestamp = datetime.now(timezone.utc)
        
        # Create sample correlations
        correlations = [
            models.CorrelationPair(
                symbol1='EURUSD', symbol2='GBPUSD', correlation=0.8,
                strength=0.8, timestamp=timestamp
            ),
            models.CorrelationPair(
                symbol1='EURUSD', symbol2='USDJPY', correlation=-0.3,
                strength=0.3, timestamp=timestamp
            )
        ]
        
        # Create sample matrix
        symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
        matrix_data = np.eye(3)
        matrix = models.CorrelationMatrix(
            symbols=symbols, matrix=matrix_data,
            timestamp=timestamp, timeframe='1H'
        )
        
        # Test successful creation
        analysis = models.CorrelationAnalysis(
            correlations=correlations,
            matrix=matrix,
            timestamp=timestamp,
            summary='Strong positive correlation between EUR pairs'
        )
        
        assert len(analysis.correlations) == 2
        assert analysis.matrix == matrix
        assert analysis.summary == 'Strong positive correlation between EUR pairs'
        
        # Test with empty correlations
        empty_analysis = models.CorrelationAnalysis(
            correlations=[],
            matrix=matrix,
            timestamp=timestamp,
            summary='No significant correlations found'
        )
        
        assert len(empty_analysis.correlations) == 0
        assert empty_analysis.summary == 'No significant correlations found'
        
        # Test with maximum correlations
        max_correlations = []
        for i in range(10):
            max_correlations.append(
                models.CorrelationPair(
                    symbol1=f'SYM{i}', symbol2=f'SYM{i+1}', correlation=0.5,
                    strength=0.5, timestamp=timestamp
                )
            )
        
        max_analysis = models.CorrelationAnalysis(
            correlations=max_correlations,
            matrix=matrix,
            timestamp=timestamp,
            summary='Multiple correlations detected'
        )
        
        assert len(max_analysis.correlations) == 10