"""
Test file to push global_pmi/models.py from 73% to 90%+ coverage.
Focuses on the remaining 22 missing lines: 35-40, 86-99, 136, 146, 156-161.
"""

import pytest
from datetime import date
import sys
import os

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from forex_bot.global_pmi.models import PMIData, PMITrend, PMIDivergence


class TestPMIDataMissingLines:
    """Tests to cover the missing lines in PMIData class."""

    def test_post_init_expansion_flags(self):
        """Test __post_init__ method setting expansion/contraction flags (covers lines 35-36)."""
        # Test expansion case (PMI > 50)
        pmi_expansion = PMIData(
            country="US",
            pmi_type="Manufacturing",
            date=date(2023, 1, 1),
            pmi_value=55.0
        )

        assert pmi_expansion.is_expansion is True
        assert pmi_expansion.is_contraction is False

    def test_post_init_contraction_flags(self):
        """Test __post_init__ method setting contraction flags (covers lines 35-36)."""
        # Test contraction case (PMI < 50)
        pmi_contraction = PMIData(
            country="US",
            pmi_type="Manufacturing",
            date=date(2023, 1, 1),
            pmi_value=45.0
        )

        assert pmi_contraction.is_expansion is False
        assert pmi_contraction.is_contraction is True

    def test_post_init_neutral_flags(self):
        """Test __post_init__ method with neutral PMI value (covers lines 35-36)."""
        # Test neutral case (PMI = 50)
        pmi_neutral = PMIData(
            country="US",
            pmi_type="Manufacturing",
            date=date(2023, 1, 1),
            pmi_value=50.0
        )

        assert pmi_neutral.is_expansion is False
        assert pmi_neutral.is_contraction is False

    def test_post_init_change_calculation(self):
        """Test __post_init__ method calculating change when previous_value provided (covers lines 39-40)."""
        # Test change calculation when previous_value is provided and change is None
        pmi_with_previous = PMIData(
            country="US",
            pmi_type="Manufacturing",
            date=date(2023, 1, 1),
            pmi_value=55.0,
            previous_value=52.0,
            change=None  # This should trigger calculation
        )

        assert pmi_with_previous.change == 3.0  # 55.0 - 52.0

    def test_post_init_no_change_calculation_when_change_provided(self):
        """Test __post_init__ method not overriding existing change value (covers lines 39-40)."""
        # Test that existing change value is not overridden
        pmi_with_change = PMIData(
            country="US",
            pmi_type="Manufacturing",
            date=date(2023, 1, 1),
            pmi_value=55.0,
            previous_value=52.0,
            change=2.5  # This should not be overridden
        )

        assert pmi_with_change.change == 2.5  # Should remain as provided

    def test_post_init_no_change_calculation_when_no_previous(self):
        """Test __post_init__ method when no previous_value provided (covers lines 39-40)."""
        # Test that change is not calculated when previous_value is None
        pmi_no_previous = PMIData(
            country="US",
            pmi_type="Manufacturing",
            date=date(2023, 1, 1),
            pmi_value=55.0,
            previous_value=None,
            change=None
        )

        assert pmi_no_previous.change is None


class TestPMITrendMissingLines:
    """Tests to cover the missing lines in PMITrend class."""

    def test_economic_state_strongly_expanding(self):
        """Test economic_state property for strongly expanding case (covers lines 86-87)."""
        trend = PMITrend(
            country="US",
            pmi_type="Manufacturing",
            latest_date=date(2023, 1, 1),
            latest_value=55.0,
            trend_direction='up',
            trend_strength=0.8,
            values_3m=[53.0, 54.0, 55.0],
            values_6m=[50.0, 51.0, 52.0, 53.0, 54.0, 55.0],
            values_12m=[48.0, 49.0, 50.0, 51.0, 52.0, 53.0, 54.0, 55.0, 52.0, 53.0, 54.0, 55.0],
            avg_3m=54.0,
            avg_6m=52.5,
            avg_12m=52.0,
            is_improving=True,
            is_deteriorating=False,
            is_above_3m_avg=True,
            is_above_6m_avg=True,
            is_above_12m_avg=True
        )

        assert trend.economic_state == "Strongly Expanding"

    def test_economic_state_moderately_expanding(self):
        """Test economic_state property for moderately expanding case (covers lines 88-89)."""
        trend = PMITrend(
            country="US",
            pmi_type="Manufacturing",
            latest_date=date(2023, 1, 1),
            latest_value=52.0,
            trend_direction='down',
            trend_strength=0.6,
            values_3m=[54.0, 53.0, 52.0],
            values_6m=[55.0, 54.0, 53.0, 54.0, 53.0, 52.0],
            values_12m=[56.0, 55.0, 54.0, 53.0, 54.0, 55.0, 54.0, 53.0, 54.0, 53.0, 52.0, 52.0],
            avg_3m=53.0,
            avg_6m=53.5,
            avg_12m=53.7,
            is_improving=False,
            is_deteriorating=True,
            is_above_3m_avg=False,
            is_above_6m_avg=False,
            is_above_12m_avg=False
        )

        assert trend.economic_state == "Moderately Expanding"

    def test_economic_state_stable_expansion(self):
        """Test economic_state property for stable expansion case (covers lines 90-91)."""
        trend = PMITrend(
            country="US",
            pmi_type="Manufacturing",
            latest_date=date(2023, 1, 1),
            latest_value=51.0,
            trend_direction='stable',
            trend_strength=0.3,
            values_3m=[51.0, 51.0, 51.0],
            values_6m=[51.0, 51.0, 51.0, 51.0, 51.0, 51.0],
            values_12m=[51.0, 51.0, 51.0, 51.0, 51.0, 51.0, 51.0, 51.0, 51.0, 51.0, 51.0, 51.0],
            avg_3m=51.0,
            avg_6m=51.0,
            avg_12m=51.0,
            is_improving=False,
            is_deteriorating=False,
            is_above_3m_avg=False,
            is_above_6m_avg=False,
            is_above_12m_avg=False
        )

        assert trend.economic_state == "Stable Expansion"

    def test_economic_state_improving_contraction(self):
        """Test economic_state property for improving contraction case (covers lines 93-94)."""
        trend = PMITrend(
            country="US",
            pmi_type="Manufacturing",
            latest_date=date(2023, 1, 1),
            latest_value=48.0,
            trend_direction='up',
            trend_strength=0.7,
            values_3m=[46.0, 47.0, 48.0],
            values_6m=[44.0, 45.0, 46.0, 46.0, 47.0, 48.0],
            values_12m=[42.0, 43.0, 44.0, 45.0, 46.0, 45.0, 46.0, 46.0, 47.0, 47.0, 48.0, 48.0],
            avg_3m=47.0,
            avg_6m=46.0,
            avg_12m=45.4,
            is_improving=True,
            is_deteriorating=False,
            is_above_3m_avg=True,
            is_above_6m_avg=True,
            is_above_12m_avg=True
        )

        assert trend.economic_state == "Improving Contraction"

    def test_economic_state_deepening_contraction(self):
        """Test economic_state property for deepening contraction case (covers lines 95-96)."""
        trend = PMITrend(
            country="US",
            pmi_type="Manufacturing",
            latest_date=date(2023, 1, 1),
            latest_value=45.0,
            trend_direction='down',
            trend_strength=0.9,
            values_3m=[47.0, 46.0, 45.0],
            values_6m=[49.0, 48.0, 47.0, 47.0, 46.0, 45.0],
            values_12m=[51.0, 50.0, 49.0, 48.0, 47.0, 48.0, 47.0, 47.0, 46.0, 46.0, 45.0, 45.0],
            avg_3m=46.0,
            avg_6m=47.0,
            avg_12m=47.4,
            is_improving=False,
            is_deteriorating=True,
            is_above_3m_avg=False,
            is_above_6m_avg=False,
            is_above_12m_avg=False
        )

        assert trend.economic_state == "Deepening Contraction"

    def test_economic_state_stable_contraction(self):
        """Test economic_state property for stable contraction case (covers lines 97-98)."""
        trend = PMITrend(
            country="US",
            pmi_type="Manufacturing",
            latest_date=date(2023, 1, 1),
            latest_value=47.0,
            trend_direction='stable',
            trend_strength=0.2,
            values_3m=[47.0, 47.0, 47.0],
            values_6m=[47.0, 47.0, 47.0, 47.0, 47.0, 47.0],
            values_12m=[47.0, 47.0, 47.0, 47.0, 47.0, 47.0, 47.0, 47.0, 47.0, 47.0, 47.0, 47.0],
            avg_3m=47.0,
            avg_6m=47.0,
            avg_12m=47.0,
            is_improving=False,
            is_deteriorating=False,
            is_above_3m_avg=False,
            is_above_6m_avg=False,
            is_above_12m_avg=False
        )

        assert trend.economic_state == "Stable Contraction"


class TestPMIDivergenceMissingLines:
    """Tests to cover the missing lines in PMIDivergence class."""

    def test_is_positive_divergence_true(self):
        """Test is_positive_divergence property returns True (covers line 136)."""
        divergence = PMIDivergence(
            country="US",
            currency="USD",
            start_date=date(2023, 1, 1),
            end_date=date(2023, 2, 1),
            start_pmi=52.0,
            end_pmi=54.5,
            pmi_change=2.5,
            start_currency_value=1.0800,
            end_currency_value=1.0680,
            currency_change=-1.2,
            divergence_type='positive',
            strength=0.8
        )

        assert divergence.is_positive_divergence is True
        assert divergence.is_negative_divergence is False

    def test_is_negative_divergence_true(self):
        """Test is_negative_divergence property returns True (covers line 146)."""
        divergence = PMIDivergence(
            country="US",
            currency="USD",
            start_date=date(2023, 1, 1),
            end_date=date(2023, 2, 1),
            start_pmi=54.0,
            end_pmi=52.5,
            pmi_change=-1.5,
            start_currency_value=1.0800,
            end_currency_value=1.1000,
            currency_change=2.0,
            divergence_type='negative',
            strength=0.7
        )

        assert divergence.is_negative_divergence is True
        assert divergence.is_positive_divergence is False

    def test_trading_signal_buy(self):
        """Test trading_signal property returns BUY (covers lines 156-157)."""
        divergence = PMIDivergence(
            country="US",
            currency="USD",
            start_date=date(2023, 1, 1),
            end_date=date(2023, 2, 1),
            start_pmi=52.0,
            end_pmi=55.0,
            pmi_change=3.0,
            start_currency_value=1.0800,
            end_currency_value=1.0600,
            currency_change=-2.0,
            divergence_type='positive',
            strength=0.8  # > 0.5
        )

        assert divergence.trading_signal == "BUY"

    def test_trading_signal_sell(self):
        """Test trading_signal property returns SELL (covers lines 158-159)."""
        divergence = PMIDivergence(
            country="US",
            currency="USD",
            start_date=date(2023, 1, 1),
            end_date=date(2023, 2, 1),
            start_pmi=55.0,
            end_pmi=52.5,
            pmi_change=-2.5,
            start_currency_value=1.0800,
            end_currency_value=1.0980,
            currency_change=1.8,
            divergence_type='negative',
            strength=0.9  # > 0.5
        )

        assert divergence.trading_signal == "SELL"

    def test_trading_signal_none_weak_strength(self):
        """Test trading_signal property returns None for weak strength (covers lines 160-161)."""
        divergence = PMIDivergence(
            country="US",
            currency="USD",
            start_date=date(2023, 1, 1),
            end_date=date(2023, 2, 1),
            start_pmi=52.0,
            end_pmi=53.0,
            pmi_change=1.0,
            start_currency_value=1.0800,
            end_currency_value=1.0750,
            currency_change=-0.5,
            divergence_type='positive',
            strength=0.3  # <= 0.5
        )

        assert divergence.trading_signal is None

    def test_trading_signal_none_negative_weak_strength(self):
        """Test trading_signal property returns None for negative divergence with weak strength (covers lines 160-161)."""
        divergence = PMIDivergence(
            country="US",
            currency="USD",
            start_date=date(2023, 1, 1),
            end_date=date(2023, 2, 1),
            start_pmi=53.0,
            end_pmi=52.0,
            pmi_change=-1.0,
            start_currency_value=1.0800,
            end_currency_value=1.0850,
            currency_change=0.5,
            divergence_type='negative',
            strength=0.4  # <= 0.5
        )

        assert divergence.trading_signal is None
