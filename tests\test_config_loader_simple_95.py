"""
Simple targeted tests to push config_loader.py to 95%+ coverage.

Focus on testing the Config class methods that contain the missing lines.
"""

import pytest
import os
from unittest.mock import patch, MagicMock

from src.forex_bot.config_loader import Config


class TestConfigLoaderSimple95:
    """Simple targeted tests to achieve 95%+ coverage for config_loader.py."""

    def test_config_calculate_paths_name_error(self, capsys):
        """Test NameError handling in _calculate_paths method."""
        config = Config()
        
        # Mock globals() to not contain '__file__'
        with patch('src.forex_bot.config_loader.globals', return_value={}):
            with patch('src.forex_bot.config_loader.os.getcwd', return_value='/test/fallback'):
                with patch('src.forex_bot.config_loader.os.path.exists', return_value=True):
                    with patch('src.forex_bot.config_loader.os.path.abspath', side_effect=lambda x: x):
                        # Call the method that should trigger the NameError handling
                        config._calculate_paths()
                        
                        # Verify paths were set
                        assert hasattr(config, 'knowledge_base_strategy_file_path')
                        assert hasattr(config, 'knowledge_base_metrics_file_path')

    def test_config_calculate_paths_project_marker_not_found_loop_exhaustion(self, capsys):
        """Test project marker not found when loop is exhausted."""
        config = Config()
        
        # Mock to never find project markers and exhaust the loop
        with patch('src.forex_bot.config_loader.os.path.exists', return_value=False):
            with patch('src.forex_bot.config_loader.os.getcwd', return_value='/fallback/path'):
                with patch('src.forex_bot.config_loader.os.path.dirname') as mock_dirname:
                    with patch('src.forex_bot.config_loader.os.path.abspath', side_effect=lambda x: x):
                        # Set up dirname to simulate going up directories
                        mock_dirname.side_effect = ['/test', '/test', '/test', '/test', '/']
                        
                        # Call the method
                        config._calculate_paths()
                        
                        # Check for the warning message
                        captured = capsys.readouterr()
                        assert "[WARN] KB Path: Project marker not found" in captured.out

    def test_config_calculate_paths_parent_equals_current(self, capsys):
        """Test when parent_dir equals project_root (filesystem root reached)."""
        config = Config()
        
        # Mock to never find project markers
        with patch('src.forex_bot.config_loader.os.path.exists', return_value=False):
            with patch('src.forex_bot.config_loader.os.getcwd', return_value='/root/fallback'):
                with patch('src.forex_bot.config_loader.os.path.dirname', return_value='/'):
                    with patch('src.forex_bot.config_loader.os.path.abspath', side_effect=lambda x: x):
                        # Mock globals to contain __file__
                        with patch('src.forex_bot.config_loader.globals', return_value={'__file__': '/test'}):
                            # Call the method
                            config._calculate_paths()
                            
                            # Check for the specific warning message
                            captured = capsys.readouterr()
                            assert "[WARN] KB Path: Could not find project marker" in captured.out

    def test_config_get_float_invalid_value(self, capsys):
        """Test _get_float with invalid float value."""
        config = Config()
        
        # Test with invalid float value
        with patch('src.forex_bot.config_loader.os.getenv', return_value='invalid_float'):
            result = config._get_float('TEST_KEY', 1.5)
            
            # Should return default value
            assert result == 1.5
            
            # Should print warning
            captured = capsys.readouterr()
            assert "[WARNING] Invalid float value for TEST_KEY" in captured.out

    def test_config_get_float_none_value(self):
        """Test _get_float with None value."""
        config = Config()
        
        # Test with None value (environment variable not set)
        with patch('src.forex_bot.config_loader.os.getenv', return_value=None):
            result = config._get_float('MISSING_KEY', 2.5)
            
            # Should return default value
            assert result == 2.5

    def test_config_get_float_valid_value(self):
        """Test _get_float with valid float value."""
        config = Config()
        
        # Test with valid float value
        with patch('src.forex_bot.config_loader.os.getenv', return_value='3.14'):
            result = config._get_float('VALID_KEY', 1.0)
            
            # Should return parsed float value
            assert result == 3.14

    def test_config_init_calls_calculate_paths(self):
        """Test that Config.__init__ calls _calculate_paths."""
        with patch.object(Config, '_calculate_paths') as mock_calculate:
            with patch.object(Config, '_check_dependencies'):
                config = Config()
                
                # Verify _calculate_paths was called
                mock_calculate.assert_called_once()

    def test_config_check_dependencies_method(self):
        """Test the _check_dependencies method."""
        config = Config()
        
        # This method should run without errors
        config._check_dependencies()
        
        # Verify that dependency flags are accessible
        assert hasattr(config, 'rclone_exe_available')
        assert hasattr(config, 'qdrant_client_available')

    def test_config_edge_case_empty_kb_files(self):
        """Test Config with empty knowledge base file names."""
        # Test with empty kb file names
        with patch.object(Config, 'kb_strategy_file', ''):
            with patch.object(Config, 'kb_metrics_file', ''):
                with patch('src.forex_bot.config_loader.os.path.abspath', side_effect=lambda x: x):
                    config = Config()
                    
                    # Should still create paths (even if empty)
                    assert hasattr(config, 'knowledge_base_strategy_file_path')
                    assert hasattr(config, 'knowledge_base_metrics_file_path')

    def test_config_various_path_scenarios(self):
        """Test Config with various path scenarios."""
        test_scenarios = [
            {
                'name': 'deep_nested_path',
                'start_path': '/very/deep/nested/path',
                'find_marker': False,
                'expected_fallback': True
            },
            {
                'name': 'root_path',
                'start_path': '/',
                'find_marker': False,
                'expected_fallback': True
            },
            {
                'name': 'relative_path',
                'start_path': 'relative/path',
                'find_marker': False,
                'expected_fallback': True
            }
        ]
        
        for scenario in test_scenarios:
            with patch('src.forex_bot.config_loader.os.path.exists', return_value=scenario['find_marker']):
                with patch('src.forex_bot.config_loader.os.getcwd', return_value='/test/cwd'):
                    with patch('src.forex_bot.config_loader.os.path.abspath', side_effect=lambda x: x):
                        with patch('src.forex_bot.config_loader.globals', return_value={'__file__': scenario['start_path']}):
                            config = Config()
                            
                            # Should complete without errors
                            assert hasattr(config, 'knowledge_base_strategy_file_path')
                            assert hasattr(config, 'knowledge_base_metrics_file_path')