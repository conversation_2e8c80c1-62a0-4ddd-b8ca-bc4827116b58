"""
Phase 6A: Simple test for correlation_matrix/models.py to push from 61% to 90%+ coverage.
"""

import pytest
from datetime import datetime, timezone
from unittest.mock import patch
from src.forex_bot.correlation_matrix.models import (
    TimeWindow, CorrelationMethod, CorrelationStrength,
    CorrelationSettings, CorrelationPair
)


class TestCorrelationMatrixModelsPhase6ASimple:
    """Simple test to push correlation_matrix/models.py to 90%+ coverage"""

    def test_validator_edge_cases(self):
        """Test validator edge cases and error conditions"""
        
        now = datetime.now(timezone.utc)
        
        # Test CorrelationSettings min_periods validator
        with pytest.raises(ValueError, match="min_periods must be at least 2"):
            CorrelationSettings(min_periods=1)
        
        # Test CorrelationPair correlation validator
        with pytest.raises(ValueError, match="correlation must be between -1.0 and 1.0"):
            CorrelationPair(
                symbol1="EURUSD",
                symbol2="GBPUSD",
                correlation=1.5,  # Invalid correlation
                strength=CorrelationStrength.STRONG_POSITIVE,
                timestamp=now,
                time_window=TimeWindow.DAY_1,
                method=CorrelationMethod.PEARSON
            )
        
        # Test CorrelationPair strength validator
        with pytest.raises(ValueError, match="strength should be"):
            CorrelationPair(
                symbol1="EURUSD",
                symbol2="GBPUSD",
                correlation=0.75,  # Strong positive
                strength=CorrelationStrength.WEAK_POSITIVE,  # Wrong strength
                timestamp=now,
                time_window=TimeWindow.DAY_1,
                method=CorrelationMethod.PEARSON
            )

    def test_pydantic_fallback_basic(self):
        """Test basic pydantic fallback functionality"""
        
        # Test with pydantic unavailable to trigger fallback classes
        with patch('src.forex_bot.correlation_matrix.models.PYDANTIC_AVAILABLE', False):
            import importlib
            import src.forex_bot.correlation_matrix.models as models_module
            importlib.reload(models_module)
            
            # Test fallback CorrelationSettings
            settings = models_module.CorrelationSettings(
                time_window=models_module.TimeWindow.HOUR_4,
                method=models_module.CorrelationMethod.SPEARMAN,
                min_periods=50
            )
            assert settings.time_window == models_module.TimeWindow.HOUR_4
            assert settings.min_periods == 50