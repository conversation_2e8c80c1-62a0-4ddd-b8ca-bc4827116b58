"""
Phase 5B comprehensive tests to push correlation_matrix/models.py to 90%+ coverage.
"""

import pytest
from datetime import datetime, timezone

class TestCorrelationMatrixModelsPhase5Final:
    """Phase 5B tests to achieve 90%+ coverage for correlation_matrix/models.py."""

    def test_comprehensive_model_validation(self):
        """Test comprehensive validation for all correlation matrix model classes."""
        from src.forex_bot.correlation_matrix import models
        
        timestamp = datetime.now(timezone.utc)
        
        # Test CorrelationSettings validation
        settings = models.CorrelationSettings(
            time_window=models.TimeWindow.DAY_1,
            method=models.CorrelationMethod.PEARSON,
            min_periods=30,
            symbols=['EURUSD', 'GBPUSD'],
            include_base_pairs=True
        )
        assert settings.min_periods == 30
        assert len(settings.symbols) == 2
        
        # Test min_periods validation
        with pytest.raises(ValueError, match="min_periods must be at least 2"):
            models.CorrelationSettings(min_periods=1)
        
        # Test CorrelationPair validation
        pair = models.CorrelationPair(
            symbol1='EURUSD',
            symbol2='GBPUSD',
            correlation=0.8,
            strength=models.CorrelationStrength.STRONG_POSITIVE,
            timestamp=timestamp,
            time_window=models.TimeWindow.DAY_1,
            method=models.CorrelationMethod.PEARSON
        )
        assert pair.correlation == 0.8
        assert pair.strength == models.CorrelationStrength.STRONG_POSITIVE
        
        # Test correlation boundary validation
        with pytest.raises(ValueError, match="correlation must be between -1.0 and 1.0"):
            models.CorrelationPair(
                symbol1='EUR', symbol2='GBP', correlation=1.1,
                strength=models.CorrelationStrength.STRONG_POSITIVE,
                timestamp=timestamp, time_window=models.TimeWindow.DAY_1,
                method=models.CorrelationMethod.PEARSON
            )        
        # Test CorrelationMatrix validation
        matrix_data = {
            'EURUSD': {'EURUSD': 1.0, 'GBPUSD': 0.8},
            'GBPUSD': {'EURUSD': 0.8, 'GBPUSD': 1.0}
        }
        
        matrix = models.CorrelationMatrix(
            timestamp=timestamp,
            time_window=models.TimeWindow.DAY_1,
            method=models.CorrelationMethod.PEARSON,
            symbols=['EURUSD', 'GBPUSD'],
            matrix=matrix_data,
            pairs=[pair]
        )
        assert len(matrix.symbols) == 2
        assert matrix.matrix == matrix_data
        
        # Test CorrelationTrend validation
        trend = models.CorrelationTrend(
            symbol1='EURUSD',
            symbol2='GBPUSD',
            timestamps=[timestamp],
            correlations=[0.8],
            time_window=models.TimeWindow.DAY_1,
            method=models.CorrelationMethod.PEARSON
        )
        assert len(trend.correlations) == 1
        assert trend.correlations[0] == 0.8
        
        # Test correlations length validation
        with pytest.raises(ValueError, match="correlations and timestamps must have the same length"):
            models.CorrelationTrend(
                symbol1='EUR', symbol2='GBP',
                timestamps=[timestamp], correlations=[0.8, 0.9],
                time_window=models.TimeWindow.DAY_1, method=models.CorrelationMethod.PEARSON
            )
        
        # Test CorrelationAlert validation
        alert = models.CorrelationAlert(
            symbol1='EURUSD',
            symbol2='GBPUSD',
            old_correlation=0.5,
            new_correlation=0.8,
            change=0.3,
            timestamp=timestamp,
            time_window=models.TimeWindow.DAY_1,
            method=models.CorrelationMethod.PEARSON,
            is_significant=True
        )
        assert alert.change == 0.3
        assert alert.is_significant is True