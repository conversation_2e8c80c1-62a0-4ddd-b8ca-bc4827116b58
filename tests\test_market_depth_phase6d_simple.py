"""
Phase 6D: Simple test for market_depth_visualizer/models.py to push from 59% to 90%+ coverage.
"""

import pytest
from datetime import datetime, timezone
from unittest.mock import patch
from src.forex_bot.market_depth_visualizer.models import (
    MarketDepthSnapshot, MarketDepthVisualization, MarketDepthDashboard,
    DashboardSettings, VisualizationType
)


class TestMarketDepthPhase6DSimple:
    """Simple test to push market_depth_visualizer/models.py to 90%+ coverage"""

    def test_remaining_validator_edge_cases(self):
        """Test remaining validator edge cases"""
        
        now = datetime.now(timezone.utc)
        
        # Test TradeEntry volume validator
        from src.forex_bot.market_depth_visualizer.models import TradeEntry
        with pytest.raises(ValueError):
            TradeEntry(
                timestamp=now,
                price=1.2340,
                volume=0.0,  # Zero volume
                direction="buy"
            )
        
        # Test bid_volumes length validator
        with pytest.raises(ValueError):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340, 1.2339, 1.2338],
                bid_volumes=[1000.0, 1500.0],  # Different length
                ask_prices=[1.2341],
                ask_volumes=[800.0]
            )
        
        # Test ask_volumes length validator
        with pytest.raises(ValueError):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340],
                bid_volumes=[1000.0],
                ask_prices=[1.2341, 1.2342, 1.2343],
                ask_volumes=[800.0, 1200.0]  # Different length
            )    def test_property_edge_cases(self):
        """Test property edge cases"""
        
        now = datetime.now(timezone.utc)
        
        # Test properties with single values
        snapshot_single = MarketDepthSnapshot(
            symbol="EURUSD",
            timestamp=now,
            bid_prices=[1.2340],
            bid_volumes=[1000.0],
            ask_prices=[1.2341],
            ask_volumes=[800.0]
        )
        
        # Test best_bid property
        assert snapshot_single.best_bid == 1.2340
        
        # Test best_ask property
        assert snapshot_single.best_ask == 1.2341
        
        # Test spread calculation
        expected_spread = 1.2341 - 1.2340
        assert abs(snapshot_single.spread - expected_spread) < 1e-10
        
        # Test mid_price calculation
        expected_mid = (1.2340 + 1.2341) / 2
        assert abs(snapshot_single.mid_price - expected_mid) < 1e-10
        
        # Test total volumes
        assert snapshot_single.total_bid_volume == 1000.0
        assert snapshot_single.total_ask_volume == 800.0
        
        # Test imbalance ratio with zero total volume
        snapshot_zero = MarketDepthSnapshot(
            symbol="GBPUSD",
            timestamp=now,
            bid_prices=[1.3000],
            bid_volumes=[0.0],  # Zero volume
            ask_prices=[1.3001],
            ask_volumes=[0.0]   # Zero volume
        )
        # Should return None for zero total volume
        assert snapshot_zero.imbalance_ratio is None