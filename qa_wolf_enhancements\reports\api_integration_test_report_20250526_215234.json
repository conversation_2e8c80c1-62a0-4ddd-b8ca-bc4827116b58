{"timestamp": "2025-05-27T02:52:34.914353+00:00", "test_suite": "QA Wolf API Integration Tests", "total_duration_ms": 7963.659048080444, "summary": {"total_tests": 5, "passed_tests": 5, "failed_tests": 0, "success_rate_percent": 100.0, "overall_grade": "EXCELLENT"}, "test_results": [{"test_name": "MT5 Connection Reliability", "timestamp": "2025-05-27T02:52:26.950722+00:00", "status": "PASSED", "metrics": {"average_connection_time_ms": 120.37830352783203, "max_connection_time_ms": 147.57847785949707, "min_connection_time_ms": 105.13186454772949, "connection_success_rate": 100.0, "performance_grade": "EXCELLENT"}, "details": [{"attempt": 1, "connection_time_ms": 147.57847785949707, "success": true}, {"attempt": 2, "connection_time_ms": 105.13186454772949, "success": true}, {"attempt": 3, "connection_time_ms": 110.**************, "success": true}, {"attempt": 4, "connection_time_ms": 116.**************, "success": true}, {"attempt": 5, "connection_time_ms": 122.**************, "success": true}]}, {"test_name": "Broker API Response Times", "timestamp": "2025-05-27T02:52:27.552836+00:00", "status": "PASSED", "endpoints_tested": [{"endpoint": "Market Data", "response_time_ms": 61.***************, "expected_ms": 100, "success": true, "performance": "GOOD"}, {"endpoint": "Account Info", "response_time_ms": 112.*************, "expected_ms": 200, "success": true, "performance": "GOOD"}, {"endpoint": "Symbol Info", "response_time_ms": 95.**************, "expected_ms": 150, "success": true, "performance": "GOOD"}, {"endpoint": "Historical Data", "response_time_ms": 216.**************, "expected_ms": 500, "success": true, "performance": "GOOD"}], "metrics": {"success_rate_percent": 100.0, "average_response_time_ms": 121.**************, "endpoints_tested": 4, "successful_requests": 4, "overall_grade": "EXCELLENT"}}, {"test_name": "Network Resilience", "timestamp": "2025-05-27T02:52:28.039317+00:00", "status": "PASSED", "scenarios_tested": [{"scenario": "Normal Connection", "success": true, "recovery_time_ms": 50, "test_duration_ms": 50.**************, "resilience_grade": "EXCELLENT"}, {"scenario": "High Latency", "success": true, "recovery_time_ms": 500, "test_duration_ms": 500.**************, "resilience_grade": "EXCELLENT"}, {"scenario": "Intermittent Connection", "success": true, "recovery_time_ms": 200, "test_duration_ms": 200.06299018859863, "resilience_grade": "EXCELLENT"}, {"scenario": "Connection Timeout", "success": true, "recovery_time_ms": 1000, "test_duration_ms": 1000.892162322998, "resilience_grade": "GOOD"}, {"scenario": "DNS Resolution Delay", "success": true, "recovery_time_ms": 300, "test_duration_ms": 300.9929656982422, "resilience_grade": "EXCELLENT"}], "metrics": {"resilience_rate_percent": 100.0, "average_recovery_time_ms": 410.0, "scenarios_tested": 5, "successful_recoveries": 5, "network_grade": "EXCELLENT"}}, {"test_name": "Concurrent API Calls", "timestamp": "2025-05-27T02:52:30.091794+00:00", "status": "PASSED", "concurrent_tests": [{"concurrency_level": 1, "total_time_ms": 52.941322326660156, "success_rate_percent": 100.0, "average_response_time_ms": 52.01387405395508, "successful_calls": 1, "performance_grade": "EXCELLENT"}, {"concurrency_level": 5, "total_time_ms": 54.210662841796875, "success_rate_percent": 100.0, "average_response_time_ms": 53.617191314697266, "successful_calls": 5, "performance_grade": "EXCELLENT"}, {"concurrency_level": 10, "total_time_ms": 55.88793754577637, "success_rate_percent": 100.0, "average_response_time_ms": 54.57322597503662, "successful_calls": 10, "performance_grade": "EXCELLENT"}, {"concurrency_level": 20, "total_time_ms": 57.24811553955078, "success_rate_percent": 100.0, "average_response_time_ms": 55.51109313964844, "successful_calls": 20, "performance_grade": "EXCELLENT"}], "metrics": {"max_concurrent_level": 20, "concurrency_grade": "EXCELLENT", "overall_performance": "STABLE"}}, {"test_name": "API Error Handling", "timestamp": "2025-05-27T02:52:30.312259+00:00", "status": "PASSED", "error_scenarios": [{"error_type": "Connection Refused", "expected_recovery": true, "actual_recovery": true, "recovery_time_ms": 500, "test_duration_ms": 500.37240982055664, "handling_grade": "EXCELLENT"}, {"error_type": "Timeout Error", "expected_recovery": true, "actual_recovery": true, "recovery_time_ms": 1000, "test_duration_ms": 1000.3595352172852, "handling_grade": "EXCELLENT"}, {"error_type": "Authentication Error", "expected_recovery": false, "actual_recovery": false, "recovery_time_ms": 0, "test_duration_ms": 0.007867813110351562, "handling_grade": "EXCELLENT"}, {"error_type": "Rate Limit Exceeded", "expected_recovery": true, "actual_recovery": true, "recovery_time_ms": 2000, "test_duration_ms": 2000.5195140838623, "handling_grade": "EXCELLENT"}, {"error_type": "Server Error (500)", "expected_recovery": true, "actual_recovery": true, "recovery_time_ms": 800, "test_duration_ms": 800.1961708068848, "handling_grade": "EXCELLENT"}, {"error_type": "Invalid Response", "expected_recovery": true, "actual_recovery": true, "recovery_time_ms": 300, "test_duration_ms": 300.42099952697754, "handling_grade": "EXCELLENT"}], "metrics": {"recovery_rate_percent": 100.0, "average_recovery_time_ms": 920.0, "scenarios_tested": 6, "successful_recoveries": 5, "error_handling_grade": "EXCELLENT"}}], "safety_confirmation": {"trading_operations_affected": false, "test_mode_only": true, "zero_trading_impact": true}}