"""
Phase 5E comprehensive tests to push cot_reports/models.py to 90%+ coverage.
"""

import pytest
from datetime import date
from src.forex_bot.cot_reports import models

class TestCOTReportsModelsPhase5Clean:
    """Phase 5E tests to achieve 90%+ coverage for cot_reports/models.py."""

    def test_comprehensive_cot_report_validation(self):
        """Test COTReport class with comprehensive scenarios."""
        
        # Test successful creation
        report = models.COTReport(
            report_date=date(2023, 12, 15),
            currency='EUR',
            contract='EUR_USD',
            non_commercial_long=150000,
            non_commercial_short=120000,
            non_commercial_spreading=10000,
            commercial_long=80000,
            commercial_short=110000,
            total_reportable_long=240000,
            total_reportable_short=240000,
            non_reportable_long=60000,
            non_reportable_short=60000
        )
        
        # Test basic attributes
        assert report.report_date == date(2023, 12, 15)
        assert report.currency == 'EUR'
        assert report.contract == 'EUR_USD'
        assert report.non_commercial_long == 150000
        assert report.non_commercial_short == 120000
        
        # Test derived properties
        assert report.non_commercial_net == 30000  # 150000 - 120000
        assert report.commercial_net == -30000     # 80000 - 110000
        assert report.non_reportable_net == 0      # 60000 - 60000
        assert report.open_interest == 300000      # 240000 + 60000
        
        # Test percentage calculations
        expected_non_commercial_pct = (30000 / 300000) * 100.0  # 10.0%
        expected_commercial_pct = (-30000 / 300000) * 100.0     # -10.0%
        expected_non_reportable_pct = (0 / 300000) * 100.0      # 0.0%
        
        assert abs(report.non_commercial_net_percentage - expected_non_commercial_pct) < 1e-10
        assert abs(report.commercial_net_percentage - expected_commercial_pct) < 1e-10
        assert abs(report.non_reportable_net_percentage - expected_non_reportable_pct) < 1e-10

    def test_cot_report_zero_open_interest_edge_case(self):
        """Test COTReport with zero open interest edge case."""
        
        # Create report with zero open interest
        report = models.COTReport(
            report_date=date(2023, 12, 15),
            currency='GBP',
            contract='GBP_USD',
            non_commercial_long=0,
            non_commercial_short=0,
            non_commercial_spreading=0,
            commercial_long=0,
            commercial_short=0,
            total_reportable_long=0,
            total_reportable_short=0,
            non_reportable_long=0,
            non_reportable_short=0
        )
        
        # Test that percentage calculations handle zero division
        assert report.open_interest == 0
        assert report.non_commercial_net_percentage == 0.0
        assert report.commercial_net_percentage == 0.0
        assert report.non_reportable_net_percentage == 0.0

    def test_cot_report_negative_positions(self):
        """Test COTReport with negative net positions."""
        
        # Create report with negative net positions
        report = models.COTReport(
            report_date=date(2023, 12, 15),
            currency='JPY',
            contract='USD_JPY',
            non_commercial_long=50000,
            non_commercial_short=100000,
            non_commercial_spreading=5000,
            commercial_long=200000,
            commercial_short=150000,
            total_reportable_long=255000,
            total_reportable_short=255000,
            non_reportable_long=45000,
            non_reportable_short=45000
        )
        
        # Test negative net positions
        assert report.non_commercial_net == -50000  # 50000 - 100000
        assert report.commercial_net == 50000       # 200000 - 150000
        assert report.non_reportable_net == 0       # 45000 - 45000
        assert report.open_interest == 300000       # 255000 + 45000    def test_comprehensive_cot_positioning_validation(self):
        """Test COTPositioning class with comprehensive scenarios."""
        
        # Test successful creation with neutral positioning
        positioning = models.COTPositioning(
            currency='EUR',
            latest_report_date=date(2023, 12, 15),
            net_speculator_position=5.0,
            net_hedger_position=-5.0,
            net_small_trader_position=0.0,
            z_score=0.5,
            percentile=60.0,
            position_change_4_weeks=2.0,
            position_change_12_weeks=3.5,
            is_extreme_long=False,
            is_extreme_short=False,
            is_crowded_long=False,
            is_crowded_short=False
        )
        
        # Test basic attributes
        assert positioning.currency == 'EUR'
        assert positioning.latest_report_date == date(2023, 12, 15)
        assert positioning.net_speculator_position == 5.0
        assert positioning.net_hedger_position == -5.0
        assert positioning.net_small_trader_position == 0.0
        assert positioning.z_score == 0.5
        assert positioning.percentile == 60.0
        
        # Test neutral sentiment
        assert positioning.positioning_sentiment == "Neutral"
        assert positioning.contrarian_signal is None

    def test_cot_positioning_extreme_long_scenario(self):
        """Test COTPositioning with extreme long positioning."""
        
        positioning = models.COTPositioning(
            currency='GBP',
            latest_report_date=date(2023, 12, 15),
            net_speculator_position=25.0,
            net_hedger_position=-25.0,
            net_small_trader_position=0.0,
            z_score=3.0,
            percentile=95.0,
            position_change_4_weeks=10.0,
            position_change_12_weeks=15.0,
            is_extreme_long=True,
            is_extreme_short=False,
            is_crowded_long=False,
            is_crowded_short=False
        )
        
        # Test extreme long sentiment and contrarian signal
        assert positioning.positioning_sentiment == "Extremely Bullish"
        assert positioning.contrarian_signal == "SELL"    def test_cot_positioning_extreme_short_scenario(self):
        """Test COTPositioning with extreme short positioning."""
        
        positioning = models.COTPositioning(
            currency='JPY',
            latest_report_date=date(2023, 12, 15),
            net_speculator_position=-25.0,
            net_hedger_position=25.0,
            net_small_trader_position=0.0,
            z_score=-3.0,
            percentile=5.0,
            position_change_4_weeks=-10.0,
            position_change_12_weeks=-15.0,
            is_extreme_long=False,
            is_extreme_short=True,
            is_crowded_long=False,
            is_crowded_short=False
        )
        
        # Test extreme short sentiment and contrarian signal
        assert positioning.positioning_sentiment == "Extremely Bearish"
        assert positioning.contrarian_signal == "BUY"

    def test_cot_positioning_crowded_scenarios(self):
        """Test COTPositioning with crowded positioning scenarios."""
        
        # Test crowded long
        positioning_long = models.COTPositioning(
            currency='CHF',
            latest_report_date=date(2023, 12, 15),
            net_speculator_position=15.0,
            net_hedger_position=-15.0,
            net_small_trader_position=0.0,
            z_score=2.0,
            percentile=85.0,
            position_change_4_weeks=5.0,
            position_change_12_weeks=8.0,
            is_extreme_long=False,
            is_extreme_short=False,
            is_crowded_long=True,
            is_crowded_short=False
        )
        
        # Test crowded long sentiment
        assert positioning_long.positioning_sentiment == "Bullish"
        assert positioning_long.contrarian_signal is None
        
        # Test crowded short
        positioning_short = models.COTPositioning(
            currency='CAD',
            latest_report_date=date(2023, 12, 15),
            net_speculator_position=-15.0,
            net_hedger_position=15.0,
            net_small_trader_position=0.0,
            z_score=-2.0,
            percentile=15.0,
            position_change_4_weeks=-5.0,
            position_change_12_weeks=-8.0,
            is_extreme_long=False,
            is_extreme_short=False,
            is_crowded_long=False,
            is_crowded_short=True
        )
        
        # Test crowded short sentiment
        assert positioning_short.positioning_sentiment == "Bearish"
        assert positioning_short.contrarian_signal is None    def test_cot_positioning_edge_cases_and_priority(self):
        """Test COTPositioning with edge cases and flag priority."""
        
        # Test with extreme z-scores and percentiles
        positioning = models.COTPositioning(
            currency='AUD',
            latest_report_date=date(2023, 12, 15),
            net_speculator_position=0.0,
            net_hedger_position=0.0,
            net_small_trader_position=0.0,
            z_score=0.0,
            percentile=50.0,
            position_change_4_weeks=0.0,
            position_change_12_weeks=0.0
        )
        
        # Test default values for boolean flags
        assert positioning.is_extreme_long == False
        assert positioning.is_extreme_short == False
        assert positioning.is_crowded_long == False
        assert positioning.is_crowded_short == False
        
        # Test neutral sentiment with all zeros
        assert positioning.positioning_sentiment == "Neutral"
        assert positioning.contrarian_signal is None
        
        # Test extreme_long takes priority over crowded_long
        positioning_priority = models.COTPositioning(
            currency='NZD',
            latest_report_date=date(2023, 12, 15),
            net_speculator_position=30.0,
            net_hedger_position=-30.0,
            net_small_trader_position=0.0,
            z_score=4.0,
            percentile=98.0,
            position_change_4_weeks=12.0,
            position_change_12_weeks=20.0,
            is_extreme_long=True,
            is_extreme_short=False,
            is_crowded_long=True,  # This should be overridden by extreme_long
            is_crowded_short=False
        )
        
        # Test that extreme_long takes priority
        assert positioning_priority.positioning_sentiment == "Extremely Bullish"
        assert positioning_priority.contrarian_signal == "SELL"