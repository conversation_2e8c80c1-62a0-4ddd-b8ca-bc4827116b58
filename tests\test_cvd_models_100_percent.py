"""
Targeted tests to push cvd/models.py to 100% coverage.
"""

import numpy as np
import pandas as pd
from src.forex_bot.cvd.models import CVDResult, CVDDivergence


def test_cvd_result_to_dataframe_with_none_volumes():
    """Test to_dataframe method with None volume arrays - covers lines 39-55."""
    timestamps = np.array([pd.Timestamp('2023-01-01 00:00:00')])
    cvd_values = np.array([100.0])

    result = CVDResult(
        timestamps=timestamps,
        cvd_values=cvd_values,
        symbol='EURUSD',
        timeframe=60,
        buying_volume=None,
        selling_volume=None,
        delta_volume=None
    )

    df = result.to_dataframe()
    assert isinstance(df, pd.DataFrame)
    assert len(df) == 1
    # Fix the timestamp comparison - convert to same type
    assert df['timestamp'].iloc[0] == timestamps[0]
    assert np.array_equal(df['cvd'].values, cvd_values)


def test_cvd_result_to_dataframe_with_volumes():
    """Test to_dataframe method with volume arrays - covers lines 46, 49, 52."""
    timestamps = np.array([pd.Timestamp('2023-01-01 00:00:00'), pd.Timestamp('2023-01-01 01:00:00')])
    cvd_values = np.array([100.0, 150.0])
    buying_volume = np.array([50.0, 75.0])
    selling_volume = np.array([30.0, 25.0])
    delta_volume = np.array([20.0, 50.0])

    result = CVDResult(
        timestamps=timestamps,
        cvd_values=cvd_values,
        symbol='EURUSD',
        timeframe=60,
        buying_volume=buying_volume,
        selling_volume=selling_volume,
        delta_volume=delta_volume
    )

    df = result.to_dataframe()
    assert isinstance(df, pd.DataFrame)
    assert len(df) == 2
    assert 'buying_volume' in df.columns  # covers line 46
    assert 'selling_volume' in df.columns  # covers line 49
    assert 'delta_volume' in df.columns  # covers line 52
    assert np.array_equal(df['buying_volume'].values, buying_volume)
    assert np.array_equal(df['selling_volume'].values, selling_volume)
    assert np.array_equal(df['delta_volume'].values, delta_volume)


def test_cvd_divergence_is_bullish_property():
    """Test CVDDivergence is_bullish property - covers line 80."""
    divergence = CVDDivergence(
        start_time=pd.Timestamp('2023-01-01 00:00:00'),
        end_time=pd.Timestamp('2023-01-01 01:00:00'),
        start_price=1.0,
        end_price=2.0,
        start_cvd=100.0,
        end_cvd=200.0,
        divergence_type='bullish',
        strength=0.8
    )

    # Check is_bullish property (this covers line 80)
    assert divergence.is_bullish is True


def test_cvd_divergence_is_bearish_property():
    """Test CVDDivergence is_bearish property - covers line 90."""
    divergence = CVDDivergence(
        start_time=pd.Timestamp('2023-01-01 00:00:00'),
        end_time=pd.Timestamp('2023-01-01 01:00:00'),
        start_price=2.0,
        end_price=1.0,
        start_cvd=200.0,
        end_cvd=100.0,
        divergence_type='bearish',
        strength=0.7
    )

    # Check is_bearish property (this covers line 90)
    assert divergence.is_bearish is True


def test_cvd_divergence_price_change_property():
    """Test CVDDivergence price_change property - covers line 100."""
    divergence = CVDDivergence(
        start_time=pd.Timestamp('2023-01-01 00:00:00'),
        end_time=pd.Timestamp('2023-01-01 01:00:00'),
        start_price=1.0,
        end_price=2.5,
        start_cvd=100.0,
        end_cvd=200.0,
        divergence_type='bullish',
        strength=0.8
    )

    # Check price_change property (this covers line 100)
    assert divergence.price_change == 1.5  # 2.5 - 1.0


def test_cvd_divergence_cvd_change_property():
    """Test CVDDivergence cvd_change property - covers line 110."""
    divergence = CVDDivergence(
        start_time=pd.Timestamp('2023-01-01 00:00:00'),
        end_time=pd.Timestamp('2023-01-01 01:00:00'),
        start_price=1.0,
        end_price=2.0,
        start_cvd=100.0,
        end_cvd=200.0,
        divergence_type='bullish',
        strength=0.8
    )

    # Check cvd_change property (this covers line 110)
    assert divergence.cvd_change == 100.0  # 200.0 - 100.0