"""
Test for volume_profile/models.py to reach 90%+ coverage
Target: 89% → 90%+ (missing lines 47-53, 76, 86)
"""

import pytest
import numpy as np
import pandas as pd
from src.forex_bot.volume_profile.models import VolumeProfileResult, VolumeZone


class TestVolumeProfileModels90:
    """Test to push volume_profile/models.py to 90%+ coverage"""

    def test_volume_profile_to_dataframe_edge_cases(self):
        """Test VolumeProfileResult.to_dataframe() with edge cases (lines 47-53)"""
        
        # Test with empty normalized_volumes and cumulative_volumes
        profile = VolumeProfileResult(
            price_levels=np.array([1.2340, 1.2341, 1.2342]),
            volumes=np.array([1000.0, 1500.0, 800.0]),
            poc_price=1.2341,
            poc_volume=1500.0,
            value_area_high=1.2342,
            value_area_low=1.2340,
            symbol="EURUSD",
            timeframe=1,
            normalized_volumes=[],  # Empty - should trigger np.zeros_like
            cumulative_volumes=[]   # Empty - should trigger np.zeros_like
        )
        
        # This should trigger lines 50-51 (np.zeros_like fallbacks)
        df = profile.to_dataframe()
        
        # Verify the dataframe structure
        assert isinstance(df, pd.DataFrame)
        assert len(df) == 3
        assert list(df.columns) == ['price_level', 'volume', 'normalized_volume', 'cumulative_volume']
        
        # Verify the fallback values are zeros
        assert all(df['normalized_volume'] == 0.0)
        assert all(df['cumulative_volume'] == 0.0)
        
        # Verify the actual data
        assert list(df['price_level']) == [1.2340, 1.2341, 1.2342]
        assert list(df['volume']) == [1000.0, 1500.0, 800.0]

    def test_volume_zone_properties(self):
        """Test VolumeZone properties (lines 76, 86)"""
        
        # Test middle_price property (line 76)
        zone = VolumeZone(
            zone_type="high_volume",
            price_low=1.2340,
            price_high=1.2350,
            volume=5000.0,
            strength=0.85
        )
        
        # Test mid_price calculation (the property is called mid_price, not middle_price)
        expected_middle = (1.2340 + 1.2350) / 2
        assert abs(zone.mid_price - expected_middle) < 1e-10
        assert abs(zone.mid_price - 1.2345) < 1e-10
        
        # Test price_range property (line 86)
        expected_range = 1.2350 - 1.2340
        assert abs(zone.price_range - expected_range) < 1e-10
        assert abs(zone.price_range - 0.001) < 1e-10
        
        # Test with different values
        zone2 = VolumeZone(
            zone_type="value_area",
            price_low=1.3000,
            price_high=1.3020,
            volume=3000.0,
            strength=0.75
        )
        
        assert abs(zone2.mid_price - 1.3010) < 1e-10
        assert abs(zone2.price_range - 0.002) < 1e-10