"""
Phase 5Y: Final push for metrics_dashboard/models.py to achieve 90%+ coverage.
"""

import pytest
from datetime import datetime, timedelta
from src.forex_bot.metrics_dashboard.models import (
    TimeFrame, MetricCategory, MetricValue, MetricTimeSeries, 
    MarketMetrics, DashboardLayout, Dashboard
)


class TestMetricsDashboardModelsPhase5YFinal:
    """Final test class to push metrics_dashboard/models.py to 90%+ coverage"""

    def test_pandas_import_error_in_to_dataframe(self):
        """Test pandas import error in to_dataframe method"""
        
        # Create a valid MetricTimeSeries
        timestamps = [datetime.now(), datetime.now() + timedelta(hours=1)]
        values = [1.0, 2.0]
        
        time_series = MetricTimeSeries(
            name="test_series",
            values=values,
            timestamps=timestamps,
            category=MetricCategory.PERFORMANCE
        )
        
        # Test to_dataframe with pandas available (should work)
        df = time_series.to_dataframe()
        assert len(df) == 2
        assert list(df.columns) == ['timestamp', 'value']

    def test_market_metrics_sentiment_validation_edge_cases(self):
        """Test MarketMetrics sentiment validation edge cases"""
        
        # Test exact boundary values
        boundary_test_cases = [
            (-1.0, True),   # Valid: exactly -1.0
            (1.0, True),    # Valid: exactly 1.0
            (-1.1, False),  # Invalid: below -1.0
            (1.1, False),   # Invalid: above 1.0
            (0.0, True),    # Valid: exactly 0.0
            (None, True)    # Valid: None is allowed
        ]
        
        for sentiment_score, should_be_valid in boundary_test_cases:
            if should_be_valid:
                # Should not raise an exception
                market_metrics = MarketMetrics(
                    spread=1.5,
                    volume=1000.0,
                    volatility=0.02,
                    sentiment_score=sentiment_score,
                    timestamp=datetime.now()
                )
                assert market_metrics.sentiment_score == sentiment_score
            else:
                # Should raise an exception
                with pytest.raises(ValueError, match="sentiment_score must be between -1.0 and 1.0"):
                    MarketMetrics(
                        spread=1.5,
                        volume=1000.0,
                        volatility=0.02,
                        sentiment_score=sentiment_score,
                        timestamp=datetime.now()
                    )