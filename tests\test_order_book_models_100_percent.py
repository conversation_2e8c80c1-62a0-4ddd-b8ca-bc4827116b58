"""
Comprehensive tests to push order_book/models.py to 100% coverage.
"""

import pytest
from unittest.mock import patch
from datetime import datetime, timezone
from pydantic import ValidationError

class TestOrderBookModels100Percent:
    """Comprehensive tests to achieve 100% coverage for order_book/models.py."""

    def test_import_error_handling_pydantic(self):
        """Test pydantic import error handling."""
        from src.forex_bot.order_book import models
        assert hasattr(models, 'PYDANTIC_AVAILABLE')
        assert isinstance(models.PYDANTIC_AVAILABLE, bool)

    def test_order_book_entry_validation_errors(self):
        """Test OrderBookEntry validation errors."""
        from src.forex_bot.order_book.models import OrderBookEntry
        
        # Test negative price
        with pytest.raises(ValidationError) as exc_info:
            OrderBookEntry(price=-1.0, volume=100.0, type="bid")
        assert "Price must be positive" in str(exc_info.value)
        
        # Test zero price
        with pytest.raises(ValidationError) as exc_info:
            OrderBookEntry(price=0.0, volume=100.0, type="bid")
        assert "Price must be positive" in str(exc_info.value)
        
        # Test negative volume
        with pytest.raises(ValidationError) as exc_info:
            OrderBookEntry(price=1.1000, volume=-100.0, type="bid")
        assert "Volume must be non-negative" in str(exc_info.value)

    def test_order_book_properties(self):
        """Test OrderBook properties and methods."""
        from src.forex_bot.order_book.models import OrderBook, OrderBookEntry
        
        timestamp = datetime.now(timezone.utc)
        
        # Create test entries
        bids = [
            OrderBookEntry(price=1.1000, volume=100.0, type="bid"),
            OrderBookEntry(price=1.0999, volume=200.0, type="bid"),
            OrderBookEntry(price=1.0998, volume=150.0, type="bid")
        ]
        
        asks = [
            OrderBookEntry(price=1.1001, volume=120.0, type="ask"),
            OrderBookEntry(price=1.1002, volume=180.0, type="ask"),
            OrderBookEntry(price=1.1003, volume=160.0, type="ask")
        ]
        
        order_book = OrderBook(
            symbol="EURUSD",
            timestamp=timestamp,
            bids=bids,
            asks=asks,
            is_stale=False,
            source="MT5"
        )
        
        # Test basic properties
        assert order_book.symbol == "EURUSD"
        assert order_book.timestamp == timestamp
        assert len(order_book.bids) == 3
        assert len(order_book.asks) == 3
        
        # Test price properties
        assert order_book.bid_prices == [1.1000, 1.0999, 1.0998]
        assert order_book.ask_prices == [1.1001, 1.1002, 1.1003]
        assert order_book.best_bid == 1.1000
        assert order_book.best_ask == 1.1001
        assert order_book.spread == 0.0001
        assert order_book.mid_price == 1.10005
        
        # Test volume properties
        assert order_book.total_bid_volume == 450.0
        assert order_book.total_ask_volume == 460.0
        assert abs(order_book.imbalance_ratio - (-10.0 / 910.0)) < 1e-10        # Test cumulative properties
        cumulative_bids = order_book.cumulative_bids
        assert len(cumulative_bids) == 3
        assert cumulative_bids[0]['price'] == 1.1000
        assert cumulative_bids[0]['volume'] == 100.0
        assert cumulative_bids[1]['price'] == 1.0999
        assert cumulative_bids[1]['volume'] == 300.0
        assert cumulative_bids[2]['price'] == 1.0998
        assert cumulative_bids[2]['volume'] == 450.0
        
        cumulative_asks = order_book.cumulative_asks
        assert len(cumulative_asks) == 3
        assert cumulative_asks[0]['price'] == 1.1001
        assert cumulative_asks[0]['volume'] == 120.0
        assert cumulative_asks[1]['price'] == 1.1002
        assert cumulative_asks[1]['volume'] == 300.0
        assert cumulative_asks[2]['price'] == 1.1003
        assert cumulative_asks[2]['volume'] == 460.0

    def test_empty_order_book(self):
        """Test OrderBook with empty bids and asks."""
        from src.forex_bot.order_book.models import OrderBook
        
        timestamp = datetime.now(timezone.utc)
        order_book = OrderBook(
            symbol="EURUSD",
            timestamp=timestamp,
            bids=[],
            asks=[],
            is_stale=True,
            source="TEST"
        )
        
        # Test properties with empty lists
        assert order_book.bid_prices == []
        assert order_book.ask_prices == []
        assert order_book.best_bid is None
        assert order_book.best_ask is None
        assert order_book.spread is None
        assert order_book.mid_price is None
        assert order_book.total_bid_volume == 0.0
        assert order_book.total_ask_volume == 0.0
        assert order_book.imbalance_ratio is None
        assert order_book.cumulative_bids == []
        assert order_book.cumulative_asks == []

    def test_non_pydantic_fallback(self):
        """Test non-pydantic fallback implementations."""
        with patch('src.forex_bot.order_book.models.PYDANTIC_AVAILABLE', False):
            import importlib
            from src.forex_bot.order_book import models
            importlib.reload(models)
            
            timestamp = datetime.now(timezone.utc)
            
            # Test OrderBookEntry fallback
            entry = models.OrderBookEntry(price=1.1000, volume=100.0, type="bid")
            assert entry.price == 1.1000
            assert entry.volume == 100.0
            assert entry.type == "bid"
            
            # Test validation errors
            with pytest.raises(ValueError) as exc_info:
                models.OrderBookEntry(price=-1.0, volume=100.0, type="bid")
            assert "Price must be positive" in str(exc_info.value)
            
            with pytest.raises(ValueError) as exc_info:
                models.OrderBookEntry(price=1.1000, volume=-100.0, type="bid")
            assert "Volume must be non-negative" in str(exc_info.value)
            
            with pytest.raises(ValueError) as exc_info:
                models.OrderBookEntry(price=1.1000, volume=100.0, type="invalid")
            assert 'Type must be "bid" or "ask"' in str(exc_info.value)