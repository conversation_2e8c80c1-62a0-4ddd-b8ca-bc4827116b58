"""
Targeted tests to push volatility_indices/models.py to 90%+ coverage.
"""

import pytest
from datetime import date
from src.forex_bot.volatility_indices.models import VolatilityData, VolatilityTrend, VolatilityRegime


def test_volatility_data_post_init_edge_cases():
    """Test post_init method edge cases - covers missing lines."""
    # Test with zero previous_value (division by zero protection)
    volatility_data = VolatilityData(
        index_name='VIX',
        date=date(2023, 1, 1),
        value=20.5,
        previous_value=0.0
    )

    # Check that change is calculated but change_percent is None (zero division protection)
    assert volatility_data.change == 20.5  # 20.5 - 0.0
    assert volatility_data.change_percent is None  # Division by zero protection


def test_volatility_trend_market_sentiment_subdued():
    """Test market_sentiment property for subdued volatility - covers line 112."""
    # Create VolatilityTrend with subdued volatility
    subdued_trend = VolatilityTrend(
        index_name='VIX',
        latest_date=date(2023, 1, 1),
        latest_value=12.0,
        trend_direction='down',
        trend_strength=0.3,
        values_5d=[12.0, 12.5, 13.0, 13.5, 14.0],
        values_20d=[12.0] * 20,
        values_60d=[12.0] * 60,
        avg_5d=13.0,
        avg_20d=16.5,
        avg_60d=12.0,
        percentile_1y=15.0,
        is_rising=False,
        is_falling=True,
        is_above_5d_avg=False,
        is_above_20d_avg=False,
        is_above_60d_avg=False,
        is_extreme_high=False,
        is_extreme_low=False,
        is_elevated=False,
        is_subdued=True
    )
    assert subdued_trend.market_sentiment == "Complacency"


def test_volatility_trend_market_sentiment_normal():
    """Test market_sentiment property for normal volatility - covers line 114."""
    # Create VolatilityTrend with normal volatility (none of the extreme flags set)
    normal_trend = VolatilityTrend(
        index_name='VIX',
        latest_date=date(2023, 1, 1),
        latest_value=18.0,
        trend_direction='flat',
        trend_strength=0.1,
        values_5d=[18.0, 17.8, 18.2, 17.9, 18.1],
        values_20d=[18.0] * 20,
        values_60d=[18.0] * 60,
        avg_5d=18.0,
        avg_20d=18.0,
        avg_60d=18.0,
        percentile_1y=50.0,
        is_rising=False,
        is_falling=False,
        is_above_5d_avg=True,
        is_above_20d_avg=True,
        is_above_60d_avg=True,
        is_extreme_high=False,
        is_extreme_low=False,
        is_elevated=False,
        is_subdued=False
    )

    assert normal_trend.market_sentiment == "Neutral"


def test_volatility_regime_trading_bias_neutral():
    """Test trading_bias property for neutral regime - covers line 153."""
    # Create VolatilityRegime with neutral bias (neither risk-on nor risk-off)
    neutral_regime = VolatilityRegime(
        date=date(2023, 1, 1),
        vix_regime="Normal",
        move_regime="Normal",
        overall_regime="Normal",
        currency_impact={
            'USD': 'neutral',
            'EUR': 'neutral',
            'JPY': 'neutral',
            'GBP': 'neutral',
            'AUD': 'neutral',
            'NZD': 'neutral',
            'CAD': 'neutral',
            'CHF': 'neutral'
        },
        risk_on_assets=False,
        risk_off_assets=False,
        volatility_trend="stable"
    )

    assert neutral_regime.trading_bias == "Neutral"

def test_volatility_regime_get_currency_bias_neutral():
    """Test get_currency_bias method for neutral impact - covers line 174."""
    # Create VolatilityRegime with neutral currency impacts
    regime = VolatilityRegime(
        date=date(2023, 1, 1),
        vix_regime="Normal",
        move_regime="Normal",
        overall_regime="Normal",
        currency_impact={
            'USD': 'neutral',
            'EUR': 'positive',
            'JPY': 'negative'
        },
        risk_on_assets=False,
        risk_off_assets=True,
        volatility_trend="stable"
    )

    assert regime.get_currency_bias('USD') == "NEUTRAL"


def test_volatility_regime_get_pair_bias_complex_scenarios():
    """Test get_pair_bias method for complex scenarios - covers lines 194, 198, 200, 206."""
    # Create VolatilityRegime with mixed currency impacts
    regime = VolatilityRegime(
        date=date(2023, 1, 1),
        vix_regime="Elevated",
        move_regime="Normal",
        overall_regime="Elevated",
        currency_impact={
            'USD': 'positive',
            'EUR': 'negative',
            'JPY': 'positive',
            'GBP': 'negative',
            'AUD': 'neutral',
            'CAD': 'neutral',
            'CHF': 'positive'
        },
        risk_on_assets=False,
        risk_off_assets=True,
        volatility_trend="increasing"
    )

    # Test moderate buy signal: base positive, quote not positive (but not negative either)
    assert regime.get_pair_bias('USD', 'AUD') == "BUY"  # Tests line 194

    # Test moderate sell signal: base negative, quote not negative (but not positive either)
    assert regime.get_pair_bias('EUR', 'AUD') == "SELL"  # Tests line 196

    # Test moderate sell signal: base not negative, quote positive
    assert regime.get_pair_bias('AUD', 'USD') == "SELL"  # Tests line 198

    # Test moderate buy signal: base not positive, quote negative
    assert regime.get_pair_bias('AUD', 'EUR') == "BUY"  # Tests line 200

    # Test neutral case: both neutral
    assert regime.get_pair_bias('AUD', 'CAD') == "NEUTRAL"  # Tests line 206


def test_volatility_data_change_percent_calculation():
    """Test change_percent calculation when None but previous_value != 0 - covers line 36."""
    # Create VolatilityData with change_percent=None and previous_value != 0
    volatility_data = VolatilityData(
        index_name='VIX',
        date=date(2023, 1, 1),
        value=25.0,
        previous_value=20.0,
        change_percent=None  # Explicitly set to None
    )

    # Should calculate change_percent in __post_init__
    assert volatility_data.change_percent == 25.0  # (5.0 / 20.0) * 100


def test_volatility_trend_volatility_regime_all_cases():
    """Test all volatility_regime property cases - covers lines 87, 89, 91, 93, 95."""

    # Test "Extreme High" - line 87
    extreme_high_trend = VolatilityTrend(
        index_name='VIX',
        latest_date=date(2023, 1, 1),
        latest_value=40.0,
        trend_direction='up',
        trend_strength=0.8,
        values_5d=[40.0] * 5,
        values_20d=[40.0] * 20,
        values_60d=[40.0] * 60,
        avg_5d=40.0,
        avg_20d=40.0,
        avg_60d=40.0,
        percentile_1y=95.0,
        is_rising=True,
        is_falling=False,
        is_above_5d_avg=True,
        is_above_20d_avg=True,
        is_above_60d_avg=True,
        is_extreme_high=True,
        is_extreme_low=False,
        is_elevated=False,
        is_subdued=False
    )
    assert extreme_high_trend.volatility_regime == "Extreme High"

    # Test "Elevated" - line 89
    elevated_trend = VolatilityTrend(
        index_name='VIX',
        latest_date=date(2023, 1, 1),
        latest_value=28.0,
        trend_direction='up',
        trend_strength=0.6,
        values_5d=[28.0] * 5,
        values_20d=[28.0] * 20,
        values_60d=[28.0] * 60,
        avg_5d=28.0,
        avg_20d=28.0,
        avg_60d=28.0,
        percentile_1y=80.0,
        is_rising=True,
        is_falling=False,
        is_above_5d_avg=True,
        is_above_20d_avg=True,
        is_above_60d_avg=True,
        is_extreme_high=False,
        is_extreme_low=False,
        is_elevated=True,
        is_subdued=False
    )
    assert elevated_trend.volatility_regime == "Elevated"

    # Test "Extreme Low" - line 91
    extreme_low_trend = VolatilityTrend(
        index_name='VIX',
        latest_date=date(2023, 1, 1),
        latest_value=8.0,
        trend_direction='down',
        trend_strength=0.9,
        values_5d=[8.0] * 5,
        values_20d=[8.0] * 20,
        values_60d=[8.0] * 60,
        avg_5d=8.0,
        avg_20d=8.0,
        avg_60d=8.0,
        percentile_1y=5.0,
        is_rising=False,
        is_falling=True,
        is_above_5d_avg=False,
        is_above_20d_avg=False,
        is_above_60d_avg=False,
        is_extreme_high=False,
        is_extreme_low=True,
        is_elevated=False,
        is_subdued=False
    )
    assert extreme_low_trend.volatility_regime == "Extreme Low"

    # Test "Subdued" - line 93
    subdued_trend = VolatilityTrend(
        index_name='VIX',
        latest_date=date(2023, 1, 1),
        latest_value=12.0,
        trend_direction='down',
        trend_strength=0.3,
        values_5d=[12.0] * 5,
        values_20d=[12.0] * 20,
        values_60d=[12.0] * 60,
        avg_5d=12.0,
        avg_20d=12.0,
        avg_60d=12.0,
        percentile_1y=20.0,
        is_rising=False,
        is_falling=True,
        is_above_5d_avg=False,
        is_above_20d_avg=False,
        is_above_60d_avg=False,
        is_extreme_high=False,
        is_extreme_low=False,
        is_elevated=False,
        is_subdued=True
    )
    assert subdued_trend.volatility_regime == "Subdued"

    # Test "Normal" - line 95 (else case)
    normal_trend = VolatilityTrend(
        index_name='VIX',
        latest_date=date(2023, 1, 1),
        latest_value=18.0,
        trend_direction='flat',
        trend_strength=0.1,
        values_5d=[18.0] * 5,
        values_20d=[18.0] * 20,
        values_60d=[18.0] * 60,
        avg_5d=18.0,
        avg_20d=18.0,
        avg_60d=18.0,
        percentile_1y=50.0,
        is_rising=False,
        is_falling=False,
        is_above_5d_avg=True,
        is_above_20d_avg=True,
        is_above_60d_avg=True,
        is_extreme_high=False,
        is_extreme_low=False,
        is_elevated=False,
        is_subdued=False
    )
    assert normal_trend.volatility_regime == "Normal"


def test_volatility_trend_market_sentiment_all_cases():
    """Test all market_sentiment property cases - covers lines 107, 109, 111."""

    # Test "Fear" - line 107
    fear_trend = VolatilityTrend(
        index_name='VIX',
        latest_date=date(2023, 1, 1),
        latest_value=40.0,
        trend_direction='up',
        trend_strength=0.8,
        values_5d=[40.0] * 5,
        values_20d=[40.0] * 20,
        values_60d=[40.0] * 60,
        avg_5d=40.0,
        avg_20d=40.0,
        avg_60d=40.0,
        percentile_1y=95.0,
        is_rising=True,
        is_falling=False,
        is_above_5d_avg=True,
        is_above_20d_avg=True,
        is_above_60d_avg=True,
        is_extreme_high=True,
        is_extreme_low=False,
        is_elevated=False,
        is_subdued=False
    )
    assert fear_trend.market_sentiment == "Fear"

    # Test "Caution" - line 109
    caution_trend = VolatilityTrend(
        index_name='VIX',
        latest_date=date(2023, 1, 1),
        latest_value=28.0,
        trend_direction='up',
        trend_strength=0.6,
        values_5d=[28.0] * 5,
        values_20d=[28.0] * 20,
        values_60d=[28.0] * 60,
        avg_5d=28.0,
        avg_20d=28.0,
        avg_60d=28.0,
        percentile_1y=80.0,
        is_rising=True,
        is_falling=False,
        is_above_5d_avg=True,
        is_above_20d_avg=True,
        is_above_60d_avg=True,
        is_extreme_high=False,
        is_extreme_low=False,
        is_elevated=True,
        is_subdued=False
    )
    assert caution_trend.market_sentiment == "Caution"

    # Test "Extreme Complacency" - line 111
    extreme_complacency_trend = VolatilityTrend(
        index_name='VIX',
        latest_date=date(2023, 1, 1),
        latest_value=8.0,
        trend_direction='down',
        trend_strength=0.9,
        values_5d=[8.0] * 5,
        values_20d=[8.0] * 20,
        values_60d=[8.0] * 60,
        avg_5d=8.0,
        avg_20d=8.0,
        avg_60d=8.0,
        percentile_1y=5.0,
        is_rising=False,
        is_falling=True,
        is_above_5d_avg=False,
        is_above_20d_avg=False,
        is_above_60d_avg=False,
        is_extreme_high=False,
        is_extreme_low=True,
        is_elevated=False,
        is_subdued=False
    )
    assert extreme_complacency_trend.market_sentiment == "Extreme Complacency"


def test_volatility_regime_trading_bias_all_cases():
    """Test all trading_bias property cases - covers lines 149, 151."""

    # Test "Risk-On" - line 149
    risk_on_regime = VolatilityRegime(
        date=date(2023, 1, 1),
        vix_regime="Low",
        move_regime="Low",
        overall_regime="Low",
        currency_impact={'USD': 'positive'},
        risk_on_assets=True,
        risk_off_assets=False,
        volatility_trend="decreasing"
    )
    assert risk_on_regime.trading_bias == "Risk-On"

    # Test "Risk-Off" - line 151
    risk_off_regime = VolatilityRegime(
        date=date(2023, 1, 1),
        vix_regime="High",
        move_regime="High",
        overall_regime="High",
        currency_impact={'USD': 'negative'},
        risk_on_assets=False,
        risk_off_assets=True,
        volatility_trend="increasing"
    )
    assert risk_off_regime.trading_bias == "Risk-Off"


def test_volatility_regime_get_currency_bias_all_cases():
    """Test all get_currency_bias method cases - covers lines 167, 170, 172."""
    regime = VolatilityRegime(
        date=date(2023, 1, 1),
        vix_regime="Normal",
        move_regime="Normal",
        overall_regime="Normal",
        currency_impact={
            'USD': 'positive',
            'EUR': 'negative',
            'JPY': 'neutral'
        },
        risk_on_assets=False,
        risk_off_assets=False,
        volatility_trend="stable"
    )

    # Test None return when currency not in impact dict - line 167
    assert regime.get_currency_bias('GBP') is None

    # Test "BUY" return for positive impact - line 170
    assert regime.get_currency_bias('USD') == "BUY"

    # Test "SELL" return for negative impact - line 172
    assert regime.get_currency_bias('EUR') == "SELL"


def test_volatility_regime_get_pair_bias_none_cases():
    """Test get_pair_bias method None cases - covers line 191."""
    regime = VolatilityRegime(
        date=date(2023, 1, 1),
        vix_regime="Normal",
        move_regime="Normal",
        overall_regime="Normal",
        currency_impact={
            'USD': 'positive',
            'EUR': 'negative'
        },
        risk_on_assets=False,
        risk_off_assets=False,
        volatility_trend="stable"
    )

    # Test None return when base_impact is None - line 191
    assert regime.get_pair_bias('GBP', 'USD') is None

    # Test None return when quote_impact is None - line 191
    assert regime.get_pair_bias('USD', 'GBP') is None


def test_volatility_regime_get_pair_bias_strong_signals():
    """Test get_pair_bias method strong signals - covers lines 194, 196."""
    regime = VolatilityRegime(
        date=date(2023, 1, 1),
        vix_regime="Normal",
        move_regime="Normal",
        overall_regime="Normal",
        currency_impact={
            'USD': 'positive',
            'EUR': 'negative',
            'JPY': 'positive',
            'GBP': 'negative'
        },
        risk_on_assets=False,
        risk_off_assets=False,
        volatility_trend="stable"
    )

    # Test strong buy signal: base positive, quote negative - line 194
    assert regime.get_pair_bias('USD', 'EUR') == "BUY"

    # Test strong sell signal: base negative, quote positive - line 196
    assert regime.get_pair_bias('EUR', 'USD') == "SELL"