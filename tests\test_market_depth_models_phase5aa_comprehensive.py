"""
Phase 5AA: Comprehensive test for market_depth_visualizer/models.py to push from 60% to 90%+ coverage.
"""

import pytest
from datetime import datetime, timezone
from unittest.mock import patch
from src.forex_bot.market_depth_visualizer.models import (
    VisualizationType, ColorScheme, DepthChartSettings, HeatmapSettings,
    TimeAndSalesSettings, LiquidityMapSettings, OrderFlowFootprintSettings,
    DashboardSettings, VisualizationSettings, TradeEntry, MarketDepthSnapshot,
    MarketDepthVisualization, MarketDepthDashboard
)


class TestMarketDepthModelsPhase5AAComprehensive:
    """Comprehensive test to push market_depth_visualizer/models.py to 90%+ coverage"""

    def test_all_settings_models_comprehensive(self):
        """Test all settings models with comprehensive parameters"""
        
        # Test HeatmapSettings
        heatmap = HeatmapSettings(
            price_levels=25,
            time_window=120,
            color_scheme=ColorScheme.DARK,
            custom_colormap="plasma",
            show_current_price=False,
            show_tooltips=False,
            interpolation="bilinear",
            normalization="log"
        )
        assert heatmap.price_levels == 25
        assert heatmap.time_window == 120
        assert heatmap.color_scheme == ColorScheme.DARK
        assert heatmap.custom_colormap == "plasma"
        assert heatmap.show_current_price is False
        assert heatmap.show_tooltips is False
        assert heatmap.interpolation == "bilinear"
        assert heatmap.normalization == "log"
        
        # Test TimeAndSalesSettings
        time_sales = TimeAndSalesSettings(
            max_entries=200,
            show_direction=False,
            show_time=False,
            show_price=False,
            show_volume=False,
            color_scheme=ColorScheme.COLORBLIND,
            custom_buy_color="#00FF00",
            custom_sell_color="#FF0000",
            highlight_large_trades=False,
            large_trade_threshold=3.0
        )
        assert time_sales.max_entries == 200
        assert time_sales.show_direction is False
        assert time_sales.show_time is False
        assert time_sales.show_price is False
        assert time_sales.show_volume is False
        assert time_sales.color_scheme == ColorScheme.COLORBLIND
        assert time_sales.custom_buy_color == "#00FF00"
        assert time_sales.custom_sell_color == "#FF0000"
        assert time_sales.highlight_large_trades is False
        assert time_sales.large_trade_threshold == 3.0    def test_liquidity_map_and_order_flow_settings(self):
        """Test LiquidityMapSettings and OrderFlowFootprintSettings"""
        
        # Test LiquidityMapSettings
        liquidity = LiquidityMapSettings(
            price_levels=15,
            show_bid_liquidity=False,
            show_ask_liquidity=False,
            color_scheme=ColorScheme.LIGHT,
            custom_bid_color="#0000FF",
            custom_ask_color="#FF00FF",
            show_tooltips=False,
            normalization="log"
        )
        assert liquidity.price_levels == 15
        assert liquidity.show_bid_liquidity is False
        assert liquidity.show_ask_liquidity is False
        assert liquidity.color_scheme == ColorScheme.LIGHT
        assert liquidity.custom_bid_color == "#0000FF"
        assert liquidity.custom_ask_color == "#FF00FF"
        assert liquidity.show_tooltips is False
        assert liquidity.normalization == "log"
        
        # Test OrderFlowFootprintSettings
        footprint = OrderFlowFootprintSettings(
            price_levels=30,
            time_window=90,
            show_imbalances=False,
            color_scheme=ColorScheme.CUSTOM,
            custom_buy_color="#AAFFAA",
            custom_sell_color="#FFAAAA",
            show_tooltips=False,
            show_delta=False,
            delta_type="trades"
        )
        assert footprint.price_levels == 30
        assert footprint.time_window == 90
        assert footprint.show_imbalances is False
        assert footprint.color_scheme == ColorScheme.CUSTOM
        assert footprint.custom_buy_color == "#AAFFAA"
        assert footprint.custom_sell_color == "#FFAAAA"
        assert footprint.show_tooltips is False
        assert footprint.show_delta is False
        assert footprint.delta_type == "trades"

    def test_dashboard_settings_and_visualization_settings(self):
        """Test DashboardSettings and VisualizationSettings"""
        
        # Test custom dashboard layout
        custom_layout = [
            [VisualizationType.DEPTH_CHART, VisualizationType.HEATMAP],
            [VisualizationType.TIME_AND_SALES],
            [VisualizationType.LIQUIDITY_MAP, VisualizationType.ORDER_FLOW_FOOTPRINT]
        ]
        
        dashboard = DashboardSettings(
            layout=custom_layout,
            refresh_interval=500,
            show_title=False,
            show_timestamp=False,
            color_scheme=ColorScheme.DARK,
            custom_background_color="#000000",
            custom_text_color="#FFFFFF"
        )
        assert dashboard.layout == custom_layout
        assert dashboard.refresh_interval == 500
        assert dashboard.show_title is False
        assert dashboard.show_timestamp is False
        assert dashboard.color_scheme == ColorScheme.DARK
        assert dashboard.custom_background_color == "#000000"
        assert dashboard.custom_text_color == "#FFFFFF"
        
        # Test VisualizationSettings with custom components
        viz_settings = VisualizationSettings(
            depth_chart=DepthChartSettings(price_levels=15),
            heatmap=HeatmapSettings(time_window=30),
            time_and_sales=TimeAndSalesSettings(max_entries=50),
            liquidity_map=LiquidityMapSettings(price_levels=25),
            order_flow_footprint=OrderFlowFootprintSettings(time_window=45),
            dashboard=dashboard
        )
        assert viz_settings.depth_chart.price_levels == 15
        assert viz_settings.heatmap.time_window == 30
        assert viz_settings.time_and_sales.max_entries == 50
        assert viz_settings.liquidity_map.price_levels == 25
        assert viz_settings.order_flow_footprint.time_window == 45
        assert viz_settings.dashboard.refresh_interval == 500    def test_market_depth_snapshot_comprehensive_properties(self):
        """Test MarketDepthSnapshot comprehensive properties and edge cases"""
        
        now = datetime.now(timezone.utc)
        
        # Test comprehensive snapshot with multiple price levels
        snapshot = MarketDepthSnapshot(
            symbol="GBPUSD",
            timestamp=now,
            bid_prices=[1.3000, 1.2999, 1.2998, 1.2997],
            bid_volumes=[1000.0, 1500.0, 2000.0, 2500.0],
            ask_prices=[1.3001, 1.3002, 1.3003, 1.3004],
            ask_volumes=[800.0, 1200.0, 1600.0, 2000.0],
            trades=[]
        )
        
        # Test all properties
        assert snapshot.best_bid == 1.3000
        assert snapshot.best_ask == 1.3001
        assert snapshot.spread == 0.0001
        assert snapshot.mid_price == 1.30005
        assert snapshot.total_bid_volume == 7000.0
        assert snapshot.total_ask_volume == 5600.0
        
        # Test imbalance ratio calculation
        expected_imbalance = (7000.0 - 5600.0) / (7000.0 + 5600.0)
        assert abs(snapshot.imbalance_ratio - expected_imbalance) < 1e-10
        
        # Test cumulative volumes
        expected_cum_bid = [1000.0, 2500.0, 4500.0, 7000.0]
        expected_cum_ask = [800.0, 2000.0, 3600.0, 5600.0]
        assert snapshot.cumulative_bid_volumes == expected_cum_bid
        assert snapshot.cumulative_ask_volumes == expected_cum_ask
        
        # Test with trades
        trade1 = TradeEntry(
            timestamp=now,
            price=1.3001,
            volume=100.0,
            direction="buy",
            is_large=False
        )
        
        trade2 = TradeEntry(
            timestamp=now,
            price=1.3000,
            volume=200.0,
            direction="sell",
            is_large=True
        )
        
        snapshot_with_trades = MarketDepthSnapshot(
            symbol="GBPUSD",
            timestamp=now,
            bid_prices=[1.3000],
            bid_volumes=[1000.0],
            ask_prices=[1.3001],
            ask_volumes=[800.0],
            trades=[trade1, trade2]
        )
        
        assert len(snapshot_with_trades.trades) == 2
        assert snapshot_with_trades.trades[0].direction == "buy"
        assert snapshot_with_trades.trades[1].direction == "sell"
        assert snapshot_with_trades.trades[0].is_large is False
        assert snapshot_with_trades.trades[1].is_large is True

    def test_market_depth_snapshot_validation_comprehensive(self):
        """Test comprehensive validation scenarios"""
        
        now = datetime.now(timezone.utc)
        
        # Test bid volumes length mismatch
        with pytest.raises(ValueError, match="Bid volumes must have the same length as bid prices"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340, 1.2339, 1.2338],
                bid_volumes=[1000.0, 1500.0],  # Length mismatch
                ask_prices=[1.2341],
                ask_volumes=[800.0]
            )
        
        # Test ask volumes length mismatch
        with pytest.raises(ValueError, match="Ask volumes must have the same length as ask prices"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340],
                bid_volumes=[1000.0],
                ask_prices=[1.2341, 1.2342, 1.2343],
                ask_volumes=[800.0, 1200.0]  # Length mismatch
            )    def test_market_depth_snapshot_edge_cases(self):
        """Test edge cases for MarketDepthSnapshot"""
        
        now = datetime.now(timezone.utc)
        
        # Test zero volume scenario
        zero_volume_snapshot = MarketDepthSnapshot(
            symbol="EURUSD",
            timestamp=now,
            bid_prices=[1.2340],
            bid_volumes=[0.0],
            ask_prices=[1.2341],
            ask_volumes=[0.0]
        )
        
        assert zero_volume_snapshot.total_bid_volume == 0.0
        assert zero_volume_snapshot.total_ask_volume == 0.0
        assert zero_volume_snapshot.imbalance_ratio is None  # Should be None when total is 0
        
        # Test cumulative volumes without numpy
        with patch('src.forex_bot.market_depth_visualizer.models.NUMPY_AVAILABLE', False):
            manual_snapshot = MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340, 1.2339, 1.2338],
                bid_volumes=[100.0, 200.0, 300.0],
                ask_prices=[1.2341, 1.2342, 1.2343],
                ask_volumes=[150.0, 250.0, 350.0]
            )
            
            # Test manual cumulative calculation
            expected_cum_bid = [100.0, 300.0, 600.0]
            expected_cum_ask = [150.0, 400.0, 750.0]
            assert manual_snapshot.cumulative_bid_volumes == expected_cum_bid
            assert manual_snapshot.cumulative_ask_volumes == expected_cum_ask

    def test_market_depth_dashboard_comprehensive(self):
        """Test comprehensive MarketDepthDashboard functionality"""
        
        now = datetime.now(timezone.utc)
        
        # Create multiple visualizations
        viz1 = MarketDepthVisualization(
            symbol="EURUSD",
            timestamp=now,
            visualization_type=VisualizationType.DEPTH_CHART,
            image_data="base64_depth_chart_data",
            settings={"price_levels": 10, "color_scheme": "default"}
        )
        
        viz2 = MarketDepthVisualization(
            symbol="EURUSD",
            timestamp=now,
            visualization_type=VisualizationType.HEATMAP,
            image_data="base64_heatmap_data",
            settings={"time_window": 60, "normalization": "linear"}
        )
        
        viz3 = MarketDepthVisualization(
            symbol="EURUSD",
            timestamp=now,
            visualization_type=VisualizationType.TIME_AND_SALES,
            image_data="base64_time_sales_data",
            settings={"max_entries": 100, "show_direction": True}
        )
        
        dashboard_settings = DashboardSettings(
            refresh_interval=1000,
            show_title=True,
            show_timestamp=True,
            color_scheme=ColorScheme.DARK
        )
        
        # Test comprehensive dashboard
        dashboard = MarketDepthDashboard(
            symbol="EURUSD",
            timestamp=now,
            visualizations={
                VisualizationType.DEPTH_CHART: viz1,
                VisualizationType.HEATMAP: viz2,
                VisualizationType.TIME_AND_SALES: viz3
            },
            settings=dashboard_settings
        )
        
        assert dashboard.symbol == "EURUSD"
        assert dashboard.timestamp == now
        assert len(dashboard.visualizations) == 3
        assert VisualizationType.DEPTH_CHART in dashboard.visualizations
        assert VisualizationType.HEATMAP in dashboard.visualizations
        assert VisualizationType.TIME_AND_SALES in dashboard.visualizations
        assert dashboard.settings.refresh_interval == 1000
        assert dashboard.settings.color_scheme == ColorScheme.DARK

    def test_pydantic_fallback_comprehensive(self):
        """Test comprehensive pydantic fallback behavior"""
        
        # Test with pydantic unavailable
        with patch('src.forex_bot.market_depth_visualizer.models.PYDANTIC_AVAILABLE', False):
            # Test fallback BaseModel functionality
            from src.forex_bot.market_depth_visualizer.models import BaseModel
            
            fallback_model = BaseModel(
                test_attr="test_value",
                _private_attr="hidden",
                public_attr=123
            )
            
            assert fallback_model.test_attr == "test_value"
            assert fallback_model.public_attr == 123
            
            # Test dict method excludes private attributes
            model_dict = fallback_model.dict()
            assert model_dict["test_attr"] == "test_value"
            assert model_dict["public_attr"] == 123
            assert "_private_attr" not in model_dict