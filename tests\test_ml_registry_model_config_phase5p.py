"""
PHASE 5P: Comprehensive tests for ml_registry/model_config.py to achieve 90%+ coverage.
Target: ml_registry/model_config.py (78% → 90%+)
"""

import pytest
import os
from unittest.mock import patch, MagicMock
from typing import Dict, Any


class TestMLRegistryModelConfigPhase5P:
    """Phase 5P: Comprehensive tests for ml_registry/model_config.py."""

    def test_mlflow_config_basic_functionality(self):
        """Test MLflowConfig basic functionality."""
        from src.forex_bot.ml_registry.model_config import MLflowConfig
        
        # Test default values
        config = MLflowConfig()
        assert config.enabled == True
        assert config.tracking_uri == "sqlite:///mlflow.db"
        assert config.registry_uri is None
        assert config.experiment_name == "forex_bot"
        assert config.artifact_location is None
        assert config.log_system_metrics == True
        
        # Test custom values
        custom_config = MLflowConfig(
            enabled=False,
            tracking_uri="http://localhost:5000",
            registry_uri="http://localhost:5001",
            experiment_name="custom_experiment",
            artifact_location="/tmp/artifacts",
            log_system_metrics=False
        )
        assert custom_config.enabled == False
        assert custom_config.tracking_uri == "http://localhost:5000"
        assert custom_config.registry_uri == "http://localhost:5001"
        assert custom_config.experiment_name == "custom_experiment"
        assert custom_config.artifact_location == "/tmp/artifacts"
        assert custom_config.log_system_metrics == False

    def test_model_registry_config_basic_functionality(self):
        """Test ModelRegistryConfig basic functionality."""
        from src.forex_bot.ml_registry.model_config import ModelRegistryConfig
        
        # Test default values
        config = ModelRegistryConfig()
        assert config.enabled == True
        assert config.models_dir == "models"
        assert config.auto_register == True
        assert config.auto_deploy == False
        assert config.deployment_target == "local"
        
        # Test custom values
        custom_config = ModelRegistryConfig(
            enabled=False,
            models_dir="/tmp/models",
            auto_register=False,
            auto_deploy=True,
            deployment_target="remote"
        )
        assert custom_config.enabled == False
        assert custom_config.models_dir == "/tmp/models"
        assert custom_config.auto_register == False
        assert custom_config.auto_deploy == True
        assert custom_config.deployment_target == "remote"

    @patch('os.makedirs')
    @patch('os.path.exists')
    def test_model_registry_config_models_dir_validation(self, mock_exists, mock_makedirs):
        """Test ModelRegistryConfig models_dir validation."""
        from src.forex_bot.ml_registry.model_config import ModelRegistryConfig
        
        # Test when directory doesn't exist
        mock_exists.return_value = False
        config = ModelRegistryConfig(models_dir="/new/models/dir")
        assert config.models_dir == "/new/models/dir"
        mock_makedirs.assert_called_once_with("/new/models/dir", exist_ok=True)
        
        # Test when directory exists
        mock_exists.return_value = True
        mock_makedirs.reset_mock()
        config = ModelRegistryConfig(models_dir="/existing/models/dir")
        assert config.models_dir == "/existing/models/dir"
        mock_makedirs.assert_not_called()

    def test_model_evaluation_config_basic_functionality(self):
        """Test ModelEvaluationConfig basic functionality."""
        from src.forex_bot.ml_registry.model_config import ModelEvaluationConfig
        
        # Test default values
        config = ModelEvaluationConfig()
        assert config.enabled == True
        assert config.metrics == ["accuracy", "precision", "recall", "f1", "roc_auc"]
        assert config.test_size == 0.2
        assert config.cross_validation == True
        assert config.cv_folds == 5
        
        # Test custom values
        custom_config = ModelEvaluationConfig(
            enabled=False,
            metrics=["mse", "mae", "r2"],
            test_size=0.3,
            cross_validation=False,
            cv_folds=10
        )
        assert custom_config.enabled == False
        assert custom_config.metrics == ["mse", "mae", "r2"]
        assert custom_config.test_size == 0.3
        assert custom_config.cross_validation == False
        assert custom_config.cv_folds == 10

    def test_model_evaluation_config_test_size_validation(self):
        """Test ModelEvaluationConfig test_size validation."""
        from src.forex_bot.ml_registry.model_config import ModelEvaluationConfig
        
        # Test valid test sizes
        for test_size in [0.1, 0.2, 0.3, 0.5, 0.8, 0.9]:
            config = ModelEvaluationConfig(test_size=test_size)
            assert config.test_size == test_size
        
        # Test invalid test sizes
        with pytest.raises(ValueError, match="Test size must be between 0.0 and 1.0"):
            ModelEvaluationConfig(test_size=0.0)
        
        with pytest.raises(ValueError, match="Test size must be between 0.0 and 1.0"):
            ModelEvaluationConfig(test_size=1.0)
        
        with pytest.raises(ValueError, match="Test size must be between 0.0 and 1.0"):
            ModelEvaluationConfig(test_size=-0.1)
        
        with pytest.raises(ValueError, match="Test size must be between 0.0 and 1.0"):
            ModelEvaluationConfig(test_size=1.1)

    def test_model_evaluation_config_cv_folds_validation(self):
        """Test ModelEvaluationConfig cv_folds validation."""
        from src.forex_bot.ml_registry.model_config import ModelEvaluationConfig
        
        # Test valid cv_folds
        for cv_folds in [2, 3, 5, 10, 20]:
            config = ModelEvaluationConfig(cv_folds=cv_folds)
            assert config.cv_folds == cv_folds
        
        # Test invalid cv_folds
        with pytest.raises(ValueError, match="CV folds must be at least 2"):
            ModelEvaluationConfig(cv_folds=1)
        
        with pytest.raises(ValueError, match="CV folds must be at least 2"):
            ModelEvaluationConfig(cv_folds=0)
        
        with pytest.raises(ValueError, match="CV folds must be at least 2"):
            ModelEvaluationConfig(cv_folds=-1)

    def test_model_deployment_config_basic_functionality(self):
        """Test ModelDeploymentConfig basic functionality."""
        from src.forex_bot.ml_registry.model_config import ModelDeploymentConfig
        
        # Test default values
        config = ModelDeploymentConfig()
        assert config.enabled == True
        assert config.deployment_mode == "shadow"
        assert config.shadow_mode_ratio == 0.1
        assert config.canary_mode_ratio == 0.1
        assert config.auto_rollback == True
        assert config.rollback_threshold == 0.1
        
        # Test custom values
        custom_config = ModelDeploymentConfig(
            enabled=False,
            deployment_mode="canary",
            shadow_mode_ratio=0.2,
            canary_mode_ratio=0.3,
            auto_rollback=False,
            rollback_threshold=0.05
        )
        assert custom_config.enabled == False
        assert custom_config.deployment_mode == "canary"
        assert custom_config.shadow_mode_ratio == 0.2
        assert custom_config.canary_mode_ratio == 0.3
        assert custom_config.auto_rollback == False
        assert custom_config.rollback_threshold == 0.05

    def test_model_deployment_config_deployment_mode_validation(self):
        """Test ModelDeploymentConfig deployment_mode validation."""
        from src.forex_bot.ml_registry.model_config import ModelDeploymentConfig
        
        # Test valid deployment modes
        for mode in ["shadow", "canary", "blue-green", "direct"]:
            config = ModelDeploymentConfig(deployment_mode=mode)
            assert config.deployment_mode == mode
        
        # Test invalid deployment mode
        with pytest.raises(ValueError, match="Deployment mode must be one of"):
            ModelDeploymentConfig(deployment_mode="invalid")

    def test_model_deployment_config_ratio_validation(self):
        """Test ModelDeploymentConfig ratio validation."""
        from src.forex_bot.ml_registry.model_config import ModelDeploymentConfig
        
        # Test valid ratios
        for ratio in [0.0, 0.1, 0.5, 1.0]:
            config = ModelDeploymentConfig(shadow_mode_ratio=ratio, canary_mode_ratio=ratio)
            assert config.shadow_mode_ratio == ratio
            assert config.canary_mode_ratio == ratio
        
        # Test invalid shadow_mode_ratio
        with pytest.raises(ValueError, match="Ratio must be between 0.0 and 1.0"):
            ModelDeploymentConfig(shadow_mode_ratio=-0.1)
        
        with pytest.raises(ValueError, match="Ratio must be between 0.0 and 1.0"):
            ModelDeploymentConfig(shadow_mode_ratio=1.1)
        
        # Test invalid canary_mode_ratio
        with pytest.raises(ValueError, match="Ratio must be between 0.0 and 1.0"):
            ModelDeploymentConfig(canary_mode_ratio=-0.1)
        
        with pytest.raises(ValueError, match="Ratio must be between 0.0 and 1.0"):
            ModelDeploymentConfig(canary_mode_ratio=1.1)

    def test_model_config_basic_functionality(self):
        """Test ModelConfig basic functionality."""
        from src.forex_bot.ml_registry.model_config import (
            ModelConfig, MLflowConfig, ModelRegistryConfig, 
            ModelEvaluationConfig, ModelDeploymentConfig
        )
        
        # Test default values
        config = ModelConfig()
        assert config.enabled == True
        assert isinstance(config.mlflow, MLflowConfig)
        assert isinstance(config.registry, ModelRegistryConfig)
        assert isinstance(config.evaluation, ModelEvaluationConfig)
        assert isinstance(config.deployment, ModelDeploymentConfig)
        assert isinstance(config.model_types, dict)
        
        # Test custom values
        custom_mlflow = MLflowConfig(enabled=False)
        custom_registry = ModelRegistryConfig(enabled=False)
        custom_evaluation = ModelEvaluationConfig(enabled=False)
        custom_deployment = ModelDeploymentConfig(enabled=False)
        custom_model_types = {"test_model": {"type": "test"}}
        
        custom_config = ModelConfig(
            enabled=False,
            mlflow=custom_mlflow,
            registry=custom_registry,
            evaluation=custom_evaluation,
            deployment=custom_deployment,
            model_types=custom_model_types
        )
        assert custom_config.enabled == False
        assert custom_config.mlflow.enabled == False
        assert custom_config.registry.enabled == False
        assert custom_config.evaluation.enabled == False
        assert custom_config.deployment.enabled == False
        assert custom_config.model_types == custom_model_types

    @patch.dict(os.environ, {}, clear=True)
    def test_get_model_config_default_values(self):
        """Test get_model_config with default values."""
        from src.forex_bot.ml_registry.model_config import get_model_config
        
        # Clear the singleton
        import src.forex_bot.ml_registry.model_config as config_module
        config_module._model_config = None
        
        # Mock get_app_config to avoid dependencies
        with patch('src.forex_bot.ml_registry.model_config.get_app_config') as mock_get_app_config:
            mock_app_config = MagicMock()
            mock_get_app_config.return_value = mock_app_config
            
            config = get_model_config()
            
            # Test default values
            assert config.enabled == True
            assert config.mlflow.enabled == True
            assert config.mlflow.tracking_uri == "sqlite:///mlflow.db"
            assert config.mlflow.registry_uri is None
            assert config.mlflow.experiment_name == "forex_bot"
            assert config.mlflow.artifact_location is None
            assert config.mlflow.log_system_metrics == True
            
            assert config.registry.enabled == True
            assert config.registry.models_dir == "models"
            assert config.registry.auto_register == True
            assert config.registry.auto_deploy == False
            assert config.registry.deployment_target == "local"
            
            assert config.evaluation.enabled == True
            assert config.evaluation.metrics == ["accuracy", "precision", "recall", "f1", "roc_auc"]
            assert config.evaluation.test_size == 0.2
            assert config.evaluation.cross_validation == True
            assert config.evaluation.cv_folds == 5
            
            assert config.deployment.enabled == True
            assert config.deployment.deployment_mode == "shadow"
            assert config.deployment.shadow_mode_ratio == 0.1
            assert config.deployment.canary_mode_ratio == 0.1
            assert config.deployment.auto_rollback == True
            assert config.deployment.rollback_threshold == 0.1
            
            # Test model_types
            assert "trend_classifier" in config.model_types
            assert "volatility_regressor" in config.model_types
            assert "regime_classifier" in config.model_types
            assert "sentiment_classifier" in config.model_types

    @patch.dict(os.environ, {
        "ML_REGISTRY_ENABLED": "false",
        "MLFLOW_ENABLED": "false",
        "MLFLOW_TRACKING_URI": "http://localhost:5000",
        "MLFLOW_REGISTRY_URI": "http://localhost:5001",
        "MLFLOW_EXPERIMENT_NAME": "custom_experiment",
        "MLFLOW_ARTIFACT_LOCATION": "/tmp/artifacts",
        "MLFLOW_LOG_SYSTEM_METRICS": "false",
        "MODEL_REGISTRY_ENABLED": "false",
        "MODEL_REGISTRY_MODELS_DIR": "/tmp/models",
        "MODEL_REGISTRY_AUTO_REGISTER": "false",
        "MODEL_REGISTRY_AUTO_DEPLOY": "true",
        "MODEL_REGISTRY_DEPLOYMENT_TARGET": "remote",
        "MODEL_EVALUATION_ENABLED": "false",
        "MODEL_EVALUATION_METRICS": "mse,mae,r2",
        "MODEL_EVALUATION_TEST_SIZE": "0.3",
        "MODEL_EVALUATION_CROSS_VALIDATION": "false",
        "MODEL_EVALUATION_CV_FOLDS": "10",
        "MODEL_DEPLOYMENT_ENABLED": "false",
        "MODEL_DEPLOYMENT_MODE": "canary",
        "MODEL_DEPLOYMENT_SHADOW_RATIO": "0.2",
        "MODEL_DEPLOYMENT_CANARY_RATIO": "0.3",
        "MODEL_DEPLOYMENT_AUTO_ROLLBACK": "false",
        "MODEL_DEPLOYMENT_ROLLBACK_THRESHOLD": "0.05"
    }, clear=True)
    def test_get_model_config_environment_variables(self):
        """Test get_model_config with environment variables."""
        from src.forex_bot.ml_registry.model_config import get_model_config
        
        # Clear the singleton
        import src.forex_bot.ml_registry.model_config as config_module
        config_module._model_config = None
        
        # Mock get_app_config to avoid dependencies
        with patch('src.forex_bot.ml_registry.model_config.get_app_config') as mock_get_app_config:
            mock_app_config = MagicMock()
            mock_get_app_config.return_value = mock_app_config
            
            config = get_model_config()
            
            # Test environment variable values
            assert config.enabled == False
            assert config.mlflow.enabled == False
            assert config.mlflow.tracking_uri == "http://localhost:5000"
            assert config.mlflow.registry_uri == "http://localhost:5001"
            assert config.mlflow.experiment_name == "custom_experiment"
            assert config.mlflow.artifact_location == "/tmp/artifacts"
            assert config.mlflow.log_system_metrics == False
            
            assert config.registry.enabled == False
            assert config.registry.models_dir == "/tmp/models"
            assert config.registry.auto_register == False
            assert config.registry.auto_deploy == True
            assert config.registry.deployment_target == "remote"
            
            assert config.evaluation.enabled == False
            assert config.evaluation.metrics == ["mse", "mae", "r2"]
            assert config.evaluation.test_size == 0.3
            assert config.evaluation.cross_validation == False
            assert config.evaluation.cv_folds == 10
            
            assert config.deployment.enabled == False
            assert config.deployment.deployment_mode == "canary"
            assert config.deployment.shadow_mode_ratio == 0.2
            assert config.deployment.canary_mode_ratio == 0.3
            assert config.deployment.auto_rollback == False
            assert config.deployment.rollback_threshold == 0.05

    @patch.dict(os.environ, {
        "ML_REGISTRY_ENABLED": "1",
        "MLFLOW_ENABLED": "yes",
        "MLFLOW_LOG_SYSTEM_METRICS": "true",
        "MODEL_REGISTRY_ENABLED": "1",
        "MODEL_REGISTRY_AUTO_REGISTER": "yes",
        "MODEL_REGISTRY_AUTO_DEPLOY": "true",
        "MODEL_EVALUATION_ENABLED": "1",
        "MODEL_EVALUATION_CROSS_VALIDATION": "yes",
        "MODEL_DEPLOYMENT_ENABLED": "true",
        "MODEL_DEPLOYMENT_AUTO_ROLLBACK": "1"
    }, clear=True)
    def test_get_model_config_boolean_variations(self):
        """Test get_model_config with various boolean representations."""
        from src.forex_bot.ml_registry.model_config import get_model_config
        
        # Clear the singleton
        import src.forex_bot.ml_registry.model_config as config_module
        config_module._model_config = None
        
        # Mock get_app_config to avoid dependencies
        with patch('src.forex_bot.ml_registry.model_config.get_app_config') as mock_get_app_config:
            mock_app_config = MagicMock()
            mock_get_app_config.return_value = mock_app_config
            
            config = get_model_config()
            
            # Test that various boolean representations work
            assert config.enabled == True
            assert config.mlflow.enabled == True
            assert config.mlflow.log_system_metrics == True
            assert config.registry.enabled == True
            assert config.registry.auto_register == True
            assert config.registry.auto_deploy == True
            assert config.evaluation.enabled == True
            assert config.evaluation.cross_validation == True
            assert config.deployment.enabled == True
            assert config.deployment.auto_rollback == True

    def test_get_model_config_singleton_behavior(self):
        """Test that get_model_config returns the same instance."""
        from src.forex_bot.ml_registry.model_config import get_model_config
        
        # Clear the singleton
        import src.forex_bot.ml_registry.model_config as config_module
        config_module._model_config = None
        
        # Mock get_app_config to avoid dependencies
        with patch('src.forex_bot.ml_registry.model_config.get_app_config') as mock_get_app_config:
            mock_app_config = MagicMock()
            mock_get_app_config.return_value = mock_app_config
            
            config1 = get_model_config()
            config2 = get_model_config()
            
            # Should be the same instance
            assert config1 is config2
            
            # get_app_config should only be called once due to singleton
            assert mock_get_app_config.call_count == 1

    @patch.dict(os.environ, {
        "MODEL_EVALUATION_TEST_SIZE": "invalid",
        "MODEL_EVALUATION_CV_FOLDS": "invalid",
        "MODEL_DEPLOYMENT_SHADOW_RATIO": "invalid",
        "MODEL_DEPLOYMENT_CANARY_RATIO": "invalid",
        "MODEL_DEPLOYMENT_ROLLBACK_THRESHOLD": "invalid"
    }, clear=True)
    def test_get_model_config_invalid_environment_values(self):
        """Test get_model_config with invalid environment variable values."""
        from src.forex_bot.ml_registry.model_config import get_model_config
        
        # Clear the singleton
        import src.forex_bot.ml_registry.model_config as config_module
        config_module._model_config = None
        
        # Mock get_app_config to avoid dependencies
        with patch('src.forex_bot.ml_registry.model_config.get_app_config') as mock_get_app_config:
            mock_app_config = MagicMock()
            mock_get_app_config.return_value = mock_app_config
            
            # Should raise ValueError for invalid float values
            with pytest.raises(ValueError):
                get_model_config()

    def test_model_types_configuration(self):
        """Test model_types configuration in get_model_config."""
        from src.forex_bot.ml_registry.model_config import get_model_config
        
        # Clear the singleton
        import src.forex_bot.ml_registry.model_config as config_module
        config_module._model_config = None
        
        # Mock get_app_config to avoid dependencies
        with patch('src.forex_bot.ml_registry.model_config.get_app_config') as mock_get_app_config:
            mock_app_config = MagicMock()
            mock_get_app_config.return_value = mock_app_config
            
            config = get_model_config()
            
            # Test trend_classifier configuration
            trend_config = config.model_types["trend_classifier"]
            assert trend_config["type"] == "classifier"
            assert "rsi" in trend_config["features"]
            assert "macd" in trend_config["features"]
            assert trend_config["target"] == "trend"
            assert "n_estimators" in trend_config["hyperparameters"]
            
            # Test volatility_regressor configuration
            volatility_config = config.model_types["volatility_regressor"]
            assert volatility_config["type"] == "regressor"
            assert "atr" in volatility_config["features"]
            assert "volume" in volatility_config["features"]
            assert volatility_config["target"] == "future_volatility"
            
            # Test regime_classifier configuration
            regime_config = config.model_types["regime_classifier"]
            assert regime_config["type"] == "classifier"
            assert "volume" in regime_config["features"]
            assert regime_config["target"] == "regime"
            
            # Test sentiment_classifier configuration
            sentiment_config = config.model_types["sentiment_classifier"]
            assert sentiment_config["type"] == "classifier"
            assert "text_embedding" in sentiment_config["features"]
            assert sentiment_config["target"] == "sentiment"

    def test_config_edge_cases(self):
        """Test edge cases for configuration classes."""
        from src.forex_bot.ml_registry.model_config import (
            MLflowConfig, ModelRegistryConfig, ModelEvaluationConfig, ModelDeploymentConfig
        )
        
        # Test MLflowConfig with None values
        mlflow_config = MLflowConfig(
            registry_uri=None,
            artifact_location=None
        )
        assert mlflow_config.registry_uri is None
        assert mlflow_config.artifact_location is None
        
        # Test ModelEvaluationConfig with empty metrics list
        evaluation_config = ModelEvaluationConfig(metrics=[])
        assert evaluation_config.metrics == []
        
        # Test ModelEvaluationConfig with edge test_size values
        evaluation_config = ModelEvaluationConfig(test_size=0.01)
        assert evaluation_config.test_size == 0.01
        
        evaluation_config = ModelEvaluationConfig(test_size=0.99)
        assert evaluation_config.test_size == 0.99
        
        # Test ModelDeploymentConfig with edge ratio values
        deployment_config = ModelDeploymentConfig(
            shadow_mode_ratio=0.0,
            canary_mode_ratio=1.0
        )
        assert deployment_config.shadow_mode_ratio == 0.0
        assert deployment_config.canary_mode_ratio == 1.0

    def test_config_field_types(self):
        """Test that config fields have correct types."""
        from src.forex_bot.ml_registry.model_config import (
            MLflowConfig, ModelRegistryConfig, ModelEvaluationConfig, 
            ModelDeploymentConfig, ModelConfig
        )
        
        # Test MLflowConfig field types
        mlflow_config = MLflowConfig()
        assert isinstance(mlflow_config.enabled, bool)
        assert isinstance(mlflow_config.tracking_uri, str)
        assert isinstance(mlflow_config.experiment_name, str)
        assert isinstance(mlflow_config.log_system_metrics, bool)
        
        # Test ModelRegistryConfig field types
        registry_config = ModelRegistryConfig()
        assert isinstance(registry_config.enabled, bool)
        assert isinstance(registry_config.models_dir, str)
        assert isinstance(registry_config.auto_register, bool)
        assert isinstance(registry_config.auto_deploy, bool)
        assert isinstance(registry_config.deployment_target, str)
        
        # Test ModelEvaluationConfig field types
        evaluation_config = ModelEvaluationConfig()
        assert isinstance(evaluation_config.enabled, bool)
        assert isinstance(evaluation_config.metrics, list)
        assert isinstance(evaluation_config.test_size, float)
        assert isinstance(evaluation_config.cross_validation, bool)
        assert isinstance(evaluation_config.cv_folds, int)
        
        # Test ModelDeploymentConfig field types
        deployment_config = ModelDeploymentConfig()
        assert isinstance(deployment_config.enabled, bool)
        assert isinstance(deployment_config.deployment_mode, str)
        assert isinstance(deployment_config.shadow_mode_ratio, float)
        assert isinstance(deployment_config.canary_mode_ratio, float)
        assert isinstance(deployment_config.auto_rollback, bool)
        assert isinstance(deployment_config.rollback_threshold, float)
        
        # Test ModelConfig field types
        model_config = ModelConfig()
        assert isinstance(model_config.enabled, bool)
        assert isinstance(model_config.mlflow, MLflowConfig)
        assert isinstance(model_config.registry, ModelRegistryConfig)
        assert isinstance(model_config.evaluation, ModelEvaluationConfig)
        assert isinstance(model_config.deployment, ModelDeploymentConfig)
        assert isinstance(model_config.model_types, dict)