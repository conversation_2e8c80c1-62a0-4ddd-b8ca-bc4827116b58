"""
Phase 6B: Test for market_depth_visualizer/models.py to push from 54% to 90%+ coverage.
Building on Phase 5 work to target all remaining missing areas.
"""

import pytest
from datetime import datetime, timezone
from unittest.mock import patch
from src.forex_bot.market_depth_visualizer.models import (
    MarketDepthSnapshot, MarketDepthVisualization, MarketDepthDashboard,
    DashboardSettings, VisualizationType, ColorScheme
)


class TestMarketDepthPhase6B:
    """Test class to push market_depth_visualizer/models.py to 90%+ coverage"""

    def test_comprehensive_validator_edge_cases(self):
        """Test all validator edge cases and error conditions"""
        
        now = datetime.now(timezone.utc)
        
        # Test TradeEntry volume validator (lines 142-144)
        with pytest.raises(ValueError, match="Volume must be positive"):
            from src.forex_bot.market_depth_visualizer.models import TradeEntry
            TradeEntry(
                timestamp=now,
                price=1.2340,
                volume=-100.0,  # Negative volume
                direction="buy"
            )
        
        # Test MarketDepthSnapshot bid_prices validator (lines 158-160)
        with pytest.raises(ValueError, match="Bid prices cannot be empty"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[],  # Empty bid prices
                bid_volumes=[],
                ask_prices=[1.2341],
                ask_volumes=[800.0]
            )
        
        # Test MarketDepthSnapshot bid_volumes validator (lines 164-166)
        with pytest.raises(ValueError, match="Bid volumes must have the same length as bid prices"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340, 1.2339],
                bid_volumes=[1000.0],  # Different length
                ask_prices=[1.2341],
                ask_volumes=[800.0]
            )
        
        # Test MarketDepthSnapshot ask_prices validator (lines 170-172)
        with pytest.raises(ValueError, match="Ask prices cannot be empty"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340],
                bid_volumes=[1000.0],
                ask_prices=[],  # Empty ask prices
                ask_volumes=[]
            )
        
        # Test MarketDepthSnapshot ask_volumes validator (lines 176-178)
        with pytest.raises(ValueError, match="Ask volumes must have the same length as ask prices"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340],
                bid_volumes=[1000.0],
                ask_prices=[1.2341, 1.2342],
                ask_volumes=[800.0]  # Different length
            )    def test_property_edge_cases(self):
        """Test property edge cases and calculations"""
        
        now = datetime.now(timezone.utc)
        
        # Test best_bid property with multiple prices (line 183)
        snapshot = MarketDepthSnapshot(
            symbol="EURUSD",
            timestamp=now,
            bid_prices=[1.2340, 1.2339, 1.2338],
            bid_volumes=[1000.0, 1500.0, 2000.0],
            ask_prices=[1.2341, 1.2342, 1.2343],
            ask_volumes=[800.0, 1200.0, 1600.0]
        )
        assert snapshot.best_bid == 1.2340  # Highest bid price
        
        # Test best_ask property with multiple prices (line 188)
        assert snapshot.best_ask == 1.2341  # Lowest ask price
        
        # Test spread property calculation (lines 193-195)
        expected_spread = 1.2341 - 1.2340
        assert abs(snapshot.spread - expected_spread) < 1e-10
        
        # Test mid_price property calculation (lines 200-202)
        expected_mid = (1.2340 + 1.2341) / 2
        assert abs(snapshot.mid_price - expected_mid) < 1e-10
        
        # Test total_bid_volume property (line 207)
        expected_bid_volume = 1000.0 + 1500.0 + 2000.0
        assert snapshot.total_bid_volume == expected_bid_volume
        
        # Test total_ask_volume property (line 212)
        expected_ask_volume = 800.0 + 1200.0 + 1600.0
        assert snapshot.total_ask_volume == expected_ask_volume

    def test_imbalance_ratio_edge_cases(self):
        """Test imbalance ratio calculation edge cases"""
        
        now = datetime.now(timezone.utc)
        
        # Test zero total volume edge case (lines 222-225)
        snapshot_zero = MarketDepthSnapshot(
            symbol="GBPUSD",
            timestamp=now,
            bid_prices=[1.3000],
            bid_volumes=[0.0],  # Zero volume
            ask_prices=[1.3001],
            ask_volumes=[0.0]   # Zero volume
        )
        # Should return None for zero total volume
        assert snapshot_zero.imbalance_ratio is None
        
        # Test normal imbalance ratio calculation
        snapshot_normal = MarketDepthSnapshot(
            symbol="GBPUSD",
            timestamp=now,
            bid_prices=[1.3000],
            bid_volumes=[3000.0],
            ask_prices=[1.3001],
            ask_volumes=[1000.0]
        )
        # (3000 - 1000) / (3000 + 1000) = 2000 / 4000 = 0.5
        assert snapshot_normal.imbalance_ratio == 0.5    def test_additional_validator_edge_cases(self):
        """Test additional validator edge cases"""
        
        now = datetime.now(timezone.utc)
        
        # Test MarketDepthVisualization image_data validator (lines 261)
        with pytest.raises(ValueError, match="image_data cannot be empty"):
            MarketDepthVisualization(
                type=VisualizationType.DEPTH_CHART,
                image_data="",  # Empty image data
                metadata={}
            )
        
        # Test MarketDepthDashboard visualizations validator (lines 274)
        with pytest.raises(ValueError, match="Visualizations cannot be empty"):
            MarketDepthDashboard(
                symbol="EURUSD",
                timestamp=now,
                visualizations={},  # Empty visualizations
                settings=DashboardSettings()
            )

    def test_comprehensive_fallback_classes(self):
        """Test all fallback classes when pydantic is unavailable"""
        
        # Test with pydantic unavailable to trigger fallback classes (lines 276-371)
        with patch('src.forex_bot.market_depth_visualizer.models.PYDANTIC_AVAILABLE', False):
            import importlib
            import src.forex_bot.market_depth_visualizer.models as models_module
            importlib.reload(models_module)
            
            # Test fallback DashboardSettings
            dashboard_settings = models_module.DashboardSettings(
                layout=[[models_module.VisualizationType.DEPTH_CHART]],
                refresh_interval=1000,
                show_title=True,
                show_timestamp=True,
                color_scheme=models_module.ColorScheme.DARK
            )
            assert dashboard_settings.refresh_interval == 1000
            assert dashboard_settings.show_title is True
            
            # Test fallback VisualizationSettings
            viz_settings = models_module.VisualizationSettings()
            assert viz_settings.depth_chart is not None
            assert viz_settings.heatmap is not None
            
            # Test fallback MarketDepthSnapshot
            now = datetime.now(timezone.utc)
            snapshot = models_module.MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340],
                bid_volumes=[1000.0],
                ask_prices=[1.2341],
                ask_volumes=[800.0]
            )
            assert snapshot.symbol == "EURUSD"
            assert len(snapshot.bid_prices) == 1
            
            # Test fallback MarketDepthVisualization
            visualization = models_module.MarketDepthVisualization(
                type=models_module.VisualizationType.DEPTH_CHART,
                image_data=b"fake_image_data",
                metadata={"test": "metadata"}
            )
            assert visualization.type == models_module.VisualizationType.DEPTH_CHART
            
            # Test fallback MarketDepthDashboard
            dashboard = models_module.MarketDepthDashboard(
                symbol="GBPUSD",
                timestamp=now,
                visualizations={
                    models_module.VisualizationType.DEPTH_CHART: visualization
                },
                settings=dashboard_settings
            )
            assert dashboard.symbol == "GBPUSD"
            assert len(dashboard.visualizations) == 1