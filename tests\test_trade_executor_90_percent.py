"""
Targeted tests to push trade_executor.py to 90% coverage.

This module specifically targets the remaining uncovered lines (77-104)
in the calculate_position_size method implementation.
"""

import pytest
from unittest.mock import patch, MagicMock, Mock
import logging

from src.forex_bot.trade_executor import TradeExecutor


class TestTradeExecutor90Percent:
    """Targeted tests to achieve 90% coverage for trade_executor.py."""

    @pytest.fixture
    def mock_adapter(self):
        """Create a mock logger adapter."""
        return Mock(spec=logging.LoggerAdapter)

    @pytest.fixture
    def trade_executor(self, mock_adapter):
        """Create a TradeExecutor instance with a mock logger adapter."""
        return TradeExecutor(mock_adapter)

    @pytest.fixture
    def mock_symbol_info(self):
        """Create a mock symbol info object."""
        symbol_info = Mock()
        symbol_info.trade_tick_value = 1.0
        symbol_info.trade_tick_size = 0.00001
        symbol_info.volume_min = 0.01
        symbol_info.volume_max = 10.0
        symbol_info.volume_step = 0.01
        return symbol_info

    @pytest.fixture
    def mock_account_info(self):
        """Create a mock account info object."""
        account_info = Mock()
        account_info.balance = 10000.0
        return account_info

    @patch('src.forex_bot.trade_executor.sizer')
    @patch('src.forex_bot.trade_executor.mt5_client')
    def test_calculate_position_size_successful_calculation(self, mock_mt5_client, mock_sizer, 
                                                          trade_executor, mock_symbol_info, mock_account_info):
        """Test calculate_position_size with successful calculation - covers lines 77-95."""
        # Setup mocks
        mock_mt5_client.mt5.account_info.return_value = mock_account_info
        mock_sizer.DEFAULT_ELDER_RISK_PERCENT = 0.02
        mock_sizer.calculate_position_size.return_value = 0.1
        
        # Test parameters
        symbol = "EURUSD"
        atr = 0.0010
        point = 0.00001
        trade_type = 0  # BUY
        hmm_info = {"state_label": "Bullish", "probability": 0.75}
        
        # Call the method (this should cover lines 77-95)
        result = trade_executor.calculate_position_size(symbol, mock_symbol_info, atr, point, trade_type, hmm_info)
        
        # Verify result
        assert result == 0.1
        
        # Verify the sizer was called with correct arguments
        mock_sizer.calculate_position_size.assert_called_once()
        call_args = mock_sizer.calculate_position_size.call_args[1]  # Get keyword arguments
        
        assert call_args['method'] == 'elder'
        assert call_args['account_equity'] == 10000.0
        assert call_args['risk_percent'] == 0.02
        assert call_args['stop_loss_pips'] == 100.0  # atr / point = 0.0010 / 0.00001
        assert call_args['pip_value_per_lot'] == 100000.0  # trade_tick_value / trade_tick_size * point
        assert call_args['min_volume'] == 0.01
        assert call_args['max_volume'] == 10.0
        assert call_args['volume_step'] == 0.01
        assert call_args['symbol'] == symbol
        assert call_args['adapter'] == trade_executor.adapter
        assert call_args['current_hmm_state'] == "Bullish"

    @patch('src.forex_bot.trade_executor.sizer')
    @patch('src.forex_bot.trade_executor.mt5_client')
    def test_calculate_position_size_invalid_pip_value(self, mock_mt5_client, mock_sizer, 
                                                     trade_executor, mock_account_info):
        """Test calculate_position_size with invalid pip value - covers lines 76-79."""
        # Setup symbol info with invalid trade_tick_size
        symbol_info = Mock()
        symbol_info.trade_tick_value = 1.0
        symbol_info.trade_tick_size = 0.0  # Invalid - will cause pip_value <= 0
        symbol_info.volume_min = 0.01
        symbol_info.volume_max = 10.0
        symbol_info.volume_step = 0.01
        
        mock_mt5_client.mt5.account_info.return_value = mock_account_info
        
        # Test parameters
        symbol = "EURUSD"
        atr = 0.0010
        point = 0.00001
        trade_type = 0  # BUY
        hmm_info = None
        
        # Call the method (this should cover lines 76-79)
        result = trade_executor.calculate_position_size(symbol, symbol_info, atr, point, trade_type, hmm_info)
        
        # Verify result is None due to invalid pip value
        assert result is None
        
        # Verify error was logged
        trade_executor.adapter.error.assert_called_once_with(f"Invalid pip value for {symbol}. Skip.")

    @patch('src.forex_bot.trade_executor.sizer')
    @patch('src.forex_bot.trade_executor.mt5_client')
    def test_calculate_position_size_zero_calculated_volume(self, mock_mt5_client, mock_sizer, 
                                                          trade_executor, mock_symbol_info, mock_account_info):
        """Test calculate_position_size with zero calculated volume - covers lines 97-100."""
        # Setup mocks
        mock_mt5_client.mt5.account_info.return_value = mock_account_info
        mock_sizer.DEFAULT_ELDER_RISK_PERCENT = 0.02
        mock_sizer.calculate_position_size.return_value = 0.0  # Zero volume
        
        # Test parameters
        symbol = "EURUSD"
        atr = 0.0010
        point = 0.00001
        trade_type = 0  # BUY
        hmm_info = None
        
        # Call the method (this should cover lines 97-100)
        result = trade_executor.calculate_position_size(symbol, mock_symbol_info, atr, point, trade_type, hmm_info)
        
        # Verify result is None due to zero volume
        assert result is None
        
        # Verify warning was logged
        trade_executor.adapter.warning.assert_called_once_with(
            f"Position size calculation failed or zero volume for {symbol}."
        )

    @patch('src.forex_bot.trade_executor.sizer')
    @patch('src.forex_bot.trade_executor.mt5_client')
    def test_calculate_position_size_none_calculated_volume(self, mock_mt5_client, mock_sizer, 
                                                          trade_executor, mock_symbol_info, mock_account_info):
        """Test calculate_position_size with None calculated volume - covers lines 97-100."""
        # Setup mocks
        mock_mt5_client.mt5.account_info.return_value = mock_account_info
        mock_sizer.DEFAULT_ELDER_RISK_PERCENT = 0.02
        mock_sizer.calculate_position_size.return_value = None  # None volume
        
        # Test parameters
        symbol = "EURUSD"
        atr = 0.0010
        point = 0.00001
        trade_type = 0  # BUY
        hmm_info = {"state_label": "Bearish"}
        
        # Call the method (this should cover lines 97-100)
        result = trade_executor.calculate_position_size(symbol, mock_symbol_info, atr, point, trade_type, hmm_info)
        
        # Verify result is None
        assert result is None
        
        # Verify warning was logged
        trade_executor.adapter.warning.assert_called_once_with(
            f"Position size calculation failed or zero volume for {symbol}."
        )

    @patch('src.forex_bot.trade_executor.sizer')
    @patch('src.forex_bot.trade_executor.mt5_client')
    def test_calculate_position_size_exception_handling(self, mock_mt5_client, mock_sizer, 
                                                       trade_executor, mock_symbol_info):
        """Test calculate_position_size exception handling - covers lines 103-104."""
        # Setup mocks to raise exception
        mock_mt5_client.mt5.account_info.side_effect = Exception("Account info error")
        
        # Test parameters
        symbol = "EURUSD"
        atr = 0.0010
        point = 0.00001
        trade_type = 0  # BUY
        hmm_info = None
        
        # Call the method (this should cover lines 103-104)
        result = trade_executor.calculate_position_size(symbol, mock_symbol_info, atr, point, trade_type, hmm_info)
        
        # Verify result is None due to exception
        assert result is None
        
        # Verify exception was logged
        trade_executor.adapter.exception.assert_called_once_with(
            f"Error calculating position size for {symbol}: Account info error"
        )

    @patch('src.forex_bot.trade_executor.sizer')
    @patch('src.forex_bot.trade_executor.mt5_client')
    def test_calculate_position_size_zero_point_value(self, mock_mt5_client, mock_sizer, 
                                                    trade_executor, mock_symbol_info, mock_account_info):
        """Test calculate_position_size with zero point value - covers edge case in line 87."""
        # Setup mocks
        mock_mt5_client.mt5.account_info.return_value = mock_account_info
        mock_sizer.DEFAULT_ELDER_RISK_PERCENT = 0.02
        mock_sizer.calculate_position_size.return_value = 0.1
        
        # Test parameters
        symbol = "EURUSD"
        atr = 0.0010
        point = 0.0  # Zero point value
        trade_type = 0  # BUY
        hmm_info = None
        
        # Call the method (this should cover the edge case in line 87)
        result = trade_executor.calculate_position_size(symbol, mock_symbol_info, atr, point, trade_type, hmm_info)
        
        # Verify result
        assert result == 0.1
        
        # Verify the sizer was called with stop_loss_pips = 0 (due to point = 0)
        mock_sizer.calculate_position_size.assert_called_once()
        call_args = mock_sizer.calculate_position_size.call_args[1]
        assert call_args['stop_loss_pips'] == 0

    @patch('src.forex_bot.trade_executor.sizer')
    @patch('src.forex_bot.trade_executor.mt5_client')
    def test_calculate_position_size_no_hmm_info(self, mock_mt5_client, mock_sizer, 
                                                trade_executor, mock_symbol_info, mock_account_info):
        """Test calculate_position_size with no HMM info - covers line 95."""
        # Setup mocks
        mock_mt5_client.mt5.account_info.return_value = mock_account_info
        mock_sizer.DEFAULT_ELDER_RISK_PERCENT = 0.02
        mock_sizer.calculate_position_size.return_value = 0.1
        
        # Test parameters
        symbol = "EURUSD"
        atr = 0.0010
        point = 0.00001
        trade_type = 0  # BUY
        hmm_info = None  # No HMM info
        
        # Call the method (this should cover line 95 with None HMM info)
        result = trade_executor.calculate_position_size(symbol, mock_symbol_info, atr, point, trade_type, hmm_info)
        
        # Verify result
        assert result == 0.1
        
        # Verify the sizer was called with current_hmm_state = None
        mock_sizer.calculate_position_size.assert_called_once()
        call_args = mock_sizer.calculate_position_size.call_args[1]
        assert call_args['current_hmm_state'] is None

    @patch('src.forex_bot.trade_executor.sizer')
    @patch('src.forex_bot.trade_executor.mt5_client')
    def test_calculate_position_size_hmm_info_without_state_label(self, mock_mt5_client, mock_sizer, 
                                                                trade_executor, mock_symbol_info, mock_account_info):
        """Test calculate_position_size with HMM info but no state_label - covers line 95."""
        # Setup mocks
        mock_mt5_client.mt5.account_info.return_value = mock_account_info
        mock_sizer.DEFAULT_ELDER_RISK_PERCENT = 0.02
        mock_sizer.calculate_position_size.return_value = 0.1
        
        # Test parameters
        symbol = "EURUSD"
        atr = 0.0010
        point = 0.00001
        trade_type = 0  # BUY
        hmm_info = {"probability": 0.75}  # HMM info without state_label
        
        # Call the method (this should cover line 95 with HMM info but no state_label)
        result = trade_executor.calculate_position_size(symbol, mock_symbol_info, atr, point, trade_type, hmm_info)
        
        # Verify result
        assert result == 0.1
        
        # Verify the sizer was called with current_hmm_state = None (due to missing state_label)
        mock_sizer.calculate_position_size.assert_called_once()
        call_args = mock_sizer.calculate_position_size.call_args[1]
        assert call_args['current_hmm_state'] is None

    @patch('src.forex_bot.trade_executor.sizer')
    @patch('src.forex_bot.trade_executor.mt5_client')
    def test_calculate_position_size_negative_calculated_volume(self, mock_mt5_client, mock_sizer, 
                                                              trade_executor, mock_symbol_info, mock_account_info):
        """Test calculate_position_size with negative calculated volume - covers lines 97-100."""
        # Setup mocks
        mock_mt5_client.mt5.account_info.return_value = mock_account_info
        mock_sizer.DEFAULT_ELDER_RISK_PERCENT = 0.02
        mock_sizer.calculate_position_size.return_value = -0.1  # Negative volume
        
        # Test parameters
        symbol = "EURUSD"
        atr = 0.0010
        point = 0.00001
        trade_type = 0  # BUY
        hmm_info = None
        
        # Call the method (this should cover lines 97-100)
        result = trade_executor.calculate_position_size(symbol, mock_symbol_info, atr, point, trade_type, hmm_info)
        
        # Verify result is None due to negative volume
        assert result is None
        
        # Verify warning was logged
        trade_executor.adapter.warning.assert_called_once_with(
            f"Position size calculation failed or zero volume for {symbol}."
        )