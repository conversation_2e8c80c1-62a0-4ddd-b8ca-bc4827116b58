"""
Phase 5A comprehensive tests to push order_flow_analyzer/models.py to 90%+ coverage.
"""

import pytest
from datetime import datetime, timezone

class TestOrderFlowAnalyzerModelsPhase5:
    """Phase 5A tests to achieve 90%+ coverage for order_flow_analyzer/models.py."""

    def test_pydantic_order_flow_imbalance_comprehensive_validation(self):
        """Test all pydantic OrderFlowImbalance validation scenarios."""
        from src.forex_bot.order_flow_analyzer import models
        
        timestamp = datetime.now(timezone.utc)
        
        # Test successful creation with all enum values
        for level in [models.ImbalanceLevel.EXTREME_BUY, models.ImbalanceLevel.STRONG_BUY, 
                     models.ImbalanceLevel.MODERATE_BUY, models.ImbalanceLevel.NEUTRAL, 
                     models.ImbalanceLevel.MODERATE_SELL, models.ImbalanceLevel.STRONG_SELL]:
            imbalance = models.OrderFlowImbalance(
                symbol='EURUSD',
                timestamp=timestamp,
                price_level=1.1000,
                bid_volume=1000.0,
                ask_volume=500.0,
                imbalance_ratio=0.5,
                imbalance_level=level,
                is_significant=True,
                distance=0.001
            )
            assert imbalance.imbalance_level == level
        
        # Test boundary values for imbalance_ratio
        boundary_tests = [
            (-1.0, True),   # Valid boundary
            (1.0, True),    # Valid boundary  
            (0.0, True),    # Valid middle
            (-0.999, True), # Valid near boundary
            (0.999, True),  # Valid near boundary
            (-1.1, False),  # Invalid
            (1.1, False),   # Invalid
        ]
        
        for ratio, should_pass in boundary_tests:
            if should_pass:
                models.OrderFlowImbalance(
                    symbol='EUR', timestamp=timestamp, price_level=1.0,
                    bid_volume=100, ask_volume=100, imbalance_ratio=ratio,
                    imbalance_level=models.ImbalanceLevel.NEUTRAL, is_significant=True
                )
            else:
                with pytest.raises(ValueError, match="Imbalance ratio must be between -1.0 and 1.0"):
                    models.OrderFlowImbalance(
                        symbol='EUR', timestamp=timestamp, price_level=1.0,
                        bid_volume=100, ask_volume=100, imbalance_ratio=ratio,
                        imbalance_level=models.ImbalanceLevel.NEUTRAL, is_significant=True
                    )    def test_large_order_comprehensive_validation(self):
        """Test all LargeOrder validation scenarios and edge cases."""
        from src.forex_bot.order_flow_analyzer import models
        
        timestamp = datetime.now(timezone.utc)
        
        # Test successful creation with different types
        for order_type in ['bid', 'ask']:
            large_order = models.LargeOrder(
                symbol='GBPUSD',
                timestamp=timestamp,
                price_level=1.3000,
                volume=5000.0,
                type=order_type,
                is_market_moving=True,
                standard_deviations=3.5
            )
            assert large_order.type == order_type
        
        # Test volume boundary validation
        volume_tests = [
            (0.001, True),    # Valid small volume
            (1000000.0, True), # Valid large volume
            (0.0, False),     # Invalid zero
            (-100.0, False),  # Invalid negative
        ]
        
        for volume, should_pass in volume_tests:
            if should_pass:
                models.LargeOrder(
                    symbol='GBP', timestamp=timestamp, price_level=1.0,
                    volume=volume, type='bid', is_market_moving=True, standard_deviations=2.0
                )
            else:
                with pytest.raises(ValueError, match="Volume must be positive"):
                    models.LargeOrder(
                        symbol='GBP', timestamp=timestamp, price_level=1.0,
                        volume=volume, type='bid', is_market_moving=True, standard_deviations=2.0
                    )
        
        # Test standard_deviations boundary validation
        std_dev_tests = [
            (0.001, True),    # Valid small
            (10.0, True),     # Valid large
            (0.0, False),     # Invalid zero
            (-1.0, False),    # Invalid negative
        ]
        
        for std_dev, should_pass in std_dev_tests:
            if should_pass:
                models.LargeOrder(
                    symbol='GBP', timestamp=timestamp, price_level=1.0,
                    volume=100.0, type='bid', is_market_moving=True, standard_deviations=std_dev
                )
            else:
                with pytest.raises(ValueError, match="Standard deviations must be positive"):
                    models.LargeOrder(
                        symbol='GBP', timestamp=timestamp, price_level=1.0,
                        volume=100.0, type='bid', is_market_moving=True, standard_deviations=std_dev
                    )
        
        # Test type validation
        invalid_types = ['invalid', 'buy', 'sell', '', None]
        for invalid_type in invalid_types:
            with pytest.raises(ValueError, match='Type must be "bid" or "ask"'):
                models.LargeOrder(
                    symbol='GBP', timestamp=timestamp, price_level=1.0,
                    volume=100.0, type=invalid_type, is_market_moving=True, standard_deviations=2.0
                )    def test_support_resistance_level_comprehensive_validation(self):
        """Test all SupportResistanceLevel validation scenarios."""
        from src.forex_bot.order_flow_analyzer import models
        
        timestamp = datetime.now(timezone.utc)
        
        # Test successful creation with different types
        for sr_type in ['support', 'resistance']:
            sr_level = models.SupportResistanceLevel(
                symbol='USDJPY',
                timestamp=timestamp,
                price_level=110.00,
                type=sr_type,
                strength=0.8,
                volume_concentration=1500.0,
                is_active=True
            )
            assert sr_level.type == sr_type
        
        # Test strength boundary validation
        strength_tests = [
            (0.0, True),      # Valid boundary
            (1.0, True),      # Valid boundary
            (0.5, True),      # Valid middle
            (0.001, True),    # Valid small
            (0.999, True),    # Valid near boundary
            (-0.1, False),    # Invalid negative
            (1.1, False),     # Invalid over 1.0
        ]
        
        for strength, should_pass in strength_tests:
            if should_pass:
                models.SupportResistanceLevel(
                    symbol='USD', timestamp=timestamp, price_level=110.0,
                    type='support', strength=strength, volume_concentration=100.0, is_active=True
                )
            else:
                with pytest.raises(ValueError, match="Strength must be between 0.0 and 1.0"):
                    models.SupportResistanceLevel(
                        symbol='USD', timestamp=timestamp, price_level=110.0,
                        type='support', strength=strength, volume_concentration=100.0, is_active=True
                    )
        
        # Test volume_concentration validation
        volume_tests = [
            (0.001, True),    # Valid small
            (10000.0, True),  # Valid large
            (0.0, False),     # Invalid zero
            (-100.0, False),  # Invalid negative
        ]
        
        for volume, should_pass in volume_tests:
            if should_pass:
                models.SupportResistanceLevel(
                    symbol='USD', timestamp=timestamp, price_level=110.0,
                    type='support', strength=0.5, volume_concentration=volume, is_active=True
                )
            else:
                with pytest.raises(ValueError, match="Volume concentration must be positive"):
                    models.SupportResistanceLevel(
                        symbol='USD', timestamp=timestamp, price_level=110.0,
                        type='support', strength=0.5, volume_concentration=volume, is_active=True
                    )
        
        # Test type validation
        invalid_types = ['invalid', 'buy', 'sell', '', None]
        for invalid_type in invalid_types:
            with pytest.raises(ValueError, match='Type must be "support" or "resistance"'):
                models.SupportResistanceLevel(
                    symbol='USD', timestamp=timestamp, price_level=110.0,
                    type=invalid_type, strength=0.5, volume_concentration=100.0, is_active=True
                )    def test_order_flow_analysis_comprehensive_validation(self):
        """Test all OrderFlowAnalysis validation scenarios."""
        from src.forex_bot.order_flow_analyzer import models
        
        timestamp = datetime.now(timezone.utc)
        
        # Create sample objects for testing
        imbalance = models.OrderFlowImbalance(
            symbol='TEST',
            timestamp=timestamp,
            price_level=1.0,
            bid_volume=100.0,
            ask_volume=50.0,
            imbalance_ratio=0.33,
            imbalance_level=models.ImbalanceLevel.MODERATE_BUY,
            is_significant=True
        )
        
        large_order = models.LargeOrder(
            symbol='TEST',
            timestamp=timestamp,
            price_level=1.0,
            volume=1000.0,
            type='bid',
            is_market_moving=True,
            standard_deviations=2.5
        )
        
        sr_level = models.SupportResistanceLevel(
            symbol='TEST',
            timestamp=timestamp,
            price_level=1.0,
            type='support',
            strength=0.8,
            volume_concentration=500.0,
            is_active=True
        )
        
        # Test successful creation with all components
        analysis = models.OrderFlowAnalysis(
            symbol='EURUSD',
            timestamp=timestamp,
            imbalances=[imbalance],
            large_orders=[large_order],
            support_resistance_levels=[sr_level],
            overall_sentiment='bullish',
            confidence=0.85
        )
        
        assert analysis.symbol == 'EURUSD'
        assert analysis.confidence == 0.85
        assert len(analysis.imbalances) == 1
        assert len(analysis.large_orders) == 1
        assert len(analysis.support_resistance_levels) == 1
        
        # Test confidence boundary validation
        confidence_tests = [
            (0.0, True),      # Valid boundary
            (1.0, True),      # Valid boundary
            (0.5, True),      # Valid middle
            (0.001, True),    # Valid small
            (0.999, True),    # Valid near boundary
            (-0.1, False),    # Invalid negative
            (1.1, False),     # Invalid over 1.0
        ]
        
        for confidence, should_pass in confidence_tests:
            if should_pass:
                models.OrderFlowAnalysis(
                    symbol='TEST', timestamp=timestamp, imbalances=[], large_orders=[],
                    support_resistance_levels=[], overall_sentiment='neutral', confidence=confidence
                )
            else:
                with pytest.raises(ValueError, match="Confidence must be between 0.0 and 1.0"):
                    models.OrderFlowAnalysis(
                        symbol='TEST', timestamp=timestamp, imbalances=[], large_orders=[],
                        support_resistance_levels=[], overall_sentiment='neutral', confidence=confidence
                    )
        
        # Test with empty lists
        empty_analysis = models.OrderFlowAnalysis(
            symbol='EMPTY',
            timestamp=timestamp,
            imbalances=[],
            large_orders=[],
            support_resistance_levels=[],
            overall_sentiment='neutral',
            confidence=0.0
        )
        
        assert len(empty_analysis.imbalances) == 0
        assert len(empty_analysis.large_orders) == 0
        assert len(empty_analysis.support_resistance_levels) == 0    def test_all_non_pydantic_implementations_comprehensive(self):
        """Test all non-pydantic implementations to cover lines 127-227."""
        from src.forex_bot.order_flow_analyzer import models
        
        timestamp = datetime.now(timezone.utc)
        
        # Test all possible enum values for ImbalanceLevel
        for level_name in ['EXTREME_BUY', 'STRONG_BUY', 'MODERATE_BUY', 'NEUTRAL', 'MODERATE_SELL', 'STRONG_SELL']:
            level = getattr(models.ImbalanceLevel, level_name)
            imbalance = models.OrderFlowImbalance(
                symbol='TEST',
                timestamp=timestamp,
                price_level=1.0,
                bid_volume=100.0,
                ask_volume=100.0,
                imbalance_ratio=0.0,
                imbalance_level=level,
                is_significant=False
            )
            assert imbalance.imbalance_level == level
        
        # Test all edge cases for OrderFlowImbalance
        edge_imbalances = [
            # Extreme values
            {'symbol': 'EXTREME', 'price_level': 999999.99, 'bid_volume': 1000000.0, 'ask_volume': 1000000.0, 'imbalance_ratio': 1.0},
            {'symbol': 'MINIMAL', 'price_level': 0.0001, 'bid_volume': 0.01, 'ask_volume': 0.01, 'imbalance_ratio': -1.0},
            # Zero values where allowed
            {'symbol': 'ZERO', 'price_level': 0.0, 'bid_volume': 0.0, 'ask_volume': 0.0, 'imbalance_ratio': 0.0},
        ]
        
        for edge_case in edge_imbalances:
            imbalance = models.OrderFlowImbalance(
                symbol=edge_case['symbol'],
                timestamp=timestamp,
                price_level=edge_case['price_level'],
                bid_volume=edge_case['bid_volume'],
                ask_volume=edge_case['ask_volume'],
                imbalance_ratio=edge_case['imbalance_ratio'],
                imbalance_level=models.ImbalanceLevel.NEUTRAL,
                is_significant=True
            )
            assert imbalance.symbol == edge_case['symbol']
        
        # Test all edge cases for LargeOrder
        edge_orders = [
            # Different types and extreme values
            {'symbol': 'BID_EXTREME', 'volume': 999999.99, 'type': 'bid', 'standard_deviations': 10.0},
            {'symbol': 'ASK_MINIMAL', 'volume': 0.001, 'type': 'ask', 'standard_deviations': 0.001},
        ]
        
        for edge_case in edge_orders:
            large_order = models.LargeOrder(
                symbol=edge_case['symbol'],
                timestamp=timestamp,
                price_level=1.0,
                volume=edge_case['volume'],
                type=edge_case['type'],
                is_market_moving=False,
                standard_deviations=edge_case['standard_deviations']
            )
            assert large_order.symbol == edge_case['symbol']
            assert large_order.type == edge_case['type']
        
        # Test all edge cases for SupportResistanceLevel
        edge_sr_levels = [
            # Different types and extreme values
            {'symbol': 'SUPPORT_MAX', 'type': 'support', 'strength': 1.0, 'volume_concentration': 999999.99},
            {'symbol': 'RESISTANCE_MIN', 'type': 'resistance', 'strength': 0.0, 'volume_concentration': 0.001},
        ]
        
        for edge_case in edge_sr_levels:
            sr_level = models.SupportResistanceLevel(
                symbol=edge_case['symbol'],
                timestamp=timestamp,
                price_level=1.0,
                type=edge_case['type'],
                strength=edge_case['strength'],
                volume_concentration=edge_case['volume_concentration'],
                is_active=False
            )
            assert sr_level.symbol == edge_case['symbol']
            assert sr_level.type == edge_case['type']    def test_comprehensive_integration_and_edge_cases(self):
        """Test comprehensive integration scenarios and all remaining edge cases."""
        from src.forex_bot.order_flow_analyzer import models
        
        timestamp = datetime.now(timezone.utc)
        
        # Test OrderFlowAnalysis with maximum complexity
        complex_imbalances = []
        complex_large_orders = []
        complex_sr_levels = []
        
        # Create multiple imbalances with different levels
        for i, level in enumerate([models.ImbalanceLevel.EXTREME_BUY, models.ImbalanceLevel.STRONG_SELL]):
            imbalance = models.OrderFlowImbalance(
                symbol=f'COMPLEX_{i}',
                timestamp=timestamp,
                price_level=1.0 + i * 0.1,
                bid_volume=100.0 + i * 50,
                ask_volume=50.0 + i * 25,
                imbalance_ratio=0.5 - i * 0.1,
                imbalance_level=level,
                is_significant=i % 2 == 0,
                distance=0.001 * (i + 1)
            )
            complex_imbalances.append(imbalance)
        
        # Create multiple large orders with different types
        for i, order_type in enumerate(['bid', 'ask']):
            large_order = models.LargeOrder(
                symbol=f'COMPLEX_{i}',
                timestamp=timestamp,
                price_level=1.0 + i * 0.1,
                volume=1000.0 + i * 500,
                type=order_type,
                is_market_moving=i % 2 == 0,
                standard_deviations=2.0 + i * 0.5
            )
            complex_large_orders.append(large_order)
        
        # Create multiple support/resistance levels
        for i, sr_type in enumerate(['support', 'resistance']):
            sr_level = models.SupportResistanceLevel(
                symbol=f'COMPLEX_{i}',
                timestamp=timestamp,
                price_level=1.0 + i * 0.1,
                type=sr_type,
                strength=0.5 + i * 0.2,
                volume_concentration=500.0 + i * 250,
                is_active=i % 2 == 0
            )
            complex_sr_levels.append(sr_level)
        
        # Test complex OrderFlowAnalysis
        complex_analysis = models.OrderFlowAnalysis(
            symbol='COMPLEX_ANALYSIS',
            timestamp=timestamp,
            imbalances=complex_imbalances,
            large_orders=complex_large_orders,
            support_resistance_levels=complex_sr_levels,
            overall_sentiment='mixed',
            confidence=0.75
        )
        
        assert complex_analysis.symbol == 'COMPLEX_ANALYSIS'
        assert len(complex_analysis.imbalances) == 2
        assert len(complex_analysis.large_orders) == 2
        assert len(complex_analysis.support_resistance_levels) == 2
        assert complex_analysis.overall_sentiment == 'mixed'
        assert complex_analysis.confidence == 0.75
        
        # Test all sentiment values
        sentiment_values = ['bullish', 'bearish', 'neutral', 'mixed', 'uncertain']
        for sentiment in sentiment_values:
            analysis = models.OrderFlowAnalysis(
                symbol='SENTIMENT_TEST',
                timestamp=timestamp,
                imbalances=[],
                large_orders=[],
                support_resistance_levels=[],
                overall_sentiment=sentiment,
                confidence=0.5
            )
            assert analysis.overall_sentiment == sentiment
        
        # Test object attribute access and methods
        test_imbalance = complex_imbalances[0]
        assert hasattr(test_imbalance, 'symbol')
        assert hasattr(test_imbalance, 'timestamp')
        assert hasattr(test_imbalance, 'price_level')
        assert hasattr(test_imbalance, 'bid_volume')
        assert hasattr(test_imbalance, 'ask_volume')
        assert hasattr(test_imbalance, 'imbalance_ratio')
        assert hasattr(test_imbalance, 'imbalance_level')
        assert hasattr(test_imbalance, 'is_significant')
        
        test_large_order = complex_large_orders[0]
        assert hasattr(test_large_order, 'symbol')
        assert hasattr(test_large_order, 'timestamp')
        assert hasattr(test_large_order, 'price_level')
        assert hasattr(test_large_order, 'volume')
        assert hasattr(test_large_order, 'type')
        assert hasattr(test_large_order, 'is_market_moving')
        assert hasattr(test_large_order, 'standard_deviations')
        
        test_sr_level = complex_sr_levels[0]
        assert hasattr(test_sr_level, 'symbol')
        assert hasattr(test_sr_level, 'timestamp')
        assert hasattr(test_sr_level, 'price_level')
        assert hasattr(test_sr_level, 'type')
        assert hasattr(test_sr_level, 'strength')
        assert hasattr(test_sr_level, 'volume_concentration')
        assert hasattr(test_sr_level, 'is_active')