<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QA Wolf - Forex Bot Dashboard</title>
    <script src="https://cdn.socket.io/4.0.0/socket.io.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
            margin: 0; 
            padding: 20px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .header { 
            background: rgba(44, 62, 80, 0.9); 
            color: white; 
            padding: 30px; 
            border-radius: 12px; 
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .status-grid { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); 
            gap: 25px; 
            margin-bottom: 30px;
        }
        .status-card { 
            background: rgba(255, 255, 255, 0.95); 
            padding: 25px; 
            border-radius: 12px; 
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease;
        }
        .status-card:hover {
            transform: translateY(-5px);
        }
        .status-value { 
            font-size: 2.2em; 
            font-weight: bold; 
            color: #27ae60; 
            margin: 10px 0;
        }
        .status-label { 
            color: #7f8c8d; 
            margin-bottom: 10px; 
            font-weight: 500;
            text-transform: uppercase;
            font-size: 0.9em;
            letter-spacing: 1px;
        }
        .alert { 
            padding: 15px; 
            margin: 10px 0; 
            border-radius: 8px;
            border-left: 4px solid;
        }
        .alert-info { 
            background: rgba(212, 237, 218, 0.9); 
            border-left-color: #28a745; 
            color: #155724; 
        }
        .alert-warning { 
            background: rgba(255, 243, 205, 0.9); 
            border-left-color: #ffc107; 
            color: #856404; 
        }
        .alert-danger { 
            background: rgba(248, 215, 218, 0.9); 
            border-left-color: #dc3545; 
            color: #721c24; 
        }
        .chart-container { 
            height: 350px; 
            margin: 20px 0; 
            position: relative;
        }
        .safety-indicator { 
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            color: white; 
            padding: 8px 16px; 
            border-radius: 25px; 
            font-size: 0.9em;
            font-weight: 500;
            display: inline-block;
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        }
        .metric-icon {
            font-size: 1.5em;
            margin-right: 10px;
            vertical-align: middle;
        }
        .status-active { color: #27ae60; }
        .status-warning { color: #f39c12; }
        .status-danger { color: #e74c3c; }
        .connection-status {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 20px;
            color: white;
            font-weight: 500;
            z-index: 1000;
        }
        .connected { background: #27ae60; }
        .disconnected { background: #e74c3c; }
        .large-card {
            grid-column: 1 / -1;
        }
    </style>
</head>
<body>
    <div id="connection-status" class="connection-status disconnected">
        🔴 Connecting...
    </div>

    <div class="header">
        <h1>🚀 QA Wolf - Forex Trading Bot Dashboard</h1>
        <p>Real-time monitoring with <span class="safety-indicator">🛡️ ZERO TRADING IMPACT</span></p>
        <p id="last-update">Last Update: Initializing...</p>
    </div>
    
    <div class="status-grid">
        <div class="status-card">
            <div class="status-label">
                <span class="metric-icon">🤖</span>Trading Bot Status
            </div>
            <div class="status-value" id="bot-status">CHECKING...</div>
            <div id="bot-details" style="font-size: 0.9em; color: #7f8c8d; margin-top: 10px;"></div>
        </div>
        
        <div class="status-card">
            <div class="status-label">
                <span class="metric-icon">⚡</span>CPU Usage
            </div>
            <div class="status-value" id="cpu-usage">--%</div>
            <div style="font-size: 0.9em; color: #7f8c8d;">System Performance</div>
        </div>
        
        <div class="status-card">
            <div class="status-label">
                <span class="metric-icon">💾</span>Memory Usage
            </div>
            <div class="status-value" id="memory-usage">--%</div>
            <div style="font-size: 0.9em; color: #7f8c8d;">RAM Utilization</div>
        </div>
        
        <div class="status-card">
            <div class="status-label">
                <span class="metric-icon">🛡️</span>System Health
            </div>
            <div class="status-value" id="system-health">MONITORING</div>
            <div style="font-size: 0.9em; color: #7f8c8d;">Overall Status</div>
        </div>
    </div>
    
    <div class="status-card large-card">
        <h3><span class="metric-icon">📊</span>Real-Time Performance Chart</h3>
        <div class="chart-container">
            <canvas id="performance-chart"></canvas>
        </div>
    </div>
    
    <div class="status-card large-card">
        <h3><span class="metric-icon">🚨</span>System Alerts & Status</h3>
        <div id="alerts-container">
            <div class="alert alert-info">
                <strong>Dashboard Initialized</strong> - QA Wolf monitoring is now active with zero trading impact
            </div>
        </div>
    </div>

    <script>
        // Initialize Socket.IO connection
        const socket = io();
        
        // Connection status indicator
        const connectionStatus = document.getElementById('connection-status');
        
        // Chart setup with enhanced styling
        const ctx = document.getElementById('performance-chart').getContext('2d');
        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'CPU Usage %',
                    data: [],
                    borderColor: 'rgb(52, 152, 219)',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    tension: 0.4,
                    fill: true
                }, {
                    label: 'Memory Usage %',
                    data: [],
                    borderColor: 'rgb(231, 76, 60)',
                    backgroundColor: 'rgba(231, 76, 60, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: true,
                        text: 'System Performance Over Time'
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100,
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    },
                    x: {
                        grid: {
                            color: 'rgba(0,0,0,0.1)'
                        }
                    }
                },
                animation: {
                    duration: 750
                }
            }
        });
        
        // Socket event handlers
        socket.on('connect', function() {
            console.log('Connected to QA Wolf Dashboard');
            connectionStatus.textContent = '🟢 Connected';
            connectionStatus.className = 'connection-status connected';
            document.getElementById('last-update').textContent = 'Last Update: Connected to monitoring system';
            
            // Add connection alert
            addAlert('success', 'Connected to QA Wolf monitoring system', 'Real-time data streaming active');
        });
        
        socket.on('disconnect', function() {
            console.log('Disconnected from dashboard');
            connectionStatus.textContent = '🔴 Disconnected';
            connectionStatus.className = 'connection-status disconnected';
            
            addAlert('danger', 'Connection Lost', 'Attempting to reconnect...');
        });
        
        socket.on('status_update', function(data) {
            updateDashboard(data);
        });
        
        socket.on('error', function(error) {
            console.error('Dashboard error:', error);
            addAlert('danger', 'Dashboard Error', error.message || 'Unknown error occurred');
        });
        
        function updateDashboard(data) {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            const dateString = now.toLocaleDateString();
            
            document.getElementById('last-update').textContent = `Last Update: ${dateString} ${timeString}`;
            
            if (data.system_health) {
                const health = data.system_health;
                
                // Update CPU usage
                const cpuUsage = health.cpu_percent || 0;
                document.getElementById('cpu-usage').textContent = `${cpuUsage.toFixed(1)}%`;
                document.getElementById('cpu-usage').className = getStatusClass(cpuUsage, 70, 85);
                
                // Update Memory usage
                const memUsage = health.memory_percent || 0;
                document.getElementById('memory-usage').textContent = `${memUsage.toFixed(1)}%`;
                document.getElementById('memory-usage').className = getStatusClass(memUsage, 75, 90);
                
                // Update Bot status
                const botStatus = health.trading_bot_status || 'UNKNOWN';
                document.getElementById('bot-status').textContent = botStatus;
                document.getElementById('bot-status').className = getBotStatusClass(botStatus);
                
                // Update bot details
                const botDetails = document.getElementById('bot-details');
                if (health.timestamp) {
                    const lastCheck = new Date(health.timestamp).toLocaleTimeString();
                    botDetails.textContent = `Last checked: ${lastCheck}`;
                }
                
                // Update system health
                const overallHealth = getOverallHealth(cpuUsage, memUsage, botStatus);
                document.getElementById('system-health').textContent = overallHealth.status;
                document.getElementById('system-health').className = overallHealth.class;
                
                // Update chart
                chart.data.labels.push(timeString);
                chart.data.datasets[0].data.push(cpuUsage);
                chart.data.datasets[1].data.push(memUsage);
                
                // Keep only last 30 data points
                if (chart.data.labels.length > 30) {
                    chart.data.labels.shift();
                    chart.data.datasets[0].data.shift();
                    chart.data.datasets[1].data.shift();
                }
                
                chart.update('none'); // No animation for real-time updates
                
                // Check for alerts
                checkForAlerts(cpuUsage, memUsage, botStatus);
            }
        }
        
        function getStatusClass(value, warningThreshold, dangerThreshold) {
            if (value >= dangerThreshold) return 'status-value status-danger';
            if (value >= warningThreshold) return 'status-value status-warning';
            return 'status-value status-active';
        }
        
        function getBotStatusClass(status) {
            switch(status) {
                case 'ACTIVE': return 'status-value status-active';
                case 'IDLE': return 'status-value status-warning';
                case 'INACTIVE': 
                case 'NO_LOGS': return 'status-value status-danger';
                default: return 'status-value';
            }
        }
        
        function getOverallHealth(cpu, memory, botStatus) {
            if (cpu >= 85 || memory >= 90) {
                return { status: 'CRITICAL', class: 'status-value status-danger' };
            }
            if (cpu >= 70 || memory >= 75 || botStatus === 'INACTIVE') {
                return { status: 'WARNING', class: 'status-value status-warning' };
            }
            if (botStatus === 'ACTIVE') {
                return { status: 'EXCELLENT', class: 'status-value status-active' };
            }
            return { status: 'GOOD', class: 'status-value status-active' };
        }
        
        function checkForAlerts(cpu, memory, botStatus) {
            // CPU alerts
            if (cpu >= 85) {
                addAlert('danger', 'High CPU Usage', `CPU usage is at ${cpu.toFixed(1)}% - consider investigating`);
            } else if (cpu >= 70) {
                addAlert('warning', 'Elevated CPU Usage', `CPU usage is at ${cpu.toFixed(1)}%`);
            }
            
            // Memory alerts
            if (memory >= 90) {
                addAlert('danger', 'High Memory Usage', `Memory usage is at ${memory.toFixed(1)}% - system may be under stress`);
            } else if (memory >= 75) {
                addAlert('warning', 'Elevated Memory Usage', `Memory usage is at ${memory.toFixed(1)}%`);
            }
            
            // Bot status alerts
            if (botStatus === 'INACTIVE') {
                addAlert('warning', 'Trading Bot Inactive', 'No recent activity detected in trading bot logs');
            }
        }
        
        function addAlert(type, title, message) {
            const alertsContainer = document.getElementById('alerts-container');
            const alertClass = type === 'success' ? 'alert-info' : 
                              type === 'warning' ? 'alert-warning' : 'alert-danger';
            
            const alertElement = document.createElement('div');
            alertElement.className = `alert ${alertClass}`;
            alertElement.innerHTML = `
                <strong>${title}</strong> - ${message}
                <small style="float: right; opacity: 0.7;">${new Date().toLocaleTimeString()}</small>
            `;
            
            // Add to top of alerts
            alertsContainer.insertBefore(alertElement, alertsContainer.firstChild);
            
            // Remove old alerts (keep only last 10)
            while (alertsContainer.children.length > 10) {
                alertsContainer.removeChild(alertsContainer.lastChild);
            }
        }
        
        // Request initial update
        socket.emit('request_update');
        
        // Auto-refresh every 30 seconds
        setInterval(() => {
            socket.emit('request_update');
        }, 30000);
        
        // Add some initial sample data to make the chart look good
        setTimeout(() => {
            if (chart.data.labels.length === 0) {
                // Add some sample data points
                for (let i = 0; i < 10; i++) {
                    const time = new Date(Date.now() - (10-i) * 30000).toLocaleTimeString();
                    chart.data.labels.push(time);
                    chart.data.datasets[0].data.push(Math.random() * 30 + 10); // CPU 10-40%
                    chart.data.datasets[1].data.push(Math.random() * 20 + 40); // Memory 40-60%
                }
                chart.update();
            }
        }, 2000);
    </script>
</body>
</html>