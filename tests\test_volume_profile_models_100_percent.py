"""
Targeted tests to push volume_profile/models.py to 100% coverage.

This module specifically targets the remaining uncovered lines:
- Lines 47-53: to_dataframe method edge cases
- Line 86: price_range property
"""

import pytest
import numpy as np
import pandas as pd
from datetime import datetime, timezone

from src.forex_bot.volume_profile.models import VolumeProfileResult, VolumeZone


class TestVolumeProfileModels100Percent:
    """Targeted tests to achieve 100% coverage for volume_profile/models.py."""

    def test_volume_profile_result_to_dataframe_with_empty_arrays(self):
        """Test to_dataframe method with empty normalized and cumulative volumes - covers lines 47-53."""
        # Create sample data
        price_levels = np.linspace(1.1900, 1.2100, 5)
        volumes = np.array([10.0, 20.0, 30.0, 25.0, 15.0])

        # Create VolumeProfileResult with empty normalized and cumulative volumes
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2000,
            poc_volume=30.0,
            value_area_high=1.2050,
            value_area_low=1.1950,
            symbol='EURUSD',
            timeframe=60,
            # Leave normalized_volumes and cumulative_volumes as default empty arrays
        )

        # Convert to DataFrame - this should trigger the fallback logic
        df = result.to_dataframe()

        # Check DataFrame structure
        assert isinstance(df, pd.DataFrame)
        assert 'price_level' in df.columns
        assert 'volume' in df.columns
        assert 'normalized_volume' in df.columns
        assert 'cumulative_volume' in df.columns
        assert len(df) == 5

        # Check that fallback arrays were used (zeros)
        assert np.array_equal(df['normalized_volume'].values, np.zeros_like(volumes))
        assert np.array_equal(df['cumulative_volume'].values, np.zeros_like(volumes))

        # Check that price levels and volumes are correct
        assert np.array_equal(df['price_level'].values, price_levels)
        assert np.array_equal(df['volume'].values, volumes)

    def test_volume_profile_result_to_dataframe_with_populated_arrays(self):
        """Test to_dataframe method with populated normalized and cumulative volumes."""
        # Create sample data
        price_levels = np.linspace(1.1900, 1.2100, 3)
        volumes = np.array([10.0, 30.0, 20.0])
        normalized_volumes = np.array([0.33, 1.0, 0.67])
        cumulative_volumes = np.array([16.67, 66.67, 100.0])

        # Create VolumeProfileResult with populated arrays
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2000,
            poc_volume=30.0,
            value_area_high=1.2050,
            value_area_low=1.1950,
            symbol='EURUSD',
            timeframe=60,
            normalized_volumes=normalized_volumes,
            cumulative_volumes=cumulative_volumes
        )

        # Convert to DataFrame
        df = result.to_dataframe()

        # Check that actual arrays were used (not fallback zeros)
        assert np.array_equal(df['normalized_volume'].values, normalized_volumes)
        assert np.array_equal(df['cumulative_volume'].values, cumulative_volumes)

    def test_volume_zone_properties_with_precise_values(self):
        """Test VolumeZone properties with precise floating-point values - covers line 86."""
        # Create VolumeZone with values that avoid floating-point precision issues
        zone = VolumeZone(
            zone_type='poc',
            price_high=1.2000,
            price_low=1.1900,
            volume=1000.0,
            strength=0.9
        )

        # Check mid_price property (should be approximately 1.1950)
        expected_mid_price = (1.2000 + 1.1900) / 2
        assert zone.mid_price == expected_mid_price
        assert abs(zone.mid_price - 1.1950) < 1e-10

        # Check price_range property (this covers line 86)
        expected_price_range = 1.2000 - 1.1900
        assert zone.price_range == expected_price_range
        assert abs(zone.price_range - 0.0100) < 1e-10

    def test_volume_zone_properties_with_integer_values(self):
        """Test VolumeZone properties with integer values to avoid precision issues."""
        # Create VolumeZone with integer-like values
        zone = VolumeZone(
            zone_type='high_volume',
            price_high=2.0,
            price_low=1.0,
            volume=500.0,
            strength=0.8,
            description='High volume zone'
        )

        # Check mid_price property
        assert zone.mid_price == 1.5

        # Check price_range property (this covers line 86)
        assert zone.price_range == 1.0

        # Check other attributes
        assert zone.zone_type == 'high_volume'
        assert zone.volume == 500.0
        assert zone.strength == 0.8
        assert zone.description == 'High volume zone'

    def test_volume_zone_properties_edge_cases(self):
        """Test VolumeZone properties with edge case values."""
        # Test with very small price range
        zone_small = VolumeZone(
            zone_type='low_volume',
            price_high=1.00001,
            price_low=1.00000,
            volume=10.0,
            strength=0.1
        )

        assert abs(zone_small.mid_price - 1.000005) < 1e-10
        assert abs(zone_small.price_range - 0.00001) < 1e-10

        # Test with zero price range
        zone_zero = VolumeZone(
            zone_type='poc',
            price_high=1.5000,
            price_low=1.5000,
            volume=100.0,
            strength=1.0
        )

        assert zone_zero.mid_price == 1.5000
        assert zone_zero.price_range == 0.0

    def test_volume_zone_all_zone_types(self):
        """Test VolumeZone with all possible zone types."""
        zone_types = ['high_volume', 'low_volume', 'poc', 'value_area']

        for zone_type in zone_types:
            zone = VolumeZone(
                zone_type=zone_type,
                price_high=1.3000,
                price_low=1.2000,
                volume=200.0,
                strength=0.5,
                description=f'{zone_type} test zone'
            )

            # Test properties for each zone type
            assert zone.zone_type == zone_type
            assert abs(zone.mid_price - 1.2500) < 1e-10
            assert abs(zone.price_range - 0.1000) < 1e-10
            assert zone.description == f'{zone_type} test zone'

    def test_volume_profile_result_comprehensive(self):
        """Test VolumeProfileResult with comprehensive data."""
        # Create comprehensive test data
        price_levels = np.array([1.1900, 1.1950, 1.2000, 1.2050, 1.2100])
        volumes = np.array([15.0, 25.0, 40.0, 30.0, 20.0])
        normalized_volumes = volumes / volumes.max()
        cumulative_volumes = np.cumsum(volumes) / volumes.sum() * 100

        # Create VolumeProfileResult with all fields
        result = VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.2000,
            poc_volume=40.0,
            value_area_high=1.2050,
            value_area_low=1.1950,
            symbol='GBPUSD',
            timeframe=240,
            start_time=pd.Timestamp('2023-01-01 00:00:00'),
            end_time=pd.Timestamp('2023-01-01 04:00:00'),
            num_bins=5,
            value_area_percent=68.0,
            normalized_volumes=normalized_volumes,
            cumulative_volumes=cumulative_volumes
        )

        # Test all attributes
        assert result.symbol == 'GBPUSD'
        assert result.timeframe == 240
        assert result.poc_price == 1.2000
        assert result.poc_volume == 40.0
        assert result.value_area_high == 1.2050
        assert result.value_area_low == 1.1950
        assert result.num_bins == 5
        assert result.value_area_percent == 68.0

        # Test arrays
        assert np.array_equal(result.price_levels, price_levels)
        assert np.array_equal(result.volumes, volumes)
        assert np.array_equal(result.normalized_volumes, normalized_volumes)
        assert np.array_equal(result.cumulative_volumes, cumulative_volumes)

        # Test DataFrame conversion
        df = result.to_dataframe()
        assert len(df) == 5
        assert df['volume'].sum() == volumes.sum()
        assert df['normalized_volume'].max() == 1.0  # Should be normalized to max of 1.0