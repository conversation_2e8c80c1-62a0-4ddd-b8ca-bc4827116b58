"""
Targeted tests to push multilingual_news/models.py to 95%+ coverage.
"""

import pytest
from datetime import datetime, timezone, timedelta
from src.forex_bot.multilingual_news.models import (
    NewsArticle, NewsSummary, NewsContext, SentimentAnalysis, Entity,
    NewsSource, NewsCategory, NewsImpact, SentimentLabel
)


def test_news_summary_sources_property():
    """Test NewsSummary sources property - covers line 251."""
    # Create articles with different sources
    article1 = NewsArticle(
        title="Test Article 1",
        content="Test content 1",
        url="https://example1.com",
        source=NewsSource.BLOOMBERG,
        published_at=datetime.now(timezone.utc),
        language="en"
    )

    article2 = NewsArticle(
        title="Test Article 2",
        content="Test content 2",
        url="https://example2.com",
        source=NewsSource.REUTERS,
        published_at=datetime.now(timezone.utc),
        language="en"
    )

    article3 = NewsArticle(
        title="Test Article 3",
        content="Test content 3",
        url="https://example3.com",
        source=NewsSource.BLOOMBERG,  # Duplicate source
        published_at=datetime.now(timezone.utc),
        language="en"
    )

    summary = NewsSummary(
        text="Test summary",
        articles=[article1, article2, article3],
        language="en"
    )

    # Test sources property returns unique sources
    sources = summary.sources
    assert len(sources) == 2  # Only Bloomberg and Reuters
    assert NewsSource.BLOOMBERG in sources
    assert NewsSource.REUTERS in sources
def test_news_summary_time_range_empty_articles():
    """Test NewsSummary time_range property with empty articles - covers lines 264-265."""
    summary = NewsSummary(
        text="Test summary",
        articles=[],  # Empty articles list
        language="en"
    )

    # Test time_range property with empty articles
    start_time, end_time = summary.time_range
    assert isinstance(start_time, datetime)
    assert isinstance(end_time, datetime)
    assert start_time == end_time  # Should be the same time


def test_news_context_overall_sentiment_edge_cases():
    """Test NewsContext overall_sentiment property edge cases - covers lines 325, 331, 334-339."""
    # Test case 1: total_weight == 0 (line 325)
    context_no_sentiment = NewsContext(
        currency_or_pair="EURUSD",
        recent_sentiment=None,
        daily_sentiment=None,
        weekly_sentiment=None
    )
    assert context_no_sentiment.overall_sentiment is None  # Tests line 325

    # Test case 2: avg_value >= 0.7 (VERY_POSITIVE) - line 331
    very_positive_sentiment = SentimentAnalysis(
        sentiment_label=SentimentLabel.VERY_POSITIVE,
        sentiment_value=0.8,
        confidence=0.9,
        language="en",
        model_name="test_model"
    )

    context_very_positive = NewsContext(
        currency_or_pair="EURUSD",
        recent_sentiment=very_positive_sentiment,
        daily_sentiment=None,
        weekly_sentiment=None
    )
    assert context_very_positive.overall_sentiment == SentimentLabel.VERY_POSITIVE  # Tests line 331    # Test case 3: avg_value > -0.7 (NEGATIVE) - line 337
    negative_sentiment = SentimentAnalysis(
        sentiment_label=SentimentLabel.NEGATIVE,
        sentiment_value=-0.5,
        confidence=0.8,
        language="en",
        model_name="test_model"
    )

    context_negative = NewsContext(
        currency_or_pair="EURUSD",
        recent_sentiment=negative_sentiment,
        daily_sentiment=None,
        weekly_sentiment=None
    )
    assert context_negative.overall_sentiment == SentimentLabel.NEGATIVE  # Tests line 337

    # Test case 4: avg_value <= -0.7 (VERY_NEGATIVE) - line 339
    very_negative_sentiment = SentimentAnalysis(
        sentiment_label=SentimentLabel.VERY_NEGATIVE,
        sentiment_value=-0.8,
        confidence=0.9,
        language="en",
        model_name="test_model"
    )

    context_very_negative = NewsContext(
        currency_or_pair="EURUSD",
        recent_sentiment=very_negative_sentiment,
        daily_sentiment=None,
        weekly_sentiment=None
    )
    assert context_very_negative.overall_sentiment == SentimentLabel.VERY_NEGATIVE  # Tests line 339


def test_news_context_has_breaking_news():
    """Test NewsContext has_breaking_news property - covers line 349."""
    # Create a breaking news article (less than 1 hour old)
    breaking_article = NewsArticle(
        title="Breaking News",
        content="Breaking news content",
        url="https://breaking.com",
        source=NewsSource.CNBC,
        published_at=datetime.now(timezone.utc) - timedelta(minutes=30),  # 30 minutes ago
        language="en"
    )

    context_with_breaking = NewsContext(
        currency_or_pair="EURUSD",
        high_impact_news=[breaking_article]
    )

    # Test has_breaking_news property
    assert context_with_breaking.has_breaking_news is True  # Tests line 349


def test_news_article_age_hours_without_timezone():
    """Test NewsArticle age_hours property when published_at has no timezone - covers line 95."""
    # Create article with naive datetime (no timezone info)
    naive_datetime = datetime.now() - timedelta(hours=2)  # No timezone info
    article_without_tz = NewsArticle(
        title="Test Article",
        content="Test content",
        url="https://example.com",
        source=NewsSource.BLOOMBERG,
        published_at=naive_datetime,  # No timezone info
        language="en"
    )

    # Test age_hours calculation with naive datetime - this triggers line 95
    assert article_without_tz.age_hours >= 2.0  # Tests line 95 (replace with UTC)


def test_news_article_is_recent():
    """Test NewsArticle is_recent property - covers line 110."""
    # Create recent article (less than 24 hours old)
    recent_article = NewsArticle(
        title="Recent Article",
        content="Recent content",
        url="https://recent.com",
        source=NewsSource.REUTERS,
        published_at=datetime.now(timezone.utc) - timedelta(hours=12),
        language="en"
    )

    # Create old article (more than 24 hours old)
    old_article = NewsArticle(
        title="Old Article",
        content="Old content",
        url="https://old.com",
        source=NewsSource.CNBC,
        published_at=datetime.now(timezone.utc) - timedelta(hours=30),
        language="en"
    )

    # Test is_recent property
    assert recent_article.is_recent is True  # Tests line 110
    assert old_article.is_recent is False


def test_sentiment_analysis_properties():
    """Test SentimentAnalysis property methods - covers lines 154, 164, 174."""
    # Test is_positive property - line 154
    positive_sentiment = SentimentAnalysis(
        sentiment_label=SentimentLabel.POSITIVE,
        sentiment_value=0.5,
        confidence=0.8,
        language="en",
        model_name="test_model"
    )
    assert positive_sentiment.is_positive is True  # Tests line 154

    very_positive_sentiment = SentimentAnalysis(
        sentiment_label=SentimentLabel.VERY_POSITIVE,
        sentiment_value=0.8,
        confidence=0.9,
        language="en",
        model_name="test_model"
    )
    assert very_positive_sentiment.is_positive is True  # Tests line 154

    # Test is_negative property - line 164
    negative_sentiment = SentimentAnalysis(
        sentiment_label=SentimentLabel.NEGATIVE,
        sentiment_value=-0.5,
        confidence=0.8,
        language="en",
        model_name="test_model"
    )
    assert negative_sentiment.is_negative is True  # Tests line 164

    very_negative_sentiment = SentimentAnalysis(
        sentiment_label=SentimentLabel.VERY_NEGATIVE,
        sentiment_value=-0.8,
        confidence=0.9,
        language="en",
        model_name="test_model"
    )
    assert very_negative_sentiment.is_negative is True  # Tests line 164

    # Test is_neutral property - line 174
    neutral_sentiment = SentimentAnalysis(
        sentiment_label=SentimentLabel.NEUTRAL,
        sentiment_value=0.0,
        confidence=0.7,
        language="en",
        model_name="test_model"
    )
    assert neutral_sentiment.is_neutral is True  # Tests line 174

    # Test negative cases
    assert positive_sentiment.is_negative is False
    assert positive_sentiment.is_neutral is False
    assert negative_sentiment.is_positive is False
    assert negative_sentiment.is_neutral is False
    assert neutral_sentiment.is_positive is False
    assert neutral_sentiment.is_negative is False


def test_news_summary_article_count():
    """Test NewsSummary article_count property - covers line 241."""
    article1 = NewsArticle(
        title="Article 1",
        content="Content 1",
        url="https://example1.com",
        source=NewsSource.BLOOMBERG,
        published_at=datetime.now(timezone.utc),
        language="en"
    )

    article2 = NewsArticle(
        title="Article 2",
        content="Content 2",
        url="https://example2.com",
        source=NewsSource.REUTERS,
        published_at=datetime.now(timezone.utc),
        language="en"
    )

    summary = NewsSummary(
        text="Test summary",
        articles=[article1, article2],
        language="en"
    )

    # Test article_count property
    assert summary.article_count == 2  # Tests line 241


def test_news_context_overall_sentiment_with_daily_weekly():
    """Test NewsContext overall_sentiment with daily and weekly sentiment - covers lines 317-318, 321-322, 333, 335."""
    # Test with daily sentiment (lines 317-318)
    daily_sentiment = SentimentAnalysis(
        sentiment_label=SentimentLabel.POSITIVE,
        sentiment_value=0.3,  # This will result in avg_value >= 0.2
        confidence=0.8,
        language="en",
        model_name="test_model"
    )

    context_with_daily = NewsContext(
        currency_or_pair="EURUSD",
        daily_sentiment=daily_sentiment
    )
    assert context_with_daily.overall_sentiment == SentimentLabel.POSITIVE  # Tests lines 317-318, 333

    # Test with weekly sentiment (lines 321-322)
    weekly_sentiment = SentimentAnalysis(
        sentiment_label=SentimentLabel.NEUTRAL,
        sentiment_value=0.1,  # This will result in avg_value > -0.2 but < 0.2
        confidence=0.7,
        language="en",
        model_name="test_model"
    )

    context_with_weekly = NewsContext(
        currency_or_pair="EURUSD",
        weekly_sentiment=weekly_sentiment
    )
    assert context_with_weekly.overall_sentiment == SentimentLabel.NEUTRAL  # Tests lines 321-322, 335


def test_news_context_overall_sentiment_no_weight():
    """Test NewsContext overall_sentiment when total_weight is 0 - covers line 325."""
    # Create context with no sentiment data (total_weight will be 0)
    context_no_sentiment = NewsContext(
        currency_or_pair="EURUSD"
        # No daily_sentiment, weekly_sentiment, or monthly_sentiment
    )

    # Test that overall_sentiment returns None when total_weight == 0
    assert context_no_sentiment.overall_sentiment is None  # Tests line 325


def test_news_summary_time_range_with_articles():
    """Test NewsSummary time_range property with articles - covers lines 264-265."""
    # Create articles with different timestamps
    early_time = datetime.now(timezone.utc) - timedelta(hours=5)
    late_time = datetime.now(timezone.utc) - timedelta(hours=1)

    article1 = NewsArticle(
        title="Early Article",
        content="Early content",
        url="https://early.com",
        source=NewsSource.BLOOMBERG,
        published_at=early_time,
        language="en"
    )

    article2 = NewsArticle(
        title="Late Article",
        content="Late content",
        url="https://late.com",
        source=NewsSource.REUTERS,
        published_at=late_time,
        language="en"
    )

    summary = NewsSummary(
        text="Test summary with time range",
        articles=[article1, article2],
        language="en"
    )

    # Test time_range property - should return (min_time, max_time)
    time_range = summary.time_range  # Tests lines 264-265
    assert time_range[0] == early_time  # min time
    assert time_range[1] == late_time   # max time


def test_news_context_trading_bias():
    """Test NewsContext trading_bias property - covers lines 359-367."""
    # Test case 1: No signals (lines 359-360)
    context_no_signals = NewsContext(
        currency_or_pair="EURUSD",
        bullish_signals=[],
        bearish_signals=[]
    )
    assert context_no_signals.trading_bias is None  # Tests lines 359-360

    # Test case 2: More bullish signals (lines 362-363)
    context_bullish = NewsContext(
        currency_or_pair="EURUSD",
        bullish_signals=["Signal 1", "Signal 2", "Signal 3"],
        bearish_signals=["Signal A"]
    )
    assert context_bullish.trading_bias == "BUY"  # Tests lines 362-363

    # Test case 3: More bearish signals (lines 364-365)
    context_bearish = NewsContext(
        currency_or_pair="EURUSD",
        bullish_signals=["Signal 1"],
        bearish_signals=["Signal A", "Signal B", "Signal C"]
    )
    assert context_bearish.trading_bias == "SELL"  # Tests lines 364-365

    # Test case 4: Equal signals (lines 366-367)
    context_equal = NewsContext(
        currency_or_pair="EURUSD",
        bullish_signals=["Signal 1", "Signal 2"],
        bearish_signals=["Signal A", "Signal B"]
    )
    assert context_equal.trading_bias == "NEUTRAL"  # Tests lines 366-367