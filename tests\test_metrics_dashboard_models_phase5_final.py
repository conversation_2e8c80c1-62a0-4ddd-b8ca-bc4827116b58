"""
Phase 5D comprehensive tests to push metrics_dashboard/models.py to 90%+ coverage.
"""

import pytest
from datetime import datetime, timezone

class TestMetricsDashboardModelsPhase5Final:
    """Phase 5D tests to achieve 90%+ coverage for metrics_dashboard/models.py."""

    def test_comprehensive_enum_validation(self):
        """Test all enum values and their usage."""
        from src.forex_bot.metrics_dashboard import models
        
        # Test TimeFrame enum
        time_frames = [
            models.TimeFrame.TODAY,
            models.TimeFrame.YESTERDAY,
            models.TimeFrame.THIS_WEEK,
            models.TimeFrame.LAST_WEEK,
            models.TimeFrame.THIS_MONTH,
            models.TimeFrame.LAST_MONTH,
            models.TimeFrame.THIS_YEAR,
            models.TimeFrame.LAST_YEAR,
            models.TimeFrame.CUSTOM
        ]
        
        for time_frame in time_frames:
            assert isinstance(time_frame, models.TimeFrame)
        
        # Test MetricCategory enum
        categories = [
            models.MetricCategory.PERFORMANCE,
            models.MetricCategory.TRADE,
            models.MetricCategory.MARKET,
            models.MetricCategory.SYSTEM
        ]
        
        for category in categories:
            assert isinstance(category, models.MetricCategory)
        
        # Test ChartType enum
        chart_types = [
            models.ChartType.LINE,
            models.ChartType.BAR,
            models.ChartType.SCATTER,
            models.ChartType.PIE,
            models.ChartType.HEATMAP,
            models.ChartType.HISTOGRAM,
            models.ChartType.GAUGE,
            models.ChartType.TABLE,
            models.ChartType.CANDLESTICK
        ]
        
        for chart_type in chart_types:
            assert isinstance(chart_type, models.ChartType)

    def test_comprehensive_model_validation(self):
        """Test all model classes with comprehensive validation scenarios."""
        from src.forex_bot.metrics_dashboard import models
        
        timestamp = datetime.now(timezone.utc)
        
        # Test MetricValue
        metric = models.MetricValue(
            name='test_metric',
            value=100.5,
            unit='USD',
            timestamp=timestamp,
            category=models.MetricCategory.PERFORMANCE,
            symbol='EURUSD'
        )
        assert metric.name == 'test_metric'
        assert metric.value == 100.5
        
        # Test PerformanceMetrics
        perf_metrics = models.PerformanceMetrics(
            total_profit_loss=1500.0,
            win_loss_ratio=1.5,
            average_win=250.0,
            average_loss=-150.0,
            max_drawdown=-500.0,
            roi=15.5,
            profit_factor=1.3,
            timestamp=timestamp
        )
        assert perf_metrics.total_profit_loss == 1500.0
        
        # Test TradeMetrics
        trade_metrics = models.TradeMetrics(
            total_trades=100,
            winning_trades=60,
            losing_trades=40,
            average_trade_duration=3600.0,
            trade_frequency=5.5,
            average_position_size=10000.0,
            timestamp=timestamp
        )
        assert trade_metrics.total_trades == 100
        
        # Test MarketMetrics
        market_metrics = models.MarketMetrics(
            volatility=0.15,
            trend_strength=0.8,
            timestamp=timestamp,
            symbol='EURUSD'
        )
        assert market_metrics.volatility == 0.15        # Test SystemMetrics
        system_metrics = models.SystemMetrics(
            execution_latency=50.5,
            order_fill_rate=98.5,
            system_uptime=86400.0,
            error_rate=0.1,
            cpu_usage=45.0,
            memory_usage=60.0,
            timestamp=timestamp
        )
        assert system_metrics.execution_latency == 50.5
        
        # Test ChartData
        chart_data = models.ChartData(
            chart_type=models.ChartType.LINE,
            title='Test Chart',
            data=[1, 2, 3, 4, 5],
            category=models.MetricCategory.PERFORMANCE,
            timestamp=timestamp
        )
        assert chart_data.chart_type == models.ChartType.LINE
        
        # Test DashboardLayout
        layout = models.DashboardLayout(
            rows=3,
            columns=4,
            charts=[
                {'chart_id': 'chart1', 'row': 0, 'column': 0, 'row_span': 1, 'col_span': 2}
            ]
        )
        assert layout.rows == 3
        
        # Test Dashboard
        dashboard = models.Dashboard(
            name='Test Dashboard',
            layout=layout,
            charts={'chart1': chart_data},
            time_frame=models.TimeFrame.TODAY,
            last_updated=timestamp
        )
        assert dashboard.name == 'Test Dashboard'

    def test_comprehensive_validation_errors(self):
        """Test validation error scenarios."""
        from src.forex_bot.metrics_dashboard import models
        
        timestamp = datetime.now(timezone.utc)
        
        # Test PerformanceMetrics positive validation
        with pytest.raises(ValueError, match="value must be positive"):
            models.PerformanceMetrics(
                total_profit_loss=1000.0, win_loss_ratio=-1.0, average_win=200.0,
                average_loss=-100.0, max_drawdown=-300.0, roi=10.0, profit_factor=1.5,
                timestamp=timestamp
            )
        
        # Test PerformanceMetrics max_drawdown validation
        with pytest.raises(ValueError, match="max_drawdown must be negative or zero"):
            models.PerformanceMetrics(
                total_profit_loss=1000.0, win_loss_ratio=1.5, average_win=200.0,
                average_loss=-100.0, max_drawdown=100.0, roi=10.0, profit_factor=1.5,
                timestamp=timestamp
            )        # Test TradeMetrics non-negative validation
        with pytest.raises(ValueError, match="value must be non-negative"):
            models.TradeMetrics(
                total_trades=-1, winning_trades=60, losing_trades=40,
                average_trade_duration=3600.0, trade_frequency=5.5,
                average_position_size=10000.0, timestamp=timestamp
            )
        
        # Test TradeMetrics trades validation
        with pytest.raises(ValueError, match="winning_trades and losing_trades cannot exceed total_trades"):
            models.TradeMetrics(
                total_trades=50, winning_trades=60, losing_trades=40,
                average_trade_duration=3600.0, trade_frequency=5.5,
                average_position_size=10000.0, timestamp=timestamp
            )
        
        # Test MarketMetrics volatility validation
        with pytest.raises(ValueError, match="volatility must be non-negative"):
            models.MarketMetrics(
                volatility=-0.1, trend_strength=0.5, timestamp=timestamp, symbol='TEST'
            )
        
        # Test MarketMetrics trend_strength validation
        with pytest.raises(ValueError, match="trend_strength must be between -1.0 and 1.0"):
            models.MarketMetrics(
                volatility=0.1, trend_strength=1.5, timestamp=timestamp, symbol='TEST'
            )
        
        # Test MarketMetrics sentiment_score validation
        with pytest.raises(ValueError, match="sentiment_score must be between -1.0 and 1.0"):
            models.MarketMetrics(
                volatility=0.1, trend_strength=0.5, sentiment_score=2.0,
                timestamp=timestamp, symbol='TEST'
            )
        
        # Test SystemMetrics non-negative validation
        with pytest.raises(ValueError, match="value must be non-negative"):
            models.SystemMetrics(
                execution_latency=-10.0, order_fill_rate=98.5, system_uptime=86400.0,
                error_rate=0.1, cpu_usage=45.0, memory_usage=60.0, timestamp=timestamp
            )
        
        # Test SystemMetrics percentage validation
        with pytest.raises(ValueError, match="value must be between 0 and 100"):
            models.SystemMetrics(
                execution_latency=50.0, order_fill_rate=150.0, system_uptime=86400.0,
                error_rate=0.1, cpu_usage=45.0, memory_usage=60.0, timestamp=timestamp
            )
        
        # Test DashboardLayout positive validation
        with pytest.raises(ValueError, match="value must be positive"):
            models.DashboardLayout(rows=0, columns=2, charts=[])
        
        # Test DashboardLayout chart position validation
        with pytest.raises(ValueError, match="row must be between 0 and 2"):
            models.DashboardLayout(
                rows=3, columns=3,
                charts=[{'chart_id': 'test', 'row': 3, 'column': 0}]
            )

    def test_edge_cases_and_optional_features(self):
        """Test edge cases and optional features."""
        from src.forex_bot.metrics_dashboard import models
        
        timestamp = datetime.now(timezone.utc)
        
        # Test custom timeframe with valid dates
        layout = models.DashboardLayout(rows=2, columns=2, charts=[])
        chart_data = models.ChartData(
            chart_type=models.ChartType.BAR, title='Test', data=[],
            category=models.MetricCategory.TRADE, timestamp=timestamp
        )
        
        start_date = datetime(2023, 1, 1, tzinfo=timezone.utc)
        end_date = datetime(2023, 12, 31, tzinfo=timezone.utc)
        
        custom_dashboard = models.Dashboard(
            name='Custom Dashboard', layout=layout, charts={'chart1': chart_data},
            time_frame=models.TimeFrame.CUSTOM, custom_start_date=start_date,
            custom_end_date=end_date, last_updated=timestamp
        )
        assert custom_dashboard.time_frame == models.TimeFrame.CUSTOM
        assert custom_dashboard.custom_start_date == start_date
        assert custom_dashboard.custom_end_date == end_date
        
        # Test optional fields
        metric_minimal = models.MetricValue(
            name='minimal',
            value=50.0,
            timestamp=timestamp,
            category=models.MetricCategory.TRADE
        )
        assert metric_minimal.unit is None
        assert metric_minimal.symbol is None
        
        # Test market metrics with optional fields
        market_minimal = models.MarketMetrics(
            volatility=0.10,
            trend_strength=-0.5,
            timestamp=timestamp,
            symbol='GBPUSD'
        )
        assert market_minimal.support_level is None
        assert market_minimal.resistance_level is None
        assert market_minimal.sentiment_score is None
        
        # Test performance metrics with optional fields
        perf_minimal = models.PerformanceMetrics(
            total_profit_loss=1000.0,
            win_loss_ratio=2.0,
            average_win=200.0,
            average_loss=-100.0,
            max_drawdown=-300.0,
            roi=10.0,
            profit_factor=1.5,
            timestamp=timestamp
        )
        assert perf_minimal.sharpe_ratio is None
        assert perf_minimal.sortino_ratio is None
        assert perf_minimal.symbol is None