"""
Targeted tests to push correlation_matrix/models.py to 60%+ coverage.
"""

import pytest
from datetime import datetime, timezone
from src.forex_bot.correlation_matrix.models import (
    TimeWindow, CorrelationMethod, CorrelationStrength,
    CorrelationSettings, CorrelationPair, CorrelationMatrix,
    CorrelationTrend
)


class TestCorrelationSettings:
    """Test CorrelationSettings model."""
    
    def test_correlation_settings_creation_valid(self):
        """Test valid CorrelationSettings creation."""
        settings = CorrelationSettings(
            time_window=TimeWindow.DAY_1,
            method=CorrelationMethod.PEARSON,
            min_periods=30,
            symbols=["EURUSD", "GBPUSD"],
            include_base_pairs=True
        )
        
        assert settings.time_window == TimeWindow.DAY_1
        assert settings.method == CorrelationMethod.PEARSON
        assert settings.min_periods == 30
        assert settings.symbols == ["EURUSD", "GBPUSD"]
        assert settings.include_base_pairs is True
    
    def test_correlation_settings_invalid_min_periods(self):
        """Test CorrelationSettings with invalid min_periods."""
        with pytest.raises(ValueError, match="min_periods must be at least 2"):
            CorrelationSettings(min_periods=1)  # Invalid: < 2
class TestCorrelationPair:
    """Test CorrelationPair model."""
    
    def test_correlation_pair_creation_valid(self):
        """Test valid CorrelationPair creation."""
        pair = CorrelationPair(
            symbol1="EURUSD",
            symbol2="GBPUSD",
            correlation=0.75,
            strength=CorrelationStrength.STRONG_POSITIVE,
            p_value=0.001,
            timestamp=datetime.now(timezone.utc),
            time_window=TimeWindow.DAY_1,
            method=CorrelationMethod.PEARSON
        )
        
        assert pair.symbol1 == "EURUSD"
        assert pair.symbol2 == "GBPUSD"
        assert pair.correlation == 0.75
        assert pair.strength == CorrelationStrength.STRONG_POSITIVE
        assert pair.p_value == 0.001
        assert pair.time_window == TimeWindow.DAY_1
        assert pair.method == CorrelationMethod.PEARSON
    
    def test_correlation_pair_invalid_correlation_high(self):
        """Test CorrelationPair with invalid high correlation."""
        with pytest.raises(ValueError, match="correlation must be between -1.0 and 1.0"):
            CorrelationPair(
                symbol1="EURUSD",
                symbol2="GBPUSD",
                correlation=1.5,  # Invalid: > 1.0
                strength=CorrelationStrength.STRONG_POSITIVE,
                timestamp=datetime.now(timezone.utc),
                time_window=TimeWindow.DAY_1,
                method=CorrelationMethod.PEARSON
            )
    
    def test_correlation_pair_invalid_correlation_low(self):
        """Test CorrelationPair with invalid low correlation."""
        with pytest.raises(ValueError, match="correlation must be between -1.0 and 1.0"):
            CorrelationPair(
                symbol1="EURUSD",
                symbol2="GBPUSD",
                correlation=-1.5,  # Invalid: < -1.0
                strength=CorrelationStrength.STRONG_NEGATIVE,
                timestamp=datetime.now(timezone.utc),
                time_window=TimeWindow.DAY_1,
                method=CorrelationMethod.PEARSON
            )    
    def test_correlation_pair_invalid_strength_mismatch(self):
        """Test CorrelationPair with mismatched strength."""
        with pytest.raises(ValueError, match="strength should be"):
            CorrelationPair(
                symbol1="EURUSD",
                symbol2="GBPUSD",
                correlation=0.75,  # Should be STRONG_POSITIVE
                strength=CorrelationStrength.WEAK_POSITIVE,  # Wrong strength
                timestamp=datetime.now(timezone.utc),
                time_window=TimeWindow.DAY_1,
                method=CorrelationMethod.PEARSON
            )


class TestCorrelationMatrix:
    """Test CorrelationMatrix model."""
    
    def test_correlation_matrix_creation_valid(self):
        """Test valid CorrelationMatrix creation."""
        symbols = ["EURUSD", "GBPUSD"]
        matrix = {
            "EURUSD": {"EURUSD": 1.0, "GBPUSD": 0.75},
            "GBPUSD": {"EURUSD": 0.75, "GBPUSD": 1.0}
        }
        
        corr_matrix = CorrelationMatrix(
            timestamp=datetime.now(timezone.utc),
            time_window=TimeWindow.DAY_1,
            method=CorrelationMethod.PEARSON,
            symbols=symbols,
            matrix=matrix
        )
        
        assert corr_matrix.symbols == symbols
        assert corr_matrix.matrix == matrix
        assert corr_matrix.time_window == TimeWindow.DAY_1
        assert corr_matrix.method == CorrelationMethod.PEARSON
    
    def test_correlation_matrix_invalid_missing_symbol(self):
        """Test CorrelationMatrix with missing symbol in matrix."""
        symbols = ["EURUSD", "GBPUSD"]
        matrix = {
            "EURUSD": {"EURUSD": 1.0, "GBPUSD": 0.75}
            # Missing GBPUSD row
        }
        
        with pytest.raises(ValueError, match="matrix missing symbol GBPUSD"):
            CorrelationMatrix(
                timestamp=datetime.now(timezone.utc),
                time_window=TimeWindow.DAY_1,
                method=CorrelationMethod.PEARSON,
                symbols=symbols,
                matrix=matrix
            )    
    def test_correlation_matrix_invalid_missing_correlation(self):
        """Test CorrelationMatrix with missing correlation in matrix."""
        symbols = ["EURUSD", "GBPUSD"]
        matrix = {
            "EURUSD": {"EURUSD": 1.0},  # Missing GBPUSD correlation
            "GBPUSD": {"EURUSD": 0.75, "GBPUSD": 1.0}
        }
        
        with pytest.raises(ValueError, match="matrix missing correlation between EURUSD and GBPUSD"):
            CorrelationMatrix(
                timestamp=datetime.now(timezone.utc),
                time_window=TimeWindow.DAY_1,
                method=CorrelationMethod.PEARSON,
                symbols=symbols,
                matrix=matrix
            )


class TestCorrelationTrend:
    """Test CorrelationTrend model."""
    
    def test_correlation_trend_creation_valid(self):
        """Test valid CorrelationTrend creation."""
        timestamps = [datetime.now(timezone.utc) for _ in range(3)]
        correlations = [0.5, 0.6, 0.7]
        
        trend = CorrelationTrend(
            symbol1="EURUSD",
            symbol2="GBPUSD",
            timestamps=timestamps,
            correlations=correlations,
            time_window=TimeWindow.DAY_1,
            method=CorrelationMethod.PEARSON
        )
        
        assert trend.symbol1 == "EURUSD"
        assert trend.symbol2 == "GBPUSD"
        assert trend.timestamps == timestamps
        assert trend.correlations == correlations
        assert trend.time_window == TimeWindow.DAY_1
        assert trend.method == CorrelationMethod.PEARSON
    
    def test_correlation_trend_invalid_length_mismatch(self):
        """Test CorrelationTrend with mismatched lengths."""
        timestamps = [datetime.now(timezone.utc) for _ in range(3)]
        correlations = [0.5, 0.6]  # Different length
        
        with pytest.raises(ValueError, match="correlations and timestamps must have the same length"):
            CorrelationTrend(
                symbol1="EURUSD",
                symbol2="GBPUSD",
                timestamps=timestamps,
                correlations=correlations,
                time_window=TimeWindow.DAY_1,
                method=CorrelationMethod.PEARSON
            )    
    def test_correlation_trend_invalid_correlation_range(self):
        """Test CorrelationTrend with invalid correlation range."""
        timestamps = [datetime.now(timezone.utc) for _ in range(3)]
        correlations = [0.5, 1.5, 0.7]  # 1.5 is invalid
        
        with pytest.raises(ValueError, match="all correlations must be between -1.0 and 1.0"):
            CorrelationTrend(
                symbol1="EURUSD",
                symbol2="GBPUSD",
                timestamps=timestamps,
                correlations=correlations,
                time_window=TimeWindow.DAY_1,
                method=CorrelationMethod.PEARSON
            )


# Test non-Pydantic fallback classes
class TestNonPydanticModels:
    """Test non-Pydantic fallback models."""
    
    def test_correlation_settings_fallback(self):
        """Test CorrelationSettings fallback class."""
        # Import the fallback class directly
        from src.forex_bot.correlation_matrix.models import CorrelationSettings as FallbackSettings
        
        # Test creation
        settings = FallbackSettings(
            time_window=TimeWindow.DAY_1,
            method=CorrelationMethod.PEARSON,
            min_periods=30
        )
        
        assert settings.time_window == TimeWindow.DAY_1
        assert settings.method == CorrelationMethod.PEARSON
        assert settings.min_periods == 30
        
        # Test dict method
        settings_dict = settings.dict()
        assert isinstance(settings_dict, dict)
        assert "time_window" in settings_dict
    
    def test_correlation_pair_fallback(self):
        """Test CorrelationPair fallback class."""
        from src.forex_bot.correlation_matrix.models import CorrelationPair as FallbackPair
        
        pair = FallbackPair(
            symbol1="EURUSD",
            symbol2="GBPUSD",
            correlation=0.75,
            strength=CorrelationStrength.STRONG_POSITIVE,
            timestamp=datetime.now(timezone.utc),
            time_window=TimeWindow.DAY_1,
            method=CorrelationMethod.PEARSON
        )
        
        assert pair.symbol1 == "EURUSD"
        assert pair.symbol2 == "GBPUSD"
        assert pair.correlation == 0.75
        
        # Test dict method
        pair_dict = pair.dict()
        assert isinstance(pair_dict, dict)
        assert "symbol1" in pair_dict