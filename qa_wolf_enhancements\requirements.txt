# QA Wolf Enhancement Suite - Python Dependencies
# Professional-grade testing, monitoring, and optimization framework

# Core System Monitoring
psutil>=5.9.0                    # System performance monitoring (CPU, Memory, Disk)

# Web Dashboard Framework
flask>=2.3.0                     # Web application framework
flask-socketio>=5.3.0            # Real-time WebSocket communication
eventlet>=0.33.0                 # Async networking library for SocketIO

# HTTP and API Testing
requests>=2.31.0                 # HTTP library for API testing
aiohttp>=3.8.0                   # Async HTTP client/server
urllib3>=1.26.0                  # HTTP client library

# Data Processing and Storage
sqlite3                          # Built-in SQLite database (included in Python)
json                            # Built-in JSON processing (included in Python)

# Testing Framework
pytest>=7.4.0                   # Testing framework
pytest-cov>=4.1.0               # Coverage reporting
pytest-asyncio>=0.21.0          # Async testing support

# Development and Debugging
logging                         # Built-in logging (included in Python)
datetime                        # Built-in datetime (included in Python)
pathlib                         # Built-in path handling (included in Python)
threading                       # Built-in threading (included in Python)
time                           # Built-in time utilities (included in Python)
typing                         # Built-in type hints (included in Python)

# Optional: Enhanced Features
# Uncomment if you want additional capabilities:
# pandas>=2.0.0                  # Data analysis and manipulation
# numpy>=1.24.0                  # Numerical computing
# matplotlib>=3.7.0             # Plotting and visualization
# plotly>=5.15.0                 # Interactive plotting
# dash>=2.11.0                   # Interactive web applications

# Installation Instructions:
# 1. Basic installation:
#    pip install -r requirements.txt
#
# 2. Development installation:
#    pip install -r requirements.txt pytest pytest-cov
#
# 3. Full installation with optional features:
#    pip install -r requirements.txt pandas numpy matplotlib plotly dash

# Compatibility:
# - Python 3.8+
# - Windows, Linux, macOS
# - Minimal system requirements
# - <50MB total installation size