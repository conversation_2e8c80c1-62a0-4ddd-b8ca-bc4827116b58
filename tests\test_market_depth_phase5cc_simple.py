"""
Phase 5CC: Simple test for market_depth_visualizer/models.py to push from 69% to 90%+ coverage.
"""

import pytest
from datetime import datetime, timezone
from unittest.mock import patch
from src.forex_bot.market_depth_visualizer.models import (
    MarketDepthSnapshot, MarketDepthDashboard, DashboardSettings
)


class TestMarketDepthPhase5CCSimple:
    """Simple test to push market_depth_visualizer/models.py to 90%+ coverage"""

    def test_dashboard_validator(self):
        """Test MarketDepthDashboard validator for empty visualizations"""
        
        now = datetime.now(timezone.utc)
        
        # Test empty visualizations should fail
        with pytest.raises(ValueError, match="Visualizations cannot be empty"):
            MarketDepthDashboard(
                symbol="EURUSD",
                timestamp=now,
                visualizations={},  # Empty dict should trigger validator
                settings=DashboardSettings()
            )

    def test_pydantic_fallback(self):
        """Test pydantic fallback classes"""
        
        # Test with pydantic unavailable to trigger fallback classes
        with patch('src.forex_bot.market_depth_visualizer.models.PYDANTIC_AVAILABLE', False):
            # Force module reload to use fallback classes
            import importlib
            import src.forex_bot.market_depth_visualizer.models as models_module
            importlib.reload(models_module)
            
            # Test fallback DepthChartSettings
            depth_settings = models_module.DepthChartSettings(
                price_levels=15,
                show_imbalances=False
            )
            assert depth_settings.price_levels == 15
            assert depth_settings.show_imbalances is False
            
            # Test dict method
            settings_dict = depth_settings.dict()
            assert settings_dict["price_levels"] == 15
            
            # Test fallback HeatmapSettings
            heatmap_settings = models_module.HeatmapSettings(
                time_window=120,
                normalization="log"
            )
            assert heatmap_settings.time_window == 120
            assert heatmap_settings.normalization == "log"