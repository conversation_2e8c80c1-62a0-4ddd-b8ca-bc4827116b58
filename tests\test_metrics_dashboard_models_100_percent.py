"""
Comprehensive tests to push metrics_dashboard/models.py to 100% coverage.

This module targets all remaining uncovered lines to achieve complete coverage.
"""

import pytest
import sys
from unittest.mock import patch, MagicMock
from datetime import datetime, timezone
from pydantic import ValidationError

class TestMetricsDashboardModels100Percent:
    """Comprehensive tests to achieve 100% coverage for metrics_dashboard/models.py."""

    def test_import_error_handling_numpy(self):
        """Test numpy import error handling - covers lines 14-15."""
        # Test the actual import behavior by checking the module state
        from src.forex_bot.metrics_dashboard import models
        
        # The module should have NUMPY_AVAILABLE set based on actual import
        assert hasattr(models, 'NUMPY_AVAILABLE')
        assert isinstance(models.NUMPY_AVAILABLE, bool)

    def test_import_error_handling_pandas(self):
        """Test pandas import error handling - covers lines 20-21."""
        # Test the actual import behavior by checking the module state
        from src.forex_bot.metrics_dashboard import models
        
        # The module should have PANDAS_AVAILABLE set based on actual import
        assert hasattr(models, 'PANDAS_AVAILABLE')
        assert isinstance(models.PANDAS_AVAILABLE, bool)

    def test_import_error_handling_pydantic(self):
        """Test pydantic import error handling - covers lines 26-35."""
        # Test the actual import behavior by checking the module state
        from src.forex_bot.metrics_dashboard import models
        
        # The module should have PYDANTIC_AVAILABLE set based on actual import
        assert hasattr(models, 'PYDANTIC_AVAILABLE')
        assert isinstance(models.PYDANTIC_AVAILABLE, bool)
        
        # If pydantic is not available, test the fallback BaseModel
        if not models.PYDANTIC_AVAILABLE:
            BaseModel = models.BaseModel
            
            # Test __init__ method
            model = BaseModel(name="test", value=123)
            assert model.name == "test"
            assert model.value == 123
            
            # Test dict method
            result = model.dict()
            assert result == {"name": "test", "value": 123}
            
            # Test that private attributes are excluded
            model._private = "hidden"
            result = model.dict()
            assert "_private" not in result

    def test_metric_value_numpy_nan_validation(self):
        """Test MetricValue numpy NaN validation - covers line 82."""
        from src.forex_bot.metrics_dashboard.models import MetricValue, MetricCategory
        
        # Mock numpy to be available and provide NaN
        with patch('src.forex_bot.metrics_dashboard.models.NUMPY_AVAILABLE', True):
            with patch('src.forex_bot.metrics_dashboard.models.np') as mock_np:
                # Test NaN validation
                mock_np.isnan.return_value = True
                mock_np.isinf.return_value = False
                
                with pytest.raises(ValidationError) as exc_info:
                    MetricValue(
                        name="test_metric",
                        value=float('nan'),  # This will trigger the validation
                        unit="USD",
                        timestamp=datetime.now(timezone.utc),
                        category=MetricCategory.PERFORMANCE
                    )
                
                assert "value cannot be NaN or Inf" in str(exc_info.value)

    def test_metric_value_numpy_inf_validation(self):
        """Test MetricValue numpy Inf validation - covers line 82."""
        from src.forex_bot.metrics_dashboard.models import MetricValue, MetricCategory
        
        # Mock numpy to be available and provide Inf
        with patch('src.forex_bot.metrics_dashboard.models.NUMPY_AVAILABLE', True):
            with patch('src.forex_bot.metrics_dashboard.models.np') as mock_np:
                # Test Inf validation
                mock_np.isnan.return_value = False
                mock_np.isinf.return_value = True
                
                with pytest.raises(ValidationError) as exc_info:
                    MetricValue(
                        name="test_metric",
                        value=float('inf'),  # This will trigger the validation
                        unit="USD",
                        timestamp=datetime.now(timezone.utc),
                        category=MetricCategory.PERFORMANCE
                    )
                
                assert "value cannot be NaN or Inf" in str(exc_info.value)

    def test_metric_time_series_numpy_validation(self):
        """Test MetricTimeSeries numpy validation - covers lines 99-103."""
        from src.forex_bot.metrics_dashboard.models import MetricTimeSeries, MetricCategory
        
        # Mock numpy to be available
        with patch('src.forex_bot.metrics_dashboard.models.NUMPY_AVAILABLE', True):
            with patch('src.forex_bot.metrics_dashboard.models.np') as mock_np:
                # Test NaN in values validation
                mock_np.isnan.side_effect = lambda x: x != x  # NaN check
                mock_np.isinf.side_effect = lambda x: x == float('inf')  # Inf check
                
                timestamps = [datetime.now(timezone.utc), datetime.now(timezone.utc)]
                
                with pytest.raises(ValidationError) as exc_info:
                    MetricTimeSeries(
                        name="test_series",
                        values=[1.0, float('nan')],  # Contains NaN
                        timestamps=timestamps,
                        category=MetricCategory.PERFORMANCE
                    )
                
                assert "values cannot contain NaN or Inf" in str(exc_info.value)

    def test_metric_time_series_to_dataframe_pandas_error(self):
        """Test MetricTimeSeries to_dataframe pandas ImportError - covers lines 107-110."""
        from src.forex_bot.metrics_dashboard.models import MetricTimeSeries, MetricCategory
        
        # Create a valid MetricTimeSeries
        timestamps = [datetime.now(timezone.utc), datetime.now(timezone.utc)]
        series = MetricTimeSeries(
            name="test_series",
            values=[1.0, 2.0],
            timestamps=timestamps,
            category=MetricCategory.PERFORMANCE
        )
        
        # Mock PANDAS_AVAILABLE to be False
        with patch('src.forex_bot.metrics_dashboard.models.PANDAS_AVAILABLE', False):
            with pytest.raises(ImportError) as exc_info:
                series.to_dataframe()
            
            assert "pandas is required for to_dataframe()" in str(exc_info.value)

    def test_performance_metrics_validation_positive(self):
        """Test PerformanceMetrics positive validation - covers lines 131-133."""
        from src.forex_bot.metrics_dashboard.models import PerformanceMetrics
        
        with pytest.raises(ValidationError) as exc_info:
            PerformanceMetrics(
                total_profit_loss=100.0,
                win_loss_ratio=-1.0,  # Invalid negative value
                average_win=50.0,
                average_loss=-25.0,
                max_drawdown=-10.0,
                roi=15.0,
                profit_factor=2.0,
                timestamp=datetime.now(timezone.utc)
            )
        
        assert "value must be positive" in str(exc_info.value)

    def test_performance_metrics_validation_drawdown(self):
        """Test PerformanceMetrics drawdown validation - covers lines 137-139."""
        from src.forex_bot.metrics_dashboard.models import PerformanceMetrics
        
        with pytest.raises(ValidationError) as exc_info:
            PerformanceMetrics(
                total_profit_loss=100.0,
                win_loss_ratio=2.0,
                average_win=50.0,
                average_loss=-25.0,
                max_drawdown=10.0,  # Invalid positive drawdown
                roi=15.0,
                profit_factor=2.0,
                timestamp=datetime.now(timezone.utc)
            )
        
        assert "max_drawdown must be negative or zero" in str(exc_info.value)

    def test_trade_metrics_validation_non_negative(self):
        """Test TradeMetrics non-negative validation - covers lines 154-156."""
        from src.forex_bot.metrics_dashboard.models import TradeMetrics
        
        with pytest.raises(ValidationError) as exc_info:
            TradeMetrics(
                total_trades=-1,  # Invalid negative value
                winning_trades=5,
                losing_trades=3,
                average_trade_duration=3600.0,
                trade_frequency=2.5,
                average_position_size=1000.0,
                timestamp=datetime.now(timezone.utc)
            )
        
        assert "value must be non-negative" in str(exc_info.value)

    def test_trade_metrics_validation_trades_exceed_total(self):
        """Test TradeMetrics trades validation - covers lines 160-163."""
        from src.forex_bot.metrics_dashboard.models import TradeMetrics
        
        with pytest.raises(ValidationError) as exc_info:
            TradeMetrics(
                total_trades=5,
                winning_trades=8,  # Exceeds total_trades
                losing_trades=3,
                average_trade_duration=3600.0,
                trade_frequency=2.5,
                average_position_size=1000.0,
                timestamp=datetime.now(timezone.utc)
            )
        
        assert "winning_trades and losing_trades cannot exceed total_trades" in str(exc_info.value)

    def test_market_metrics_validation_volatility(self):
        """Test MarketMetrics volatility validation - covers lines 178-180."""
        from src.forex_bot.metrics_dashboard.models import MarketMetrics
        
        with pytest.raises(ValidationError) as exc_info:
            MarketMetrics(
                volatility=-0.5,  # Invalid negative volatility
                trend_strength=0.5,
                timestamp=datetime.now(timezone.utc),
                symbol="EURUSD"
            )
        
        assert "volatility must be non-negative" in str(exc_info.value)

    def test_market_metrics_validation_trend_strength(self):
        """Test MarketMetrics trend strength validation - covers lines 184-186."""
        from src.forex_bot.metrics_dashboard.models import MarketMetrics
        
        with pytest.raises(ValidationError) as exc_info:
            MarketMetrics(
                volatility=0.5,
                trend_strength=2.0,  # Invalid value > 1.0
                timestamp=datetime.now(timezone.utc),
                symbol="EURUSD"
            )
        
        assert "trend_strength must be between -1.0 and 1.0" in str(exc_info.value)

    def test_market_metrics_validation_sentiment_score(self):
        """Test MarketMetrics sentiment score validation - covers lines 190-192."""
        from src.forex_bot.metrics_dashboard.models import MarketMetrics
        
        with pytest.raises(ValidationError) as exc_info:
            MarketMetrics(
                volatility=0.5,
                trend_strength=0.5,
                sentiment_score=1.5,  # Invalid value > 1.0
                timestamp=datetime.now(timezone.utc),
                symbol="EURUSD"
            )
        
        assert "sentiment_score must be between -1.0 and 1.0" in str(exc_info.value)

    def test_system_metrics_validation_non_negative(self):
        """Test SystemMetrics non-negative validation - covers lines 206-208."""
        from src.forex_bot.metrics_dashboard.models import SystemMetrics
        
        with pytest.raises(ValidationError) as exc_info:
            SystemMetrics(
                execution_latency=-5.0,  # Invalid negative value
                order_fill_rate=95.0,
                system_uptime=3600.0,
                error_rate=0.1,
                cpu_usage=50.0,
                memory_usage=60.0,
                timestamp=datetime.now(timezone.utc)
            )
        
        assert "value must be non-negative" in str(exc_info.value)

    def test_system_metrics_validation_percentage(self):
        """Test SystemMetrics percentage validation - covers lines 212-214."""
        from src.forex_bot.metrics_dashboard.models import SystemMetrics
        
        with pytest.raises(ValidationError) as exc_info:
            SystemMetrics(
                execution_latency=5.0,
                order_fill_rate=150.0,  # Invalid percentage > 100
                system_uptime=3600.0,
                error_rate=0.1,
                cpu_usage=50.0,
                memory_usage=60.0,
                timestamp=datetime.now(timezone.utc)
            )
        
        assert "value must be between 0 and 100" in str(exc_info.value)

    def test_dashboard_layout_validation_positive(self):
        """Test DashboardLayout positive validation - covers line 236."""
        from src.forex_bot.metrics_dashboard.models import DashboardLayout
        
        with pytest.raises(ValidationError) as exc_info:
            DashboardLayout(
                rows=0,  # Invalid non-positive value
                columns=2,
                charts=[]
            )
        
        assert "value must be positive" in str(exc_info.value)

    def test_dashboard_layout_chart_validation_row_bounds(self):
        """Test DashboardLayout chart validation for row bounds - covers lines 241-255."""
        from src.forex_bot.metrics_dashboard.models import DashboardLayout
        
        with pytest.raises(ValidationError) as exc_info:
            DashboardLayout(
                rows=2,
                columns=2,
                charts=[
                    {
                        'chart_id': 'test',
                        'row': 3,  # Invalid row (>= rows)
                        'column': 0
                    }
                ]
            )
        
        assert "row must be between 0 and 1" in str(exc_info.value)

    def test_dashboard_layout_chart_validation_column_bounds(self):
        """Test DashboardLayout chart validation for column bounds - covers lines 241-255."""
        from src.forex_bot.metrics_dashboard.models import DashboardLayout
        
        with pytest.raises(ValidationError) as exc_info:
            DashboardLayout(
                rows=2,
                columns=2,
                charts=[
                    {
                        'chart_id': 'test',
                        'row': 0,
                        'column': 3  # Invalid column (>= columns)
                    }
                ]
            )
        
        assert "column must be between 0 and 1" in str(exc_info.value)

    def test_dashboard_layout_chart_validation_row_span(self):
        """Test DashboardLayout chart validation for row_span - covers lines 241-255."""
        from src.forex_bot.metrics_dashboard.models import DashboardLayout
        
        with pytest.raises(ValidationError) as exc_info:
            DashboardLayout(
                rows=2,
                columns=2,
                charts=[
                    {
                        'chart_id': 'test',
                        'row': 0,
                        'column': 0,
                        'row_span': 0  # Invalid row_span (<= 0)
                    }
                ]
            )
        
        assert "row_span must be positive" in str(exc_info.value)

    def test_dashboard_layout_chart_validation_col_span(self):
        """Test DashboardLayout chart validation for col_span - covers lines 241-255."""
        from src.forex_bot.metrics_dashboard.models import DashboardLayout
        
        with pytest.raises(ValidationError) as exc_info:
            DashboardLayout(
                rows=2,
                columns=2,
                charts=[
                    {
                        'chart_id': 'test',
                        'row': 0,
                        'column': 0,
                        'col_span': -1  # Invalid col_span (<= 0)
                    }
                ]
            )
        
        assert "col_span must be positive" in str(exc_info.value)

    def test_dashboard_custom_dates_validation_required(self):
        """Test Dashboard custom dates validation - covers lines 270-274."""
        from src.forex_bot.metrics_dashboard.models import Dashboard, DashboardLayout, TimeFrame, ChartData, ChartType, MetricCategory
        
        layout = DashboardLayout(rows=1, columns=1, charts=[])
        
        with pytest.raises(ValidationError) as exc_info:
            Dashboard(
                name="test_dashboard",
                layout=layout,
                charts={},
                time_frame=TimeFrame.CUSTOM,
                custom_start_date=None,  # Required when time_frame is CUSTOM
                custom_end_date=None,
                last_updated=datetime.now(timezone.utc)
            )
        
        assert "custom_start_date and custom_end_date are required when time_frame is CUSTOM" in str(exc_info.value)

    def test_dashboard_custom_end_date_validation(self):
        """Test Dashboard custom end date validation - covers lines 278-282."""
        from src.forex_bot.metrics_dashboard.models import Dashboard, DashboardLayout, TimeFrame
        
        layout = DashboardLayout(rows=1, columns=1, charts=[])
        start_date = datetime.now(timezone.utc)
        end_date = start_date - timezone.utc.utcoffset(None) if timezone.utc.utcoffset(None) else start_date.replace(hour=start_date.hour - 1)
        
        with pytest.raises(ValidationError) as exc_info:
            Dashboard(
                name="test_dashboard",
                layout=layout,
                charts={},
                time_frame=TimeFrame.CUSTOM,
                custom_start_date=start_date,
                custom_end_date=end_date,  # Before start_date
                last_updated=datetime.now(timezone.utc)
            )
        
        assert "custom_end_date must be after custom_start_date" in str(exc_info.value)

    def test_successful_model_creation(self):
        """Test successful creation of all models to ensure basic functionality."""
        from src.forex_bot.metrics_dashboard.models import (
            MetricValue, MetricTimeSeries, PerformanceMetrics, TradeMetrics,
            MarketMetrics, SystemMetrics, ChartData, DashboardLayout, Dashboard,
            MetricCategory, ChartType, TimeFrame
        )
        
        timestamp = datetime.now(timezone.utc)
        
        # Test MetricValue
        metric_value = MetricValue(
            name="test_metric",
            value=123.45,
            unit="USD",
            timestamp=timestamp,
            category=MetricCategory.PERFORMANCE
        )
        assert metric_value.name == "test_metric"
        
        # Test MetricTimeSeries
        timestamps = [timestamp, timestamp]
        series = MetricTimeSeries(
            name="test_series",
            values=[1.0, 2.0],
            timestamps=timestamps,
            category=MetricCategory.PERFORMANCE
        )
        assert series.name == "test_series"
        
        # Test PerformanceMetrics
        perf_metrics = PerformanceMetrics(
            total_profit_loss=100.0,
            win_loss_ratio=2.0,
            average_win=50.0,
            average_loss=-25.0,
            max_drawdown=-10.0,
            roi=15.0,
            profit_factor=2.0,
            timestamp=timestamp
        )
        assert perf_metrics.total_profit_loss == 100.0
        
        # Test TradeMetrics
        trade_metrics = TradeMetrics(
            total_trades=10,
            winning_trades=6,
            losing_trades=4,
            average_trade_duration=3600.0,
            trade_frequency=2.5,
            average_position_size=1000.0,
            timestamp=timestamp
        )
        assert trade_metrics.total_trades == 10
        
        # Test MarketMetrics
        market_metrics = MarketMetrics(
            volatility=0.5,
            trend_strength=0.3,
            timestamp=timestamp,
            symbol="EURUSD"
        )
        assert market_metrics.symbol == "EURUSD"
        
        # Test SystemMetrics
        system_metrics = SystemMetrics(
            execution_latency=5.0,
            order_fill_rate=95.0,
            system_uptime=3600.0,
            error_rate=0.1,
            cpu_usage=50.0,
            memory_usage=60.0,
            timestamp=timestamp
        )
        assert system_metrics.cpu_usage == 50.0
        
        # Test ChartData
        chart_data = ChartData(
            chart_type=ChartType.LINE,
            title="Test Chart",
            data=[1, 2, 3],
            category=MetricCategory.PERFORMANCE,
            timestamp=timestamp
        )
        assert chart_data.title == "Test Chart"
        
        # Test DashboardLayout
        layout = DashboardLayout(
            rows=2,
            columns=2,
            charts=[
                {
                    'chart_id': 'test',
                    'row': 0,
                    'column': 0,
                    'row_span': 1,
                    'col_span': 1
                }
            ]
        )
        assert layout.rows == 2
        
        # Test Dashboard
        dashboard = Dashboard(
            name="test_dashboard",
            layout=layout,
            charts={'test': chart_data},
            time_frame=TimeFrame.TODAY,
            last_updated=timestamp
        )
        assert dashboard.name == "test_dashboard"

    def test_metric_time_series_to_dataframe_success(self):
        """Test MetricTimeSeries to_dataframe success case."""
        from src.forex_bot.metrics_dashboard.models import MetricTimeSeries, MetricCategory
        
        # Mock pandas to be available
        with patch('src.forex_bot.metrics_dashboard.models.PANDAS_AVAILABLE', True):
            with patch('src.forex_bot.metrics_dashboard.models.pd') as mock_pd:
                mock_dataframe = MagicMock()
                mock_pd.DataFrame.return_value = mock_dataframe
                
                timestamps = [datetime.now(timezone.utc), datetime.now(timezone.utc)]
                series = MetricTimeSeries(
                    name="test_series",
                    values=[1.0, 2.0],
                    timestamps=timestamps,
                    category=MetricCategory.PERFORMANCE
                )
                
                result = series.to_dataframe()
                
                # Verify pandas DataFrame was called with correct data
                mock_pd.DataFrame.assert_called_once_with({
                    'timestamp': timestamps,
                    'value': [1.0, 2.0]
                })
                assert result == mock_dataframe