"""
Phase 4 comprehensive tests to push order_flow_analyzer/models.py to 90%+ coverage.
"""

import pytest
from datetime import datetime, timezone

class TestOrderFlowAnalyzerModelsPhase4:
    """Phase 4 tests to achieve 90%+ coverage for order_flow_analyzer/models.py."""

    def test_pydantic_order_flow_imbalance_validation(self):
        """Test pydantic OrderFlowImbalance validation errors."""
        from src.forex_bot.order_flow_analyzer import models
        
        timestamp = datetime.now(timezone.utc)
        
        # Test imbalance_ratio validation - out of range
        with pytest.raises(ValueError, match="Imbalance ratio must be between -1.0 and 1.0"):
            models.OrderFlowImbalance(
                symbol='EUR',
                timestamp=timestamp,
                price_level=1.0,
                bid_volume=100,
                ask_volume=100,
                imbalance_ratio=1.5,
                imbalance_level=models.ImbalanceLevel.STRONG_BUY,
                is_significant=True
            )
        
        with pytest.raises(ValueError, match="Imbalance ratio must be between -1.0 and 1.0"):
            models.OrderFlowImbalance(
                symbol='EUR',
                timestamp=timestamp,
                price_level=1.0,
                bid_volume=100,
                ask_volume=100,
                imbalance_ratio=-1.5,
                imbalance_level=models.ImbalanceLevel.STRONG_BUY,
                is_significant=True
            )