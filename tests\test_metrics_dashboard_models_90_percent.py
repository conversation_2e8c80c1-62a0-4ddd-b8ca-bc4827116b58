"""
Targeted tests to push metrics_dashboard/models.py to 90% coverage.

This module specifically targets the remaining uncovered lines:
- Lines 14-15: numpy ImportError handling
- Lines 20-21: pandas ImportError handling  
- Lines 26-35: pydantic ImportError handling and fallback BaseModel
- Line 82: numpy NaN/Inf validation in MetricValue
- Lines 96-103: numpy NaN/Inf validation in MetricTimeSeries
- Lines 107-110: pandas ImportError in to_dataframe method
- Lines 249, 251, 253: Chart layout validation logic
"""

import pytest
import sys
from unittest.mock import patch, MagicMock
from datetime import datetime, timezone
from pydantic import ValidationError

# Test import error handling by mocking module imports
class TestMetricsDashboardModels90Percent:
    """Targeted tests to achieve 90% coverage for metrics_dashboard/models.py."""

    def test_numpy_import_error_handling(self):
        """Test numpy ImportError handling - covers lines 14-15."""
        # Mock numpy import to fail
        with patch.dict('sys.modules', {'numpy': None}):
            with patch('builtins.__import__', side_effect=lambda name, *args: 
                       ImportError("No module named 'numpy'") if name == 'numpy' else __import__(name, *args)):
                # Re-import the module to trigger the ImportError
                import importlib
                import src.forex_bot.metrics_dashboard.models
                importlib.reload(src.forex_bot.metrics_dashboard.models)
                
                # Verify NUMPY_AVAILABLE is False
                assert src.forex_bot.metrics_dashboard.models.NUMPY_AVAILABLE is False

    def test_pandas_import_error_handling(self):
        """Test pandas ImportError handling - covers lines 20-21."""
        # Mock pandas import to fail
        with patch.dict('sys.modules', {'pandas': None}):
            with patch('builtins.__import__', side_effect=lambda name, *args: 
                       ImportError("No module named 'pandas'") if name == 'pandas' else __import__(name, *args)):
                # Re-import the module to trigger the ImportError
                import importlib
                import src.forex_bot.metrics_dashboard.models
                importlib.reload(src.forex_bot.metrics_dashboard.models)
                
                # Verify PANDAS_AVAILABLE is False
                assert src.forex_bot.metrics_dashboard.models.PANDAS_AVAILABLE is False

    def test_pydantic_import_error_handling(self):
        """Test pydantic ImportError handling and fallback BaseModel - covers lines 26-35."""
        # Mock pydantic import to fail
        with patch.dict('sys.modules', {'pydantic': None}):
            with patch('builtins.__import__', side_effect=lambda name, *args: 
                       ImportError("No module named 'pydantic'") if name == 'pydantic' else __import__(name, *args)):
                # Re-import the module to trigger the ImportError
                import importlib
                import src.forex_bot.metrics_dashboard.models
                importlib.reload(src.forex_bot.metrics_dashboard.models)
                
                # Verify PYDANTIC_AVAILABLE is False
                assert src.forex_bot.metrics_dashboard.models.PYDANTIC_AVAILABLE is False
                
                # Test the fallback BaseModel
                BaseModel = src.forex_bot.metrics_dashboard.models.BaseModel
                
                # Test __init__ method
                model = BaseModel(name="test", value=123)
                assert model.name == "test"
                assert model.value == 123
                
                # Test dict method
                result = model.dict()
                assert result == {"name": "test", "value": 123}
                
                # Test that private attributes are excluded
                model._private = "hidden"
                result = model.dict()
                assert "_private" not in result

    def test_metric_value_numpy_nan_validation(self):
        """Test MetricValue numpy NaN validation - covers line 82."""
        from src.forex_bot.metrics_dashboard.models import MetricValue, MetricCategory
        
        # Mock numpy to be available and provide NaN
        with patch('src.forex_bot.metrics_dashboard.models.NUMPY_AVAILABLE', True):
            with patch('src.forex_bot.metrics_dashboard.models.np') as mock_np:
                # Test NaN validation
                mock_np.isnan.return_value = True
                mock_np.isinf.return_value = False
                
                with pytest.raises(ValidationError) as exc_info:
                    MetricValue(
                        name="test_metric",
                        value=float('nan'),  # This will trigger the validation
                        unit="USD",
                        category=MetricCategory.PERFORMANCE
                    )
                
                assert "value cannot be NaN or Inf" in str(exc_info.value)

    def test_metric_value_numpy_inf_validation(self):
        """Test MetricValue numpy Inf validation - covers line 82."""
        from src.forex_bot.metrics_dashboard.models import MetricValue, MetricCategory
        
        # Mock numpy to be available and provide Inf
        with patch('src.forex_bot.metrics_dashboard.models.NUMPY_AVAILABLE', True):
            with patch('src.forex_bot.metrics_dashboard.models.np') as mock_np:
                # Test Inf validation
                mock_np.isnan.return_value = False
                mock_np.isinf.return_value = True
                
                with pytest.raises(ValidationError) as exc_info:
                    MetricValue(
                        name="test_metric",
                        value=float('inf'),  # This will trigger the validation
                        unit="USD",
                        category=MetricCategory.PERFORMANCE
                    )
                
                assert "value cannot be NaN or Inf" in str(exc_info.value)

    def test_metric_time_series_numpy_validation(self):
        """Test MetricTimeSeries numpy validation - covers lines 96-103."""
        from src.forex_bot.metrics_dashboard.models import MetricTimeSeries, MetricCategory
        
        # Mock numpy to be available
        with patch('src.forex_bot.metrics_dashboard.models.NUMPY_AVAILABLE', True):
            with patch('src.forex_bot.metrics_dashboard.models.np') as mock_np:
                # Test NaN in values validation
                mock_np.isnan.side_effect = lambda x: x != x  # NaN check
                mock_np.isinf.side_effect = lambda x: x == float('inf')  # Inf check
                
                timestamps = [datetime.now(timezone.utc), datetime.now(timezone.utc)]
                
                with pytest.raises(ValidationError) as exc_info:
                    MetricTimeSeries(
                        name="test_series",
                        values=[1.0, float('nan')],  # Contains NaN
                        timestamps=timestamps,
                        category=MetricCategory.PERFORMANCE
                    )
                
                assert "values cannot contain NaN or Inf" in str(exc_info.value)

    def test_metric_time_series_length_validation(self):
        """Test MetricTimeSeries length validation - covers lines 96-103."""
        from src.forex_bot.metrics_dashboard.models import MetricTimeSeries, MetricCategory
        
        timestamps = [datetime.now(timezone.utc)]
        
        with pytest.raises(ValidationError) as exc_info:
            MetricTimeSeries(
                name="test_series",
                values=[1.0, 2.0],  # 2 values
                timestamps=timestamps,  # 1 timestamp
                category=MetricCategory.PERFORMANCE
            )
        
        assert "values and timestamps must have the same length" in str(exc_info.value)

    def test_metric_time_series_to_dataframe_pandas_error(self):
        """Test MetricTimeSeries to_dataframe pandas ImportError - covers lines 107-110."""
        from src.forex_bot.metrics_dashboard.models import MetricTimeSeries, MetricCategory
        
        # Create a valid MetricTimeSeries
        timestamps = [datetime.now(timezone.utc), datetime.now(timezone.utc)]
        series = MetricTimeSeries(
            name="test_series",
            values=[1.0, 2.0],
            timestamps=timestamps,
            category=MetricCategory.PERFORMANCE
        )
        
        # Mock PANDAS_AVAILABLE to be False
        with patch('src.forex_bot.metrics_dashboard.models.PANDAS_AVAILABLE', False):
            with pytest.raises(ImportError) as exc_info:
                series.to_dataframe()
            
            assert "pandas is required for to_dataframe()" in str(exc_info.value)

    def test_dashboard_layout_chart_validation_row_bounds(self):
        """Test Dashboard chart validation for row bounds - covers line 249."""
        from src.forex_bot.metrics_dashboard.models import Dashboard, DashboardLayout
        
        # Create layout with 2 rows
        layout = DashboardLayout(rows=2, columns=2)
        
        with pytest.raises(ValidationError) as exc_info:
            Dashboard(
                name="test_dashboard",
                layout=layout,
                charts=[
                    {
                        'type': 'line',
                        'title': 'Test Chart',
                        'row': 3,  # Invalid row (>= rows)
                        'column': 0
                    }
                ]
            )
        
        assert "row must be between 0 and 1" in str(exc_info.value)

    def test_dashboard_layout_chart_validation_column_bounds(self):
        """Test Dashboard chart validation for column bounds - covers line 251."""
        from src.forex_bot.metrics_dashboard.models import Dashboard, DashboardLayout
        
        # Create layout with 2 columns
        layout = DashboardLayout(rows=2, columns=2)
        
        with pytest.raises(ValidationError) as exc_info:
            Dashboard(
                name="test_dashboard",
                layout=layout,
                charts=[
                    {
                        'type': 'line',
                        'title': 'Test Chart',
                        'row': 0,
                        'column': 3  # Invalid column (>= columns)
                    }
                ]
            )
        
        assert "column must be between 0 and 1" in str(exc_info.value)

    def test_dashboard_layout_chart_validation_row_span(self):
        """Test Dashboard chart validation for row_span - covers line 253."""
        from src.forex_bot.metrics_dashboard.models import Dashboard, DashboardLayout
        
        layout = DashboardLayout(rows=2, columns=2)
        
        with pytest.raises(ValidationError) as exc_info:
            Dashboard(
                name="test_dashboard",
                layout=layout,
                charts=[
                    {
                        'type': 'line',
                        'title': 'Test Chart',
                        'row': 0,
                        'column': 0,
                        'row_span': 0  # Invalid row_span (<= 0)
                    }
                ]
            )
        
        assert "row_span must be positive" in str(exc_info.value)

    def test_dashboard_layout_chart_validation_col_span(self):
        """Test Dashboard chart validation for col_span - covers line 253."""
        from src.forex_bot.metrics_dashboard.models import Dashboard, DashboardLayout
        
        layout = DashboardLayout(rows=2, columns=2)
        
        with pytest.raises(ValidationError) as exc_info:
            Dashboard(
                name="test_dashboard",
                layout=layout,
                charts=[
                    {
                        'type': 'line',
                        'title': 'Test Chart',
                        'row': 0,
                        'column': 0,
                        'col_span': -1  # Invalid col_span (<= 0)
                    }
                ]
            )
        
        assert "col_span must be positive" in str(exc_info.value)

    def test_dashboard_layout_chart_validation_negative_row(self):
        """Test Dashboard chart validation for negative row - covers line 249."""
        from src.forex_bot.metrics_dashboard.models import Dashboard, DashboardLayout
        
        layout = DashboardLayout(rows=2, columns=2)
        
        with pytest.raises(ValidationError) as exc_info:
            Dashboard(
                name="test_dashboard",
                layout=layout,
                charts=[
                    {
                        'type': 'line',
                        'title': 'Test Chart',
                        'row': -1,  # Invalid negative row
                        'column': 0
                    }
                ]
            )
        
        assert "row must be between 0 and 1" in str(exc_info.value)

    def test_dashboard_layout_chart_validation_negative_column(self):
        """Test Dashboard chart validation for negative column - covers line 251."""
        from src.forex_bot.metrics_dashboard.models import Dashboard, DashboardLayout
        
        layout = DashboardLayout(rows=2, columns=2)
        
        with pytest.raises(ValidationError) as exc_info:
            Dashboard(
                name="test_dashboard",
                layout=layout,
                charts=[
                    {
                        'type': 'line',
                        'title': 'Test Chart',
                        'row': 0,
                        'column': -1  # Invalid negative column
                    }
                ]
            )
        
        assert "column must be between 0 and 1" in str(exc_info.value)

    def test_metric_value_without_numpy_validation(self):
        """Test MetricValue validation when numpy is not available."""
        from src.forex_bot.metrics_dashboard.models import MetricValue, MetricCategory
        
        # Mock numpy to be unavailable
        with patch('src.forex_bot.metrics_dashboard.models.NUMPY_AVAILABLE', False):
            # Should work fine without numpy validation
            metric = MetricValue(
                name="test_metric",
                value=123.45,
                unit="USD",
                category=MetricCategory.PERFORMANCE
            )
            
            assert metric.name == "test_metric"
            assert metric.value == 123.45

    def test_metric_time_series_without_numpy_validation(self):
        """Test MetricTimeSeries validation when numpy is not available."""
        from src.forex_bot.metrics_dashboard.models import MetricTimeSeries, MetricCategory
        
        # Mock numpy to be unavailable
        with patch('src.forex_bot.metrics_dashboard.models.NUMPY_AVAILABLE', False):
            timestamps = [datetime.now(timezone.utc), datetime.now(timezone.utc)]
            
            # Should work fine without numpy validation
            series = MetricTimeSeries(
                name="test_series",
                values=[1.0, 2.0],
                timestamps=timestamps,
                category=MetricCategory.PERFORMANCE
            )
            
            assert series.name == "test_series"
            assert len(series.values) == 2

    def test_metric_time_series_to_dataframe_success(self):
        """Test MetricTimeSeries to_dataframe success case."""
        from src.forex_bot.metrics_dashboard.models import MetricTimeSeries, MetricCategory
        
        # Mock pandas to be available
        with patch('src.forex_bot.metrics_dashboard.models.PANDAS_AVAILABLE', True):
            with patch('src.forex_bot.metrics_dashboard.models.pd') as mock_pd:
                mock_dataframe = MagicMock()
                mock_pd.DataFrame.return_value = mock_dataframe
                
                timestamps = [datetime.now(timezone.utc), datetime.now(timezone.utc)]
                series = MetricTimeSeries(
                    name="test_series",
                    values=[1.0, 2.0],
                    timestamps=timestamps,
                    category=MetricCategory.PERFORMANCE
                )
                
                result = series.to_dataframe()
                
                # Verify pandas DataFrame was called with correct data
                mock_pd.DataFrame.assert_called_once_with({
                    'timestamp': timestamps,
                    'value': [1.0, 2.0]
                })
                assert result == mock_dataframe