"""
Phase 5Z: Simple test for market_depth_visualizer/models.py to achieve 90%+ coverage.
"""

import pytest
from datetime import datetime, timezone
from src.forex_bot.market_depth_visualizer.models import (
    VisualizationType, ColorScheme, DepthChartSettings, TradeEntry, 
    MarketDepthSnapshot, MarketDepthVisualization, MarketDepthDashboard
)


class TestMarketDepthVisualizerModelsPhase5ZSimple:
    """Simple test class to push market_depth_visualizer/models.py to 90%+ coverage"""

    def test_enums_and_basic_models(self):
        """Test enums and basic model creation"""
        
        # Test enums
        assert VisualizationType.DEPTH_CHART == "depth_chart"
        assert ColorScheme.DEFAULT == "default"
        
        # Test DepthChartSettings
        settings = DepthChartSettings()
        assert settings.price_levels == 10
        assert settings.show_imbalances is True
        
        # Test custom settings
        custom_settings = DepthChartSettings(
            price_levels=20,
            show_imbalances=False,
            color_scheme=ColorScheme.DARK
        )
        assert custom_settings.price_levels == 20
        assert custom_settings.show_imbalances is False

    def test_trade_entry_validation(self):
        """Test TradeEntry model with validation"""
        
        now = datetime.now(timezone.utc)
        
        # Test valid trade
        trade = TradeEntry(
            timestamp=now,
            price=1.2345,
            volume=1000.0,
            direction="buy"
        )
        assert trade.volume == 1000.0
        assert trade.direction == "buy"
        
        # Test volume validation
        with pytest.raises(ValueError, match="Volume must be positive"):
            TradeEntry(
                timestamp=now,
                price=1.2345,
                volume=-100.0,
                direction="buy"
            )

    def test_market_depth_snapshot_properties(self):
        """Test MarketDepthSnapshot properties and validation"""
        
        now = datetime.now(timezone.utc)
        
        # Test valid snapshot
        snapshot = MarketDepthSnapshot(
            symbol="EURUSD",
            timestamp=now,
            bid_prices=[1.2340, 1.2339],
            bid_volumes=[1000.0, 1500.0],
            ask_prices=[1.2341, 1.2342],
            ask_volumes=[800.0, 1200.0]
        )
        
        # Test properties
        assert snapshot.best_bid == 1.2340
        assert snapshot.best_ask == 1.2341
        assert snapshot.spread == 0.0001
        assert snapshot.total_bid_volume == 2500.0
        assert snapshot.total_ask_volume == 2000.0
        
        # Test cumulative volumes
        assert snapshot.cumulative_bid_volumes == [1000.0, 2500.0]
        assert snapshot.cumulative_ask_volumes == [800.0, 2000.0]    def test_market_depth_snapshot_validation_errors(self):
        """Test MarketDepthSnapshot validation errors"""
        
        now = datetime.now(timezone.utc)
        
        # Test empty bid prices
        with pytest.raises(ValueError, match="Bid prices cannot be empty"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[],
                bid_volumes=[],
                ask_prices=[1.2341],
                ask_volumes=[800.0]
            )
        
        # Test empty ask prices
        with pytest.raises(ValueError, match="Ask prices cannot be empty"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340],
                bid_volumes=[1000.0],
                ask_prices=[],
                ask_volumes=[]
            )

    def test_market_depth_visualization(self):
        """Test MarketDepthVisualization model"""
        
        now = datetime.now(timezone.utc)
        
        # Test valid visualization
        viz = MarketDepthVisualization(
            symbol="EURUSD",
            timestamp=now,
            visualization_type=VisualizationType.DEPTH_CHART,
            image_data="base64_image_data",
            settings={"price_levels": 10}
        )
        
        assert viz.symbol == "EURUSD"
        assert viz.visualization_type == VisualizationType.DEPTH_CHART
        assert viz.image_data == "base64_image_data"
        
        # Test empty image data validation
        with pytest.raises(ValueError, match="Image data cannot be empty"):
            MarketDepthVisualization(
                symbol="EURUSD",
                timestamp=now,
                visualization_type=VisualizationType.HEATMAP,
                image_data="",
                settings={}
            )

    def test_market_depth_dashboard(self):
        """Test MarketDepthDashboard model"""
        
        now = datetime.now(timezone.utc)
        
        # Create visualization
        viz = MarketDepthVisualization(
            symbol="EURUSD",
            timestamp=now,
            visualization_type=VisualizationType.DEPTH_CHART,
            image_data="base64_data",
            settings={}
        )
        
        from src.forex_bot.market_depth_visualizer.models import DashboardSettings
        
        # Test valid dashboard
        dashboard = MarketDepthDashboard(
            symbol="EURUSD",
            timestamp=now,
            visualizations={VisualizationType.DEPTH_CHART: viz},
            settings=DashboardSettings()
        )
        
        assert dashboard.symbol == "EURUSD"
        assert len(dashboard.visualizations) == 1
        
        # Test empty visualizations validation
        with pytest.raises(ValueError, match="Visualizations cannot be empty"):
            MarketDepthDashboard(
                symbol="EURUSD",
                timestamp=now,
                visualizations={},
                settings=DashboardSettings()
            )