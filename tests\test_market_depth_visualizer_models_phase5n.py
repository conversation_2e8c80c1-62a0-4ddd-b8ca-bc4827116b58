"""
Phase 5N comprehensive tests to push market_depth_visualizer/models.py to 90%+ coverage.
"""

import pytest
from datetime import datetime, timezone
from src.forex_bot.market_depth_visualizer import models

class TestMarketDepthVisualizerModelsPhase5N:
    """Phase 5N tests to achieve 90%+ coverage for market_depth_visualizer/models.py."""

    def test_visualization_type_enum(self):
        """Test VisualizationType enum values."""
        assert models.VisualizationType.DEPTH_CHART.value == "depth_chart"
        assert models.VisualizationType.HEATMAP.value == "heatmap"
        assert models.VisualizationType.TIME_AND_SALES.value == "time_and_sales"
        assert models.VisualizationType.LIQUIDITY_MAP.value == "liquidity_map"
        assert models.VisualizationType.ORDER_FLOW_FOOTPRINT.value == "order_flow_footprint"
        assert models.VisualizationType.DASHBOARD.value == "dashboard"

    def test_color_scheme_enum(self):
        """Test ColorScheme enum values."""
        assert models.ColorScheme.DEFAULT.value == "default"
        assert models.ColorScheme.DARK.value == "dark"
        assert models.ColorScheme.LIGHT.value == "light"
        assert models.ColorScheme.COLORBLIND.value == "colorblind"
        assert models.ColorScheme.CUSTOM.value == "custom"

    def test_depth_chart_settings_basic_functionality(self):
        """Test DepthChartSettings class basic functionality."""
        
        # Test with default values
        settings = models.DepthChartSettings()
        assert settings.price_levels == 10
        assert settings.show_imbalances == True
        assert settings.show_current_price == True
        assert settings.color_scheme == models.ColorScheme.DEFAULT
        assert settings.custom_bid_color is None
        assert settings.custom_ask_color is None
        assert settings.log_scale == False
        assert settings.show_tooltips == True
        assert settings.highlight_large_orders == True
        assert settings.large_order_threshold == 2.0
        
        # Test with custom values
        custom_settings = models.DepthChartSettings(
            price_levels=20,
            show_imbalances=False,
            show_current_price=False,
            color_scheme=models.ColorScheme.DARK,
            custom_bid_color="#00FF00",
            custom_ask_color="#FF0000",
            log_scale=True,
            show_tooltips=False,
            highlight_large_orders=False,
            large_order_threshold=3.0
        )
        assert custom_settings.price_levels == 20
        assert custom_settings.show_imbalances == False
        assert custom_settings.show_current_price == False
        assert custom_settings.color_scheme == models.ColorScheme.DARK
        assert custom_settings.custom_bid_color == "#00FF00"
        assert custom_settings.custom_ask_color == "#FF0000"
        assert custom_settings.log_scale == True
        assert custom_settings.show_tooltips == False
        assert custom_settings.highlight_large_orders == False
        assert custom_settings.large_order_threshold == 3.0

    def test_heatmap_settings_basic_functionality(self):
        """Test HeatmapSettings class basic functionality."""
        
        # Test with default values
        settings = models.HeatmapSettings()
        assert settings.price_levels == 20
        assert settings.time_window == 60
        assert settings.color_scheme == models.ColorScheme.DEFAULT
        assert settings.custom_colormap is None
        assert settings.show_current_price == True
        assert settings.show_tooltips == True
        assert settings.interpolation == "nearest"
        assert settings.normalization == "linear"
        
        # Test with custom values
        custom_settings = models.HeatmapSettings(
            price_levels=30,
            time_window=120,
            color_scheme=models.ColorScheme.LIGHT,
            custom_colormap="viridis",
            show_current_price=False,
            show_tooltips=False,
            interpolation="bilinear",
            normalization="log"
        )
        assert custom_settings.price_levels == 30
        assert custom_settings.time_window == 120
        assert custom_settings.color_scheme == models.ColorScheme.LIGHT
        assert custom_settings.custom_colormap == "viridis"
        assert custom_settings.show_current_price == False
        assert custom_settings.show_tooltips == False
        assert custom_settings.interpolation == "bilinear"
        assert custom_settings.normalization == "log"

    def test_time_and_sales_settings_basic_functionality(self):
        """Test TimeAndSalesSettings class basic functionality."""
        
        # Test with default values
        settings = models.TimeAndSalesSettings()
        assert settings.max_entries == 100
        assert settings.show_direction == True
        assert settings.show_time == True
        assert settings.show_price == True
        assert settings.show_volume == True
        assert settings.color_scheme == models.ColorScheme.DEFAULT
        assert settings.custom_buy_color is None
        assert settings.custom_sell_color is None
        assert settings.highlight_large_trades == True
        assert settings.large_trade_threshold == 2.0
        
        # Test with custom values
        custom_settings = models.TimeAndSalesSettings(
            max_entries=200,
            show_direction=False,
            show_time=False,
            show_price=False,
            show_volume=False,
            color_scheme=models.ColorScheme.COLORBLIND,
            custom_buy_color="#00AA00",
            custom_sell_color="#AA0000",
            highlight_large_trades=False,
            large_trade_threshold=1.5
        )
        assert custom_settings.max_entries == 200
        assert custom_settings.show_direction == False
        assert custom_settings.show_time == False
        assert custom_settings.show_price == False
        assert custom_settings.show_volume == False
        assert custom_settings.color_scheme == models.ColorScheme.COLORBLIND
        assert custom_settings.custom_buy_color == "#00AA00"
        assert custom_settings.custom_sell_color == "#AA0000"
        assert custom_settings.highlight_large_trades == False
        assert custom_settings.large_trade_threshold == 1.5

    def test_liquidity_map_settings_basic_functionality(self):
        """Test LiquidityMapSettings class basic functionality."""
        
        # Test with default values
        settings = models.LiquidityMapSettings()
        assert settings.price_levels == 20
        assert settings.show_bid_liquidity == True
        assert settings.show_ask_liquidity == True
        assert settings.color_scheme == models.ColorScheme.DEFAULT
        assert settings.custom_bid_color is None
        assert settings.custom_ask_color is None
        assert settings.show_tooltips == True
        assert settings.normalization == "linear"
        
        # Test with custom values
        custom_settings = models.LiquidityMapSettings(
            price_levels=15,
            show_bid_liquidity=False,
            show_ask_liquidity=False,
            color_scheme=models.ColorScheme.CUSTOM,
            custom_bid_color="#0000FF",
            custom_ask_color="#FFFF00",
            show_tooltips=False,
            normalization="log"
        )
        assert custom_settings.price_levels == 15
        assert custom_settings.show_bid_liquidity == False
        assert custom_settings.show_ask_liquidity == False
        assert custom_settings.color_scheme == models.ColorScheme.CUSTOM
        assert custom_settings.custom_bid_color == "#0000FF"
        assert custom_settings.custom_ask_color == "#FFFF00"
        assert custom_settings.show_tooltips == False
        assert custom_settings.normalization == "log"

    def test_order_flow_footprint_settings_basic_functionality(self):
        """Test OrderFlowFootprintSettings class basic functionality."""
        
        # Test with default values
        settings = models.OrderFlowFootprintSettings()
        assert settings.price_levels == 20
        assert settings.time_window == 60
        assert settings.show_imbalances == True
        assert settings.color_scheme == models.ColorScheme.DEFAULT
        assert settings.custom_buy_color is None
        assert settings.custom_sell_color is None
        assert settings.show_tooltips == True
        assert settings.show_delta == True
        assert settings.delta_type == "volume"
        
        # Test with custom values
        custom_settings = models.OrderFlowFootprintSettings(
            price_levels=25,
            time_window=90,
            show_imbalances=False,
            color_scheme=models.ColorScheme.DARK,
            custom_buy_color="#008000",
            custom_sell_color="#800000",
            show_tooltips=False,
            show_delta=False,
            delta_type="trades"
        )
        assert custom_settings.price_levels == 25
        assert custom_settings.time_window == 90
        assert custom_settings.show_imbalances == False
        assert custom_settings.color_scheme == models.ColorScheme.DARK
        assert custom_settings.custom_buy_color == "#008000"
        assert custom_settings.custom_sell_color == "#800000"
        assert custom_settings.show_tooltips == False
        assert custom_settings.show_delta == False
        assert custom_settings.delta_type == "trades"

    def test_dashboard_settings_basic_functionality(self):
        """Test DashboardSettings class basic functionality."""
        
        # Test with default values
        settings = models.DashboardSettings()
        expected_layout = [
            [models.VisualizationType.DEPTH_CHART, models.VisualizationType.HEATMAP],
            [models.VisualizationType.TIME_AND_SALES, models.VisualizationType.LIQUIDITY_MAP]
        ]
        assert settings.layout == expected_layout
        assert settings.refresh_interval == 1000
        assert settings.show_title == True
        assert settings.show_timestamp == True
        assert settings.color_scheme == models.ColorScheme.DEFAULT
        assert settings.custom_background_color is None
        assert settings.custom_text_color is None
        
        # Test with custom values
        custom_layout = [
            [models.VisualizationType.DASHBOARD],
            [models.VisualizationType.ORDER_FLOW_FOOTPRINT]
        ]
        custom_settings = models.DashboardSettings(
            layout=custom_layout,
            refresh_interval=500,
            show_title=False,
            show_timestamp=False,
            color_scheme=models.ColorScheme.LIGHT,
            custom_background_color="#FFFFFF",
            custom_text_color="#000000"
        )
        assert custom_settings.layout == custom_layout
        assert custom_settings.refresh_interval == 500
        assert custom_settings.show_title == False
        assert custom_settings.show_timestamp == False
        assert custom_settings.color_scheme == models.ColorScheme.LIGHT
        assert custom_settings.custom_background_color == "#FFFFFF"
        assert custom_settings.custom_text_color == "#000000"

    def test_visualization_settings_basic_functionality(self):
        """Test VisualizationSettings class basic functionality."""
        
        # Test with default values
        settings = models.VisualizationSettings()
        assert isinstance(settings.depth_chart, models.DepthChartSettings)
        assert isinstance(settings.heatmap, models.HeatmapSettings)
        assert isinstance(settings.time_and_sales, models.TimeAndSalesSettings)
        assert isinstance(settings.liquidity_map, models.LiquidityMapSettings)
        assert isinstance(settings.order_flow_footprint, models.OrderFlowFootprintSettings)
        assert isinstance(settings.dashboard, models.DashboardSettings)
        
        # Test that all sub-settings have their default values
        assert settings.depth_chart.price_levels == 10
        assert settings.heatmap.price_levels == 20
        assert settings.time_and_sales.max_entries == 100
        assert settings.liquidity_map.price_levels == 20
        assert settings.order_flow_footprint.price_levels == 20
        assert settings.dashboard.refresh_interval == 1000

    def test_trade_entry_basic_functionality(self):
        """Test TradeEntry class basic functionality."""
        
        timestamp = datetime(2023, 12, 15, 10, 0, 0, tzinfo=timezone.utc)
        
        # Test buy trade
        buy_trade = models.TradeEntry(
            timestamp=timestamp,
            price=1.1000,
            volume=1000.0,
            direction="buy",
            is_large=False
        )
        assert buy_trade.timestamp == timestamp
        assert buy_trade.price == 1.1000
        assert buy_trade.volume == 1000.0
        assert buy_trade.direction == "buy"
        assert buy_trade.is_large == False
        
        # Test sell trade
        sell_trade = models.TradeEntry(
            timestamp=timestamp,
            price=1.0995,
            volume=2000.0,
            direction="sell",
            is_large=True
        )
        assert sell_trade.timestamp == timestamp
        assert sell_trade.price == 1.0995
        assert sell_trade.volume == 2000.0
        assert sell_trade.direction == "sell"
        assert sell_trade.is_large == True

    def test_trade_entry_validation(self):
        """Test TradeEntry validation."""
        
        timestamp = datetime(2023, 12, 15, 10, 0, 0, tzinfo=timezone.utc)
        
        # Test invalid volume
        with pytest.raises(ValueError, match="Volume must be positive"):
            models.TradeEntry(
                timestamp=timestamp,
                price=1.1000,
                volume=0.0,  # Invalid: not positive
                direction="buy"
            )
        
        with pytest.raises(ValueError, match="Volume must be positive"):
            models.TradeEntry(
                timestamp=timestamp,
                price=1.1000,
                volume=-100.0,  # Invalid: negative
                direction="buy"
            )

    def test_market_depth_snapshot_basic_functionality(self):
        """Test MarketDepthSnapshot class basic functionality."""
        
        timestamp = datetime(2023, 12, 15, 11, 0, 0, tzinfo=timezone.utc)
        
        # Create test trades
        trades = [
            models.TradeEntry(
                timestamp=timestamp,
                price=1.1000,
                volume=500.0,
                direction="buy"
            ),
            models.TradeEntry(
                timestamp=timestamp,
                price=1.0999,
                volume=300.0,
                direction="sell"
            )
        ]
        
        # Create market depth snapshot
        snapshot = models.MarketDepthSnapshot(
            symbol="EURUSD",
            timestamp=timestamp,
            bid_prices=[1.0998, 1.0997, 1.0996],
            bid_volumes=[1000.0, 1500.0, 2000.0],
            ask_prices=[1.1001, 1.1002, 1.1003],
            ask_volumes=[800.0, 1200.0, 1800.0],
            trades=trades
        )
        
        assert snapshot.symbol == "EURUSD"
        assert snapshot.timestamp == timestamp
        assert snapshot.bid_prices == [1.0998, 1.0997, 1.0996]
        assert snapshot.bid_volumes == [1000.0, 1500.0, 2000.0]
        assert snapshot.ask_prices == [1.1001, 1.1002, 1.1003]
        assert snapshot.ask_volumes == [800.0, 1200.0, 1800.0]
        assert len(snapshot.trades) == 2

    def test_market_depth_snapshot_properties(self):
        """Test MarketDepthSnapshot calculated properties."""
        
        timestamp = datetime(2023, 12, 15, 11, 0, 0, tzinfo=timezone.utc)
        
        snapshot = models.MarketDepthSnapshot(
            symbol="GBPUSD",
            timestamp=timestamp,
            bid_prices=[1.2500, 1.2499, 1.2498],
            bid_volumes=[1000.0, 1500.0, 2000.0],
            ask_prices=[1.2502, 1.2503, 1.2504],
            ask_volumes=[800.0, 1200.0, 1600.0]
        )
        
        # Test best bid/ask
        assert snapshot.best_bid == 1.2500
        assert snapshot.best_ask == 1.2502
        
        # Test spread
        assert abs(snapshot.spread - 0.0002) < 1e-10
        
        # Test mid price
        assert snapshot.mid_price == 1.2501
        
        # Test total volumes
        assert snapshot.total_bid_volume == 4500.0
        assert snapshot.total_ask_volume == 3600.0
        
        # Test imbalance ratio
        expected_imbalance = (4500.0 - 3600.0) / (4500.0 + 3600.0)
        assert abs(snapshot.imbalance_ratio - expected_imbalance) < 1e-10
        
        # Test cumulative volumes
        assert snapshot.cumulative_bid_volumes == [1000.0, 2500.0, 4500.0]
        assert snapshot.cumulative_ask_volumes == [800.0, 2000.0, 3600.0]

    def test_market_depth_snapshot_validation(self):
        """Test MarketDepthSnapshot validation."""
        
        timestamp = datetime(2023, 12, 15, 11, 0, 0, tzinfo=timezone.utc)
        
        # Test empty bid prices
        with pytest.raises(ValueError, match="Bid prices cannot be empty"):
            models.MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=timestamp,
                bid_prices=[],  # Empty
                bid_volumes=[1000.0],
                ask_prices=[1.1001],
                ask_volumes=[800.0]
            )
        
        # Test empty ask prices
        with pytest.raises(ValueError, match="Ask prices cannot be empty"):
            models.MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=timestamp,
                bid_prices=[1.0998],
                bid_volumes=[1000.0],
                ask_prices=[],  # Empty
                ask_volumes=[800.0]
            )
        
        # Test mismatched bid prices and volumes
        with pytest.raises(ValueError, match="Bid volumes must have the same length as bid prices"):
            models.MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=timestamp,
                bid_prices=[1.0998, 1.0997],
                bid_volumes=[1000.0],  # Different length
                ask_prices=[1.1001],
                ask_volumes=[800.0]
            )
        
        # Test mismatched ask prices and volumes
        with pytest.raises(ValueError, match="Ask volumes must have the same length as ask prices"):
            models.MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=timestamp,
                bid_prices=[1.0998],
                bid_volumes=[1000.0],
                ask_prices=[1.1001, 1.1002],
                ask_volumes=[800.0]  # Different length
            )

    def test_market_depth_snapshot_edge_cases(self):
        """Test MarketDepthSnapshot edge cases."""
        
        timestamp = datetime(2023, 12, 15, 11, 0, 0, tzinfo=timezone.utc)
        
        # Test with zero volumes
        snapshot = models.MarketDepthSnapshot(
            symbol="USDJPY",
            timestamp=timestamp,
            bid_prices=[150.00],
            bid_volumes=[0.0],
            ask_prices=[150.01],
            ask_volumes=[0.0]
        )
        
        assert snapshot.total_bid_volume == 0.0
        assert snapshot.total_ask_volume == 0.0
        assert snapshot.imbalance_ratio is None  # Division by zero case
        
        # Test with single price level
        single_level_snapshot = models.MarketDepthSnapshot(
            symbol="USDCAD",
            timestamp=timestamp,
            bid_prices=[1.3500],
            bid_volumes=[1000.0],
            ask_prices=[1.3502],
            ask_volumes=[800.0]
        )
        
        assert single_level_snapshot.best_bid == 1.3500
        assert single_level_snapshot.best_ask == 1.3502
        assert abs(single_level_snapshot.spread - 0.0002) < 1e-10
        assert single_level_snapshot.cumulative_bid_volumes == [1000.0]
        assert single_level_snapshot.cumulative_ask_volumes == [800.0]

    def test_market_depth_visualization_basic_functionality(self):
        """Test MarketDepthVisualization class basic functionality."""
        
        timestamp = datetime(2023, 12, 15, 12, 0, 0, tzinfo=timezone.utc)
        image_data = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
        settings = {"price_levels": 10, "color_scheme": "default"}
        
        visualization = models.MarketDepthVisualization(
            symbol="AUDCAD",
            timestamp=timestamp,
            visualization_type=models.VisualizationType.DEPTH_CHART,
            image_data=image_data,
            settings=settings
        )
        
        assert visualization.symbol == "AUDCAD"
        assert visualization.timestamp == timestamp
        assert visualization.visualization_type == models.VisualizationType.DEPTH_CHART
        assert visualization.image_data == image_data
        assert visualization.settings == settings

    def test_market_depth_visualization_validation(self):
        """Test MarketDepthVisualization validation."""
        
        timestamp = datetime(2023, 12, 15, 12, 0, 0, tzinfo=timezone.utc)
        settings = {"price_levels": 10}
        
        # Test empty image data
        with pytest.raises(ValueError, match="Image data cannot be empty"):
            models.MarketDepthVisualization(
                symbol="AUDCAD",
                timestamp=timestamp,
                visualization_type=models.VisualizationType.HEATMAP,
                image_data="",  # Empty string
                settings=settings
            )

    def test_market_depth_dashboard_basic_functionality(self):
        """Test MarketDepthDashboard class basic functionality."""
        
        timestamp = datetime(2023, 12, 15, 13, 0, 0, tzinfo=timezone.utc)
        image_data = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
        
        # Create visualizations
        visualizations = {
            models.VisualizationType.DEPTH_CHART: models.MarketDepthVisualization(
                symbol="NZDUSD",
                timestamp=timestamp,
                visualization_type=models.VisualizationType.DEPTH_CHART,
                image_data=image_data,
                settings={"price_levels": 10}
            ),
            models.VisualizationType.HEATMAP: models.MarketDepthVisualization(
                symbol="NZDUSD",
                timestamp=timestamp,
                visualization_type=models.VisualizationType.HEATMAP,
                image_data=image_data,
                settings={"price_levels": 20}
            )
        }
        
        dashboard_settings = models.DashboardSettings()
        
        dashboard = models.MarketDepthDashboard(
            symbol="NZDUSD",
            timestamp=timestamp,
            visualizations=visualizations,
            settings=dashboard_settings
        )
        
        assert dashboard.symbol == "NZDUSD"
        assert dashboard.timestamp == timestamp
        assert len(dashboard.visualizations) == 2
        assert models.VisualizationType.DEPTH_CHART in dashboard.visualizations
        assert models.VisualizationType.HEATMAP in dashboard.visualizations
        assert isinstance(dashboard.settings, models.DashboardSettings)

    def test_market_depth_dashboard_validation(self):
        """Test MarketDepthDashboard validation."""
        
        timestamp = datetime(2023, 12, 15, 13, 0, 0, tzinfo=timezone.utc)
        dashboard_settings = models.DashboardSettings()
        
        # Test empty visualizations
        with pytest.raises(ValueError, match="Visualizations cannot be empty"):
            models.MarketDepthDashboard(
                symbol="NZDUSD",
                timestamp=timestamp,
                visualizations={},  # Empty dict
                settings=dashboard_settings
            )

    def test_dict_methods(self):
        """Test dict() methods for non-Pydantic classes."""
        
        # Test DepthChartSettings dict method
        settings = models.DepthChartSettings(price_levels=15, show_imbalances=False)
        settings_dict = settings.dict()
        assert isinstance(settings_dict, dict)
        assert settings_dict["price_levels"] == 15
        assert settings_dict["show_imbalances"] == False
        
        # Test HeatmapSettings dict method
        heatmap_settings = models.HeatmapSettings(time_window=120, normalization="log")
        heatmap_dict = heatmap_settings.dict()
        assert isinstance(heatmap_dict, dict)
        assert heatmap_dict["time_window"] == 120
        assert heatmap_dict["normalization"] == "log"