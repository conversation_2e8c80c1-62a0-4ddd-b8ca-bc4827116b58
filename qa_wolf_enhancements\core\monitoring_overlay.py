#!/usr/bin/env python3
"""
QA Wolf Enhancement: Real-Time Monitoring Overlay

This module provides enhanced monitoring capabilities for the Forex Trading Bot,
adding real-time dashboards and performance tracking without interfering with trading logic.

SAFETY LEVEL: MAXIMUM - Non-invasive monitoring overlay, zero trading impact
"""

import json
import time
import threading
import psutil
import logging
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, asdict
from collections import deque
import sqlite3

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class PerformanceMetric:
    """Performance metric data structure."""
    timestamp: str
    metric_name: str
    value: float
    unit: str
    category: str

@dataclass
class SystemHealth:
    """System health snapshot."""
    timestamp: str
    cpu_percent: float
    memory_percent: float
    memory_used_mb: float
    disk_usage_percent: float
    network_io_mb: float
    process_count: int
    trading_bot_status: str

class QAWolfMonitoringOverlay:
    """
    Non-invasive monitoring overlay for the Forex Trading Bot.
    
    SAFETY GUARANTEE: This class only monitors and reports,
    never interferes with trading operations or core functionality.
    """
    
    def __init__(self, project_root: str, monitoring_interval: int = 60):
        self.project_root = Path(project_root)
        self.monitoring_interval = monitoring_interval
        self.is_monitoring = False
        self.monitoring_thread = None
        
        # Data storage
        self.metrics_history = deque(maxlen=1000)  # Keep last 1000 metrics
        self.health_history = deque(maxlen=1000)   # Keep last 1000 health snapshots
        
        # Database for persistent storage
        self.db_path = self.project_root / "qa_wolf_enhancements" / "monitoring.db"
        self._init_database()
        
        # Performance thresholds
        self.thresholds = {
            'cpu_warning': 70.0,
            'cpu_critical': 85.0,
            'memory_warning': 75.0,
            'memory_critical': 90.0,
            'disk_warning': 80.0,
            'disk_critical': 95.0
        }
        
        logger.info("QA Wolf Monitoring Overlay initialized - SAFE MODE")
    
    def _init_database(self):
        """Initialize SQLite database for metrics storage."""
        try:
            self.db_path.parent.mkdir(exist_ok=True)
            
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS performance_metrics (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TEXT NOT NULL,
                        metric_name TEXT NOT NULL,
                        value REAL NOT NULL,
                        unit TEXT NOT NULL,
                        category TEXT NOT NULL
                    )
                ''')
                
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS system_health (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TEXT NOT NULL,
                        cpu_percent REAL NOT NULL,
                        memory_percent REAL NOT NULL,
                        memory_used_mb REAL NOT NULL,
                        disk_usage_percent REAL NOT NULL,
                        network_io_mb REAL NOT NULL,
                        process_count INTEGER NOT NULL,
                        trading_bot_status TEXT NOT NULL
                    )
                ''')
                
                conn.commit()
                
            logger.info(f"Monitoring database initialized: {self.db_path}")
            
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
    
    def start_monitoring(self):
        """Start the monitoring overlay in a separate thread."""
        if self.is_monitoring:
            logger.warning("Monitoring already active")
            return
        
        self.is_monitoring = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        
        logger.info("🚀 QA Wolf Monitoring Overlay started")
        logger.info("🛡️ SAFETY: Non-invasive monitoring, zero trading impact")
    
    def stop_monitoring(self):
        """Stop the monitoring overlay."""
        self.is_monitoring = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        
        logger.info("⏹️ QA Wolf Monitoring Overlay stopped")
    
    def _monitoring_loop(self):
        """Main monitoring loop - runs in separate thread."""
        logger.info(f"Monitoring loop started (interval: {self.monitoring_interval}s)")
        
        while self.is_monitoring:
            try:
                # Collect system health metrics
                health = self._collect_system_health()
                self.health_history.append(health)
                self._store_health_metric(health)
                
                # Collect performance metrics
                metrics = self._collect_performance_metrics()
                for metric in metrics:
                    self.metrics_history.append(metric)
                    self._store_performance_metric(metric)
                
                # Check for alerts
                alerts = self._check_alerts(health, metrics)
                if alerts:
                    self._handle_alerts(alerts)
                
                # Generate status report
                self._generate_status_report(health, metrics)
                
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
            
            # Sleep until next monitoring cycle
            time.sleep(self.monitoring_interval)
    
    def _collect_system_health(self) -> SystemHealth:
        """Collect current system health metrics."""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Memory usage
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_used_mb = memory.used / (1024 * 1024)
            
            # Disk usage
            disk = psutil.disk_usage('/')
            disk_usage_percent = (disk.used / disk.total) * 100
            
            # Network I/O
            network = psutil.net_io_counters()
            network_io_mb = (network.bytes_sent + network.bytes_recv) / (1024 * 1024)
            
            # Process count
            process_count = len(psutil.pids())
            
            # Trading bot status (check if process exists)
            trading_bot_status = self._check_trading_bot_status()
            
            return SystemHealth(
                timestamp=datetime.now(timezone.utc).isoformat(),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_used_mb=memory_used_mb,
                disk_usage_percent=disk_usage_percent,
                network_io_mb=network_io_mb,
                process_count=process_count,
                trading_bot_status=trading_bot_status
            )
            
        except Exception as e:
            logger.error(f"Error collecting system health: {e}")
            return SystemHealth(
                timestamp=datetime.now(timezone.utc).isoformat(),
                cpu_percent=0.0,
                memory_percent=0.0,
                memory_used_mb=0.0,
                disk_usage_percent=0.0,
                network_io_mb=0.0,
                process_count=0,
                trading_bot_status="UNKNOWN"
            )
    
    def _collect_performance_metrics(self) -> List[PerformanceMetric]:
        """Collect performance metrics."""
        metrics = []
        timestamp = datetime.now(timezone.utc).isoformat()
        
        try:
            # System performance metrics
            metrics.extend([
                PerformanceMetric(timestamp, "cpu_usage", psutil.cpu_percent(), "%", "system"),
                PerformanceMetric(timestamp, "memory_usage", psutil.virtual_memory().percent, "%", "system"),
                PerformanceMetric(timestamp, "disk_io_read", psutil.disk_io_counters().read_bytes / (1024*1024), "MB", "system"),
                PerformanceMetric(timestamp, "disk_io_write", psutil.disk_io_counters().write_bytes / (1024*1024), "MB", "system"),
            ])
            
            # Trading bot specific metrics (if log files exist)
            trading_metrics = self._collect_trading_metrics()
            metrics.extend(trading_metrics)
            
        except Exception as e:
            logger.error(f"Error collecting performance metrics: {e}")
        
        return metrics
    
    def _collect_trading_metrics(self) -> List[PerformanceMetric]:
        """Collect trading-specific metrics from log files (non-invasive)."""
        metrics = []
        timestamp = datetime.now(timezone.utc).isoformat()
        
        try:
            # Check for trading bot log file
            log_file = self.project_root / "trading_bot.log"
            if log_file.exists():
                # Get file size as a metric
                file_size_mb = log_file.stat().st_size / (1024 * 1024)
                metrics.append(PerformanceMetric(
                    timestamp, "log_file_size", file_size_mb, "MB", "trading"
                ))
                
                # Get last modified time to check if bot is active
                last_modified = datetime.fromtimestamp(log_file.stat().st_mtime, tz=timezone.utc)
                minutes_since_update = (datetime.now(timezone.utc) - last_modified).total_seconds() / 60
                metrics.append(PerformanceMetric(
                    timestamp, "log_last_update_minutes", minutes_since_update, "minutes", "trading"
                ))
            
            # Check for performance log file
            perf_log_file = self.project_root / "trading_performance.log"
            if perf_log_file.exists():
                file_size_mb = perf_log_file.stat().st_size / (1024 * 1024)
                metrics.append(PerformanceMetric(
                    timestamp, "performance_log_size", file_size_mb, "MB", "trading"
                ))
        
        except Exception as e:
            logger.error(f"Error collecting trading metrics: {e}")
        
        return metrics
    
    def _check_trading_bot_status(self) -> str:
        """Check if trading bot is running (non-invasive check)."""
        try:
            # Check for recent log activity
            log_file = self.project_root / "trading_bot.log"
            if log_file.exists():
                last_modified = datetime.fromtimestamp(log_file.stat().st_mtime, tz=timezone.utc)
                minutes_since_update = (datetime.now(timezone.utc) - last_modified).total_seconds() / 60
                
                if minutes_since_update < 5:
                    return "ACTIVE"
                elif minutes_since_update < 30:
                    return "IDLE"
                else:
                    return "INACTIVE"
            else:
                return "NO_LOGS"
                
        except Exception as e:
            logger.error(f"Error checking trading bot status: {e}")
            return "UNKNOWN"
    
    def _check_alerts(self, health: SystemHealth, metrics: List[PerformanceMetric]) -> List[Dict[str, Any]]:
        """Check for alert conditions."""
        alerts = []
        
        # CPU alerts
        if health.cpu_percent > self.thresholds['cpu_critical']:
            alerts.append({
                'level': 'CRITICAL',
                'category': 'system',
                'message': f"CPU usage critical: {health.cpu_percent:.1f}%",
                'value': health.cpu_percent,
                'threshold': self.thresholds['cpu_critical']
            })
        elif health.cpu_percent > self.thresholds['cpu_warning']:
            alerts.append({
                'level': 'WARNING',
                'category': 'system',
                'message': f"CPU usage high: {health.cpu_percent:.1f}%",
                'value': health.cpu_percent,
                'threshold': self.thresholds['cpu_warning']
            })
        
        # Memory alerts
        if health.memory_percent > self.thresholds['memory_critical']:
            alerts.append({
                'level': 'CRITICAL',
                'category': 'system',
                'message': f"Memory usage critical: {health.memory_percent:.1f}%",
                'value': health.memory_percent,
                'threshold': self.thresholds['memory_critical']
            })
        elif health.memory_percent > self.thresholds['memory_warning']:
            alerts.append({
                'level': 'WARNING',
                'category': 'system',
                'message': f"Memory usage high: {health.memory_percent:.1f}%",
                'value': health.memory_percent,
                'threshold': self.thresholds['memory_warning']
            })
        
        # Trading bot status alerts
        if health.trading_bot_status == "INACTIVE":
            alerts.append({
                'level': 'WARNING',
                'category': 'trading',
                'message': "Trading bot appears inactive (no recent log activity)",
                'value': health.trading_bot_status,
                'threshold': 'ACTIVE'
            })
        
        return alerts
    
    def _handle_alerts(self, alerts: List[Dict[str, Any]]):
        """Handle alert notifications."""
        for alert in alerts:
            if alert['level'] == 'CRITICAL':
                logger.critical(f"🚨 {alert['message']}")
            elif alert['level'] == 'WARNING':
                logger.warning(f"⚠️ {alert['message']}")
            
            # Save alert to database
            self._store_alert(alert)
    
    def _store_health_metric(self, health: SystemHealth):
        """Store health metric in database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    INSERT INTO system_health 
                    (timestamp, cpu_percent, memory_percent, memory_used_mb, 
                     disk_usage_percent, network_io_mb, process_count, trading_bot_status)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    health.timestamp, health.cpu_percent, health.memory_percent,
                    health.memory_used_mb, health.disk_usage_percent, health.network_io_mb,
                    health.process_count, health.trading_bot_status
                ))
                conn.commit()
        except Exception as e:
            logger.error(f"Error storing health metric: {e}")
    
    def _store_performance_metric(self, metric: PerformanceMetric):
        """Store performance metric in database."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.execute('''
                    INSERT INTO performance_metrics 
                    (timestamp, metric_name, value, unit, category)
                    VALUES (?, ?, ?, ?, ?)
                ''', (metric.timestamp, metric.metric_name, metric.value, metric.unit, metric.category))
                conn.commit()
        except Exception as e:
            logger.error(f"Error storing performance metric: {e}")
    
    def _store_alert(self, alert: Dict[str, Any]):
        """Store alert in database."""
        # For now, just log alerts. Could extend to store in separate alerts table
        logger.info(f"Alert stored: {alert}")
    
    def _generate_status_report(self, health: SystemHealth, metrics: List[PerformanceMetric]):
        """Generate periodic status report."""
        try:
            report = {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'monitoring_status': 'ACTIVE',
                'system_health': asdict(health),
                'performance_summary': {
                    'metrics_collected': len(metrics),
                    'trading_bot_status': health.trading_bot_status,
                    'system_load': {
                        'cpu': f"{health.cpu_percent:.1f}%",
                        'memory': f"{health.memory_percent:.1f}%",
                        'disk': f"{health.disk_usage_percent:.1f}%"
                    }
                },
                'safety_status': {
                    'trading_interference': False,
                    'monitoring_overhead': 'MINIMAL',
                    'data_collection_only': True
                }
            }
            
            # Save report to file
            report_file = self.project_root / "qa_wolf_enhancements" / "latest_status_report.json"
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
            
        except Exception as e:
            logger.error(f"Error generating status report: {e}")
    
    def get_current_status(self) -> Dict[str, Any]:
        """Get current monitoring status."""
        return {
            'monitoring_active': self.is_monitoring,
            'metrics_count': len(self.metrics_history),
            'health_snapshots': len(self.health_history),
            'database_path': str(self.db_path),
            'monitoring_interval': self.monitoring_interval
        }


def main():
    """Main execution function for testing."""
    
    # Get project root
    project_root = Path(__file__).parent.parent
    
    logger.info("🚀 QA Wolf Monitoring Overlay Test")
    logger.info("🛡️ SAFETY: Non-invasive monitoring only")
    
    # Initialize monitoring
    monitor = QAWolfMonitoringOverlay(str(project_root), monitoring_interval=10)
    
    try:
        # Start monitoring
        monitor.start_monitoring()
        
        # Run for 60 seconds as a test
        logger.info("Running monitoring test for 60 seconds...")
        time.sleep(60)
        
        # Display status
        status = monitor.get_current_status()
        logger.info(f"Monitoring status: {status}")
        
    except KeyboardInterrupt:
        logger.info("Monitoring test interrupted by user")
    finally:
        # Stop monitoring
        monitor.stop_monitoring()
        logger.info("✅ Monitoring test completed")


if __name__ == "__main__":
    main()