"""
Phase 5K comprehensive tests to push multilingual_news/models.py to 90%+ coverage.
"""

import pytest
from datetime import datetime, date, timezone, timedelta
from src.forex_bot.multilingual_news import models

class TestMultilingualNewsModelsPhase5K:
    """Phase 5K tests to achieve 90%+ coverage for multilingual_news/models.py."""

    def test_news_source_enum(self):
        """Test NewsSource enum values."""
        assert models.NewsSource.BLOOMBERG.value == "Bloomberg"
        assert models.NewsSource.REUTERS.value == "Reuters"
        assert models.NewsSource.FINANCIAL_TIMES.value == "Financial Times"
        assert models.NewsSource.WALL_STREET_JOURNAL.value == "Wall Street Journal"
        assert models.NewsSource.CNBC.value == "CNBC"
        assert models.NewsSource.FOREX_FACTORY.value == "Forex Factory"
        assert models.NewsSource.INVESTING_COM.value == "Investing.com"
        assert models.NewsSource.CENTRAL_BANK.value == "Central Bank"
        assert models.NewsSource.OTHER.value == "Other"

    def test_news_category_enum(self):
        """Test NewsCategory enum values."""
        assert models.NewsCategory.ECONOMIC.value == "Economic"
        assert models.NewsCategory.MONETARY_POLICY.value == "Monetary Policy"
        assert models.NewsCategory.FISCAL_POLICY.value == "Fiscal Policy"
        assert models.NewsCategory.GEOPOLITICAL.value == "Geopolitical"
        assert models.NewsCategory.MARKET.value == "Market"
        assert models.NewsCategory.CORPORATE.value == "Corporate"
        assert models.NewsCategory.COMMODITY.value == "Commodity"
        assert models.NewsCategory.FOREX.value == "Forex"
        assert models.NewsCategory.OTHER.value == "Other"

    def test_news_impact_enum(self):
        """Test NewsImpact enum values."""
        assert models.NewsImpact.HIGH.value == "High"
        assert models.NewsImpact.MEDIUM.value == "Medium"
        assert models.NewsImpact.LOW.value == "Low"
        assert models.NewsImpact.UNKNOWN.value == "Unknown"

    def test_sentiment_label_enum(self):
        """Test SentimentLabel enum values."""
        assert models.SentimentLabel.VERY_POSITIVE.value == "Very Positive"
        assert models.SentimentLabel.POSITIVE.value == "Positive"
        assert models.SentimentLabel.NEUTRAL.value == "Neutral"
        assert models.SentimentLabel.NEGATIVE.value == "Negative"
        assert models.SentimentLabel.VERY_NEGATIVE.value == "Very Negative"

    def test_news_article_basic_functionality(self):
        """Test NewsArticle class basic functionality."""
        
        # Create test article
        published_time = datetime(2023, 12, 15, 10, 0, 0, tzinfo=timezone.utc)
        article = models.NewsArticle(
            title="EUR/USD Rises on ECB Policy",
            content="The European Central Bank announced new policy measures...",
            url="https://example.com/news/1",
            source=models.NewsSource.BLOOMBERG,
            published_at=published_time,
            language="en",
            category=models.NewsCategory.MONETARY_POLICY,
            impact=models.NewsImpact.HIGH,
            currencies=["EUR", "USD"],
            is_translated=False,
            tags=["ECB", "monetary policy"],
            author="John Doe"
        )
        
        # Test basic attributes
        assert article.title == "EUR/USD Rises on ECB Policy"
        assert article.content == "The European Central Bank announced new policy measures..."
        assert article.url == "https://example.com/news/1"
        assert article.source == models.NewsSource.BLOOMBERG
        assert article.published_at == published_time
        assert article.language == "en"
        assert article.category == models.NewsCategory.MONETARY_POLICY
        assert article.impact == models.NewsImpact.HIGH
        assert article.currencies == ["EUR", "USD"]
        assert article.is_translated == False
        assert article.tags == ["ECB", "monetary policy"]
        assert article.author == "John Doe"

    def test_news_article_age_properties(self):
        """Test NewsArticle age-related properties."""
        
        # Test recent article (1 hour old)
        recent_time = datetime.now(timezone.utc) - timedelta(hours=1)
        recent_article = models.NewsArticle(
            title="Recent News",
            content="Recent content",
            url="https://example.com/recent",
            source=models.NewsSource.REUTERS,
            published_at=recent_time,
            language="en"
        )
        
        assert recent_article.age_hours >= 0.9  # Allow some tolerance
        assert recent_article.age_hours <= 1.1
        assert recent_article.is_recent == True
        assert recent_article.is_breaking == False
        
        # Test breaking news (30 minutes old)
        breaking_time = datetime.now(timezone.utc) - timedelta(minutes=30)
        breaking_article = models.NewsArticle(
            title="Breaking News",
            content="Breaking content",
            url="https://example.com/breaking",
            source=models.NewsSource.CNBC,
            published_at=breaking_time,
            language="en"
        )
        
        assert breaking_article.age_hours >= 0.4  # Allow some tolerance
        assert breaking_article.age_hours <= 0.6
        assert breaking_article.is_recent == True
        assert breaking_article.is_breaking == True
        
        # Test old article (2 days old)
        old_time = datetime.now(timezone.utc) - timedelta(days=2)
        old_article = models.NewsArticle(
            title="Old News",
            content="Old content",
            url="https://example.com/old",
            source=models.NewsSource.OTHER,
            published_at=old_time,
            language="en"
        )
        
        assert old_article.age_hours >= 47  # Allow some tolerance
        assert old_article.age_hours <= 49
        assert old_article.is_recent == False
        assert old_article.is_breaking == False

    def test_news_article_timezone_handling(self):
        """Test NewsArticle timezone handling."""
        
        # Test with naive datetime (no timezone)
        naive_time = datetime(2023, 12, 15, 10, 0, 0)
        naive_article = models.NewsArticle(
            title="Naive Time Article",
            content="Content",
            url="https://example.com/naive",
            source=models.NewsSource.FOREX_FACTORY,
            published_at=naive_time,
            language="en"
        )
        
        # Should handle naive datetime by assuming UTC
        assert naive_article.age_hours >= 0

    def test_sentiment_analysis_basic_functionality(self):
        """Test SentimentAnalysis class basic functionality."""
        
        # Create test sentiment analysis
        sentiment = models.SentimentAnalysis(
            sentiment_label=models.SentimentLabel.POSITIVE,
            sentiment_value=0.75,
            confidence=0.85,
            language="en",
            model_name="bert-base-multilingual",
            text="This is positive news",
            probabilities={
                models.SentimentLabel.VERY_POSITIVE: 0.1,
                models.SentimentLabel.POSITIVE: 0.7,
                models.SentimentLabel.NEUTRAL: 0.15,
                models.SentimentLabel.NEGATIVE: 0.04,
                models.SentimentLabel.VERY_NEGATIVE: 0.01
            }
        )
        
        # Test basic attributes
        assert sentiment.sentiment_label == models.SentimentLabel.POSITIVE
        assert sentiment.sentiment_value == 0.75
        assert sentiment.confidence == 0.85
        assert sentiment.language == "en"
        assert sentiment.model_name == "bert-base-multilingual"
        assert sentiment.text == "This is positive news"
        assert sentiment.error is None
        
        # Test probabilities
        assert sentiment.probabilities[models.SentimentLabel.POSITIVE] == 0.7
        assert sentiment.probabilities[models.SentimentLabel.NEUTRAL] == 0.15

    def test_sentiment_analysis_properties(self):
        """Test SentimentAnalysis sentiment properties."""
        
        # Test positive sentiment
        positive_sentiment = models.SentimentAnalysis(
            sentiment_label=models.SentimentLabel.VERY_POSITIVE,
            sentiment_value=0.9,
            confidence=0.95,
            language="en",
            model_name="test-model"
        )
        
        assert positive_sentiment.is_positive == True
        assert positive_sentiment.is_negative == False
        assert positive_sentiment.is_neutral == False
        
        # Test negative sentiment
        negative_sentiment = models.SentimentAnalysis(
            sentiment_label=models.SentimentLabel.NEGATIVE,
            sentiment_value=-0.6,
            confidence=0.8,
            language="en",
            model_name="test-model"
        )
        
        assert negative_sentiment.is_positive == False
        assert negative_sentiment.is_negative == True
        assert negative_sentiment.is_neutral == False
        
        # Test neutral sentiment
        neutral_sentiment = models.SentimentAnalysis(
            sentiment_label=models.SentimentLabel.NEUTRAL,
            sentiment_value=0.0,
            confidence=0.7,
            language="en",
            model_name="test-model"
        )
        
        assert neutral_sentiment.is_positive == False
        assert neutral_sentiment.is_negative == False
        assert neutral_sentiment.is_neutral == True

    def test_entity_basic_functionality(self):
        """Test Entity class basic functionality."""
        
        # Create test entity
        entity = models.Entity(
            text="European Central Bank",
            type="ORGANIZATION",
            start_char=10,
            end_char=30,
            confidence=0.95,
            model_name="spacy-en",
            language="en",
            normalized_text="ECB",
            value=None,
            currency=None,
            date_value=None
        )
        
        # Test basic attributes
        assert entity.text == "European Central Bank"
        assert entity.type == "ORGANIZATION"
        assert entity.start_char == 10
        assert entity.end_char == 30
        assert entity.confidence == 0.95
        assert entity.model_name == "spacy-en"
        assert entity.language == "en"
        assert entity.normalized_text == "ECB"
        assert entity.value is None
        assert entity.currency is None
        assert entity.date_value is None

    def test_entity_financial_data(self):
        """Test Entity with financial data."""
        
        # Create financial entity
        financial_entity = models.Entity(
            text="$1.5 billion",
            type="MONEY",
            start_char=0,
            end_char=11,
            confidence=0.9,
            model_name="spacy-en",
            language="en",
            value=**********.0,
            currency="USD"
        )
        
        assert financial_entity.text == "$1.5 billion"
        assert financial_entity.type == "MONEY"
        assert financial_entity.value == **********.0
        assert financial_entity.currency == "USD"

    def test_entity_date_data(self):
        """Test Entity with date data."""
        
        # Create date entity
        date_entity = models.Entity(
            text="December 15, 2023",
            type="DATE",
            start_char=5,
            end_char=22,
            confidence=0.85,
            model_name="spacy-en",
            language="en",
            date_value=date(2023, 12, 15)
        )
        
        assert date_entity.text == "December 15, 2023"
        assert date_entity.type == "DATE"
        assert date_entity.date_value == date(2023, 12, 15)

    def test_news_summary_basic_functionality(self):
        """Test NewsSummary class basic functionality."""
        
        # Create test articles
        article1 = models.NewsArticle(
            title="Article 1",
            content="Content 1",
            url="https://example.com/1",
            source=models.NewsSource.BLOOMBERG,
            published_at=datetime(2023, 12, 15, 9, 0, 0, tzinfo=timezone.utc),
            language="en"
        )
        
        article2 = models.NewsArticle(
            title="Article 2",
            content="Content 2",
            url="https://example.com/2",
            source=models.NewsSource.REUTERS,
            published_at=datetime(2023, 12, 15, 11, 0, 0, tzinfo=timezone.utc),
            language="en"
        )
        
        # Create test sentiment
        sentiment = models.SentimentAnalysis(
            sentiment_label=models.SentimentLabel.POSITIVE,
            sentiment_value=0.6,
            confidence=0.8,
            language="en",
            model_name="test-model"
        )
        
        # Create test entity
        entity = models.Entity(
            text="Federal Reserve",
            type="ORGANIZATION",
            start_char=0,
            end_char=15,
            confidence=0.9,
            model_name="spacy-en",
            language="en"
        )
        
        # Create test summary
        summary = models.NewsSummary(
            text="Summary of recent news articles",
            articles=[article1, article2],
            language="en",
            model_name="gpt-3.5-turbo",
            sentiment=sentiment,
            entities=[entity],
            topics=["monetary policy", "inflation"],
            currencies=["USD", "EUR"]
        )
        
        # Test basic attributes
        assert summary.text == "Summary of recent news articles"
        assert len(summary.articles) == 2
        assert summary.language == "en"
        assert summary.model_name == "gpt-3.5-turbo"
        assert summary.sentiment == sentiment
        assert len(summary.entities) == 1
        assert summary.topics == ["monetary policy", "inflation"]
        assert summary.currencies == ["USD", "EUR"]

    def test_news_summary_properties(self):
        """Test NewsSummary calculated properties."""
        
        # Create test articles with different sources and times
        article1 = models.NewsArticle(
            title="Article 1",
            content="Content 1",
            url="https://example.com/1",
            source=models.NewsSource.BLOOMBERG,
            published_at=datetime(2023, 12, 15, 9, 0, 0, tzinfo=timezone.utc),
            language="en"
        )
        
        article2 = models.NewsArticle(
            title="Article 2",
            content="Content 2",
            url="https://example.com/2",
            source=models.NewsSource.REUTERS,
            published_at=datetime(2023, 12, 15, 11, 0, 0, tzinfo=timezone.utc),
            language="en"
        )
        
        article3 = models.NewsArticle(
            title="Article 3",
            content="Content 3",
            url="https://example.com/3",
            source=models.NewsSource.BLOOMBERG,
            published_at=datetime(2023, 12, 15, 10, 0, 0, tzinfo=timezone.utc),
            language="en"
        )
        
        summary = models.NewsSummary(
            text="Test summary",
            articles=[article1, article2, article3],
            language="en"
        )
        
        # Test article_count property
        assert summary.article_count == 3
        
        # Test sources property
        expected_sources = {models.NewsSource.BLOOMBERG, models.NewsSource.REUTERS}
        assert summary.sources == expected_sources
        
        # Test time_range property
        earliest, latest = summary.time_range
        assert earliest == datetime(2023, 12, 15, 9, 0, 0, tzinfo=timezone.utc)
        assert latest == datetime(2023, 12, 15, 11, 0, 0, tzinfo=timezone.utc)

    def test_news_summary_empty_articles(self):
        """Test NewsSummary with empty articles list."""
        
        summary = models.NewsSummary(
            text="Empty summary",
            articles=[],
            language="en"
        )
        
        # Test with empty articles
        assert summary.article_count == 0
        assert summary.sources == set()
        
        # Test time_range with empty articles
        earliest, latest = summary.time_range
        # Should return current time for both when no articles
        assert isinstance(earliest, datetime)
        assert isinstance(latest, datetime)

    def test_news_context_basic_functionality(self):
        """Test NewsContext class basic functionality."""
        
        # Create test data
        article = models.NewsArticle(
            title="High Impact News",
            content="Important economic data",
            url="https://example.com/high-impact",
            source=models.NewsSource.CENTRAL_BANK,
            published_at=datetime.now(timezone.utc) - timedelta(minutes=30),
            language="en",
            impact=models.NewsImpact.HIGH
        )
        
        sentiment = models.SentimentAnalysis(
            sentiment_label=models.SentimentLabel.POSITIVE,
            sentiment_value=0.7,
            confidence=0.85,
            language="en",
            model_name="test-model"
        )
        
        entity = models.Entity(
            text="Federal Reserve",
            type="ORGANIZATION",
            start_char=0,
            end_char=15,
            confidence=0.9,
            model_name="spacy-en",
            language="en"
        )
        
        # Create test context
        context = models.NewsContext(
            currency_or_pair="EURUSD",
            recent_sentiment=sentiment,
            daily_sentiment=sentiment,
            weekly_sentiment=sentiment,
            key_entities=[entity],
            high_impact_news=[article],
            bullish_signals=["ECB hawkish stance", "Strong GDP growth"],
            bearish_signals=["Inflation concerns"]
        )
        
        # Test basic attributes
        assert context.currency_or_pair == "EURUSD"
        assert context.recent_sentiment == sentiment
        assert context.daily_sentiment == sentiment
        assert context.weekly_sentiment == sentiment
        assert len(context.key_entities) == 1
        assert len(context.high_impact_news) == 1
        assert len(context.bullish_signals) == 2
        assert len(context.bearish_signals) == 1

    def test_news_context_overall_sentiment(self):
        """Test NewsContext overall_sentiment property."""
        
        # Test with all sentiment types
        recent_sentiment = models.SentimentAnalysis(
            sentiment_label=models.SentimentLabel.VERY_POSITIVE,
            sentiment_value=0.8,
            confidence=0.9,
            language="en",
            model_name="test-model"
        )
        
        daily_sentiment = models.SentimentAnalysis(
            sentiment_label=models.SentimentLabel.POSITIVE,
            sentiment_value=0.6,
            confidence=0.8,
            language="en",
            model_name="test-model"
        )
        
        weekly_sentiment = models.SentimentAnalysis(
            sentiment_label=models.SentimentLabel.NEUTRAL,
            sentiment_value=0.1,
            confidence=0.7,
            language="en",
            model_name="test-model"
        )
        
        context = models.NewsContext(
            currency_or_pair="GBPUSD",
            recent_sentiment=recent_sentiment,
            daily_sentiment=daily_sentiment,
            weekly_sentiment=weekly_sentiment
        )
        
        # Test weighted average calculation
        # (0.8 * 3 + 0.6 * 2 + 0.1 * 1) / (3 + 2 + 1) = 3.7 / 6 = 0.617
        overall = context.overall_sentiment
        assert overall == models.SentimentLabel.POSITIVE  # 0.617 is between 0.2 and 0.7    def test_news_context_sentiment_edge_cases(self):
        """Test NewsContext overall_sentiment with edge cases."""
        
        # Test with no sentiment data
        context_no_sentiment = models.NewsContext(currency_or_pair="USDJPY")
        assert context_no_sentiment.overall_sentiment is None
        
        # Test with very negative sentiment
        very_negative_sentiment = models.SentimentAnalysis(
            sentiment_label=models.SentimentLabel.VERY_NEGATIVE,
            sentiment_value=-0.9,
            confidence=0.95,
            language="en",
            model_name="test-model"
        )
        
        context_very_negative = models.NewsContext(
            currency_or_pair="AUDUSD",
            recent_sentiment=very_negative_sentiment
        )
        
        assert context_very_negative.overall_sentiment == models.SentimentLabel.VERY_NEGATIVE
        
        # Test with very positive sentiment
        very_positive_sentiment = models.SentimentAnalysis(
            sentiment_label=models.SentimentLabel.VERY_POSITIVE,
            sentiment_value=0.85,
            confidence=0.95,
            language="en",
            model_name="test-model"
        )
        
        context_very_positive = models.NewsContext(
            currency_or_pair="NZDUSD",
            recent_sentiment=very_positive_sentiment
        )
        
        assert context_very_positive.overall_sentiment == models.SentimentLabel.VERY_POSITIVE

    def test_news_context_breaking_news(self):
        """Test NewsContext has_breaking_news property."""
        
        # Test with breaking news
        breaking_article = models.NewsArticle(
            title="Breaking News",
            content="Breaking content",
            url="https://example.com/breaking",
            source=models.NewsSource.CNBC,
            published_at=datetime.now(timezone.utc) - timedelta(minutes=30),
            language="en",
            impact=models.NewsImpact.HIGH
        )
        
        context_with_breaking = models.NewsContext(
            currency_or_pair="USDCAD",
            high_impact_news=[breaking_article]
        )
        
        assert context_with_breaking.has_breaking_news == True
        
        # Test without breaking news
        old_article = models.NewsArticle(
            title="Old News",
            content="Old content",
            url="https://example.com/old",
            source=models.NewsSource.OTHER,
            published_at=datetime.now(timezone.utc) - timedelta(hours=2),
            language="en",
            impact=models.NewsImpact.HIGH
        )
        
        context_without_breaking = models.NewsContext(
            currency_or_pair="USDCHF",
            high_impact_news=[old_article]
        )
        
        assert context_without_breaking.has_breaking_news == False

    def test_news_context_trading_bias(self):
        """Test NewsContext trading_bias property."""
        
        # Test bullish bias
        context_bullish = models.NewsContext(
            currency_or_pair="EURJPY",
            bullish_signals=["Strong GDP", "Hawkish central bank", "Rising yields"],
            bearish_signals=["Inflation concerns"]
        )
        
        assert context_bullish.trading_bias == "BUY"
        
        # Test bearish bias
        context_bearish = models.NewsContext(
            currency_or_pair="GBPJPY",
            bullish_signals=["Economic recovery"],
            bearish_signals=["Political uncertainty", "Trade deficit", "Weak employment"]
        )
        
        assert context_bearish.trading_bias == "SELL"
        
        # Test neutral bias
        context_neutral = models.NewsContext(
            currency_or_pair="AUDJPY",
            bullish_signals=["Commodity prices", "Employment growth"],
            bearish_signals=["Housing concerns", "Consumer sentiment"]
        )
        
        assert context_neutral.trading_bias == "NEUTRAL"
        
        # Test no signals
        context_no_signals = models.NewsContext(currency_or_pair="NZDJPY")
        
        assert context_no_signals.trading_bias is None