"""
Phase 5BB: Comprehensive test for market_depth_visualizer/models.py to push from 61% to 90%+ coverage.
Targeting the remaining missing lines: validation methods, properties, and edge cases.
"""

import pytest
from datetime import datetime, timezone
from unittest.mock import patch
from src.forex_bot.market_depth_visualizer.models import (
    VisualizationType, ColorScheme, DepthChartSettings, HeatmapSettings,
    TimeAndSalesSettings, LiquidityMapSettings, OrderFlowFootprintSettings,
    DashboardSettings, VisualizationSettings, TradeEntry, MarketDepthSnapshot,
    MarketDepthVisualization, MarketDepthDashboard
)


class TestMarketDepthPhase5BBComprehensive:
    """Comprehensive test to push market_depth_visualizer/models.py to 90%+ coverage"""

    def test_import_error_handling_comprehensive(self):
        """Test comprehensive import error handling for missing dependencies"""
        
        # Test numpy import error (lines 14-15)
        with patch.dict('sys.modules', {'numpy': None}):
            # Force module reload to trigger import error
            import src.forex_bot.market_depth_visualizer.models as models_module
            import importlib
            importlib.reload(models_module)
            
            # Verify NUMPY_AVAILABLE is False
            assert not models_module.NUMPY_AVAILABLE

        # Test pandas import error (lines 20-29)
        with patch.dict('sys.modules', {'pandas': None}):
            import src.forex_bot.market_depth_visualizer.models as models_module
            import importlib
            importlib.reload(models_module)
            
            # Verify PANDAS_AVAILABLE is False
            assert not models_module.PANDAS_AVAILABLE

    def test_market_depth_snapshot_validation_comprehensive(self):
        """Test comprehensive validation scenarios for MarketDepthSnapshot"""
        
        now = datetime.now(timezone.utc)
        
        # Test bid volumes length validation (line 165)
        with pytest.raises(ValueError, match="Bid volumes must have the same length as bid prices"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340, 1.2339, 1.2338],
                bid_volumes=[1000.0, 1500.0],  # Length mismatch
                ask_prices=[1.2341],
                ask_volumes=[800.0]
            )
        
        # Test ask volumes length validation (line 177)
        with pytest.raises(ValueError, match="Ask volumes must have the same length as ask prices"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340],
                bid_volumes=[1000.0],
                ask_prices=[1.2341, 1.2342, 1.2343],
                ask_volumes=[800.0, 1200.0]  # Length mismatch
            )    def test_market_depth_snapshot_properties_comprehensive(self):
        """Test comprehensive property calculations for MarketDepthSnapshot"""
        
        now = datetime.now(timezone.utc)
        
        # Test all properties with valid data
        snapshot = MarketDepthSnapshot(
            symbol="GBPUSD",
            timestamp=now,
            bid_prices=[1.3000, 1.2999, 1.2998],
            bid_volumes=[1000.0, 1500.0, 2000.0],
            ask_prices=[1.3001, 1.3002, 1.3003],
            ask_volumes=[800.0, 1200.0, 1600.0]
        )
        
        # Test best_bid property (line 193-195)
        assert snapshot.best_bid == 1.3000  # max of bid_prices
        
        # Test best_ask property (line 200-202)
        assert snapshot.best_ask == 1.3001  # min of ask_prices
        
        # Test spread property
        assert snapshot.spread == 0.0001  # best_ask - best_bid
        
        # Test mid_price property
        assert snapshot.mid_price == 1.30005  # (best_bid + best_ask) / 2
        
        # Test total volumes
        assert snapshot.total_bid_volume == 4500.0  # sum of bid_volumes
        assert snapshot.total_ask_volume == 3600.0  # sum of ask_volumes
        
        # Test imbalance_ratio property (lines 225)
        expected_imbalance = (4500.0 - 3600.0) / (4500.0 + 3600.0)
        assert abs(snapshot.imbalance_ratio - expected_imbalance) < 1e-10
        
        # Test cumulative volumes with numpy available (lines 230-238)
        expected_cum_bid = [1000.0, 2500.0, 4500.0]
        expected_cum_ask = [800.0, 2000.0, 3600.0]
        assert snapshot.cumulative_bid_volumes == expected_cum_bid
        assert snapshot.cumulative_ask_volumes == expected_cum_ask

    def test_market_depth_snapshot_edge_cases_comprehensive(self):
        """Test edge cases for MarketDepthSnapshot properties"""
        
        now = datetime.now(timezone.utc)
        
        # Test empty prices edge case
        with pytest.raises(ValueError, match="Bid prices cannot be empty"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[],  # Empty bid prices (line 159)
                bid_volumes=[],
                ask_prices=[1.2341],
                ask_volumes=[800.0]
            )
        
        with pytest.raises(ValueError, match="Ask prices cannot be empty"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340],
                bid_volumes=[1000.0],
                ask_prices=[],  # Empty ask prices (line 171)
                ask_volumes=[]
            )
        
        # Test zero volume imbalance ratio (returns None)
        zero_volume_snapshot = MarketDepthSnapshot(
            symbol="EURUSD",
            timestamp=now,
            bid_prices=[1.2340],
            bid_volumes=[0.0],
            ask_prices=[1.2341],
            ask_volumes=[0.0]
        )
        assert zero_volume_snapshot.imbalance_ratio is None  # total_volume == 0
        
        # Test cumulative volumes without numpy (lines 243-251)
        with patch('src.forex_bot.market_depth_visualizer.models.NUMPY_AVAILABLE', False):
            manual_snapshot = MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340, 1.2339, 1.2338],
                bid_volumes=[100.0, 200.0, 300.0],
                ask_prices=[1.2341, 1.2342, 1.2343],
                ask_volumes=[150.0, 250.0, 350.0]
            )
            
            # Test manual cumulative calculation
            expected_cum_bid = [100.0, 300.0, 600.0]
            expected_cum_ask = [150.0, 400.0, 750.0]
            assert manual_snapshot.cumulative_bid_volumes == expected_cum_bid
            assert manual_snapshot.cumulative_ask_volumes == expected_cum_ask    def test_visualization_settings_comprehensive(self):
        """Test comprehensive VisualizationSettings functionality"""
        
        # Test default VisualizationSettings
        default_settings = VisualizationSettings()
        assert isinstance(default_settings.depth_chart, DepthChartSettings)
        assert isinstance(default_settings.heatmap, HeatmapSettings)
        assert isinstance(default_settings.time_and_sales, TimeAndSalesSettings)
        assert isinstance(default_settings.liquidity_map, LiquidityMapSettings)
        assert isinstance(default_settings.order_flow_footprint, OrderFlowFootprintSettings)
        assert isinstance(default_settings.dashboard, DashboardSettings)
        
        # Test custom VisualizationSettings with all parameters
        custom_depth = DepthChartSettings(
            price_levels=25,
            show_imbalances=False,
            show_current_price=False,
            color_scheme=ColorScheme.DARK,
            custom_bid_color="#00FF00",
            custom_ask_color="#FF0000",
            log_scale=True,
            show_tooltips=False,
            highlight_large_orders=False,
            large_order_threshold=3.0
        )
        
        custom_heatmap = HeatmapSettings(
            price_levels=30,
            time_window=180,
            color_scheme=ColorScheme.CUSTOM,
            custom_colormap="viridis",
            show_current_price=False,
            show_tooltips=False,
            interpolation="bilinear",
            normalization="log"
        )
        
        custom_time_sales = TimeAndSalesSettings(
            max_entries=500,
            show_direction=False,
            show_time=False,
            show_price=False,
            show_volume=False,
            color_scheme=ColorScheme.COLORBLIND,
            custom_buy_color="#AAFFAA",
            custom_sell_color="#FFAAAA",
            highlight_large_trades=False,
            large_trade_threshold=5.0
        )
        
        custom_liquidity = LiquidityMapSettings(
            price_levels=40,
            show_bid_liquidity=False,
            show_ask_liquidity=False,
            color_scheme=ColorScheme.LIGHT,
            custom_bid_color="#0000AA",
            custom_ask_color="#AA0000",
            show_tooltips=False,
            normalization="log"
        )
        
        custom_footprint = OrderFlowFootprintSettings(
            price_levels=35,
            time_window=120,
            show_imbalances=False,
            color_scheme=ColorScheme.CUSTOM,
            custom_buy_color="#CCFFCC",
            custom_sell_color="#FFCCCC",
            show_tooltips=False,
            show_delta=False,
            delta_type="trades"
        )
        
        custom_dashboard = DashboardSettings(
            layout=[
                [VisualizationType.DEPTH_CHART, VisualizationType.HEATMAP, VisualizationType.TIME_AND_SALES],
                [VisualizationType.LIQUIDITY_MAP, VisualizationType.ORDER_FLOW_FOOTPRINT]
            ],
            refresh_interval=250,
            show_title=False,
            show_timestamp=False,
            color_scheme=ColorScheme.DARK,
            custom_background_color="#111111",
            custom_text_color="#EEEEEE"
        )
        
        comprehensive_settings = VisualizationSettings(
            depth_chart=custom_depth,
            heatmap=custom_heatmap,
            time_and_sales=custom_time_sales,
            liquidity_map=custom_liquidity,
            order_flow_footprint=custom_footprint,
            dashboard=custom_dashboard
        )
        
        # Verify all custom settings are properly set
        assert comprehensive_settings.depth_chart.price_levels == 25
        assert comprehensive_settings.heatmap.time_window == 180
        assert comprehensive_settings.time_and_sales.max_entries == 500
        assert comprehensive_settings.liquidity_map.price_levels == 40
        assert comprehensive_settings.order_flow_footprint.price_levels == 35
        assert comprehensive_settings.dashboard.refresh_interval == 250    def test_market_depth_visualization_and_dashboard_comprehensive(self):
        """Test comprehensive MarketDepthVisualization and MarketDepthDashboard functionality"""
        
        now = datetime.now(timezone.utc)
        
        # Test MarketDepthVisualization with all visualization types
        viz_types = [
            VisualizationType.DEPTH_CHART,
            VisualizationType.HEATMAP,
            VisualizationType.TIME_AND_SALES,
            VisualizationType.LIQUIDITY_MAP,
            VisualizationType.ORDER_FLOW_FOOTPRINT
        ]
        
        visualizations = {}
        for viz_type in viz_types:
            viz = MarketDepthVisualization(
                symbol="EURUSD",
                timestamp=now,
                visualization_type=viz_type,
                image_data=f"base64_encoded_{viz_type.value}_data",
                settings={
                    "price_levels": 20,
                    "color_scheme": "default",
                    "custom_setting": f"{viz_type.value}_specific"
                }
            )
            visualizations[viz_type] = viz
            
            # Verify each visualization
            assert viz.symbol == "EURUSD"
            assert viz.timestamp == now
            assert viz.visualization_type == viz_type
            assert viz.image_data == f"base64_encoded_{viz_type.value}_data"
            assert viz.settings["price_levels"] == 20
            assert viz.settings["custom_setting"] == f"{viz_type.value}_specific"
        
        # Test comprehensive MarketDepthDashboard
        dashboard_settings = DashboardSettings(
            layout=[
                [VisualizationType.DEPTH_CHART, VisualizationType.HEATMAP],
                [VisualizationType.TIME_AND_SALES, VisualizationType.LIQUIDITY_MAP],
                [VisualizationType.ORDER_FLOW_FOOTPRINT]
            ],
            refresh_interval=100,
            show_title=True,
            show_timestamp=True,
            color_scheme=ColorScheme.CUSTOM,
            custom_background_color="#FFFFFF",
            custom_text_color="#000000"
        )
        
        comprehensive_dashboard = MarketDepthDashboard(
            symbol="EURUSD",
            timestamp=now,
            visualizations=visualizations,
            settings=dashboard_settings
        )
        
        # Verify comprehensive dashboard
        assert comprehensive_dashboard.symbol == "EURUSD"
        assert comprehensive_dashboard.timestamp == now
        assert len(comprehensive_dashboard.visualizations) == 5
        assert comprehensive_dashboard.settings.refresh_interval == 100
        assert comprehensive_dashboard.settings.color_scheme == ColorScheme.CUSTOM
        
        # Verify all visualization types are present
        for viz_type in viz_types:
            assert viz_type in comprehensive_dashboard.visualizations
            assert comprehensive_dashboard.visualizations[viz_type].visualization_type == viz_type

    def test_pydantic_fallback_comprehensive(self):
        """Test comprehensive pydantic fallback behavior"""
        
        # Test with pydantic unavailable
        with patch('src.forex_bot.market_depth_visualizer.models.PYDANTIC_AVAILABLE', False):
            # Test fallback BaseModel functionality
            from src.forex_bot.market_depth_visualizer.models import BaseModel
            
            fallback_model = BaseModel(
                test_attr="test_value",
                _private_attr="hidden",
                public_attr=123,
                complex_attr={"nested": "data"}
            )
            
            assert fallback_model.test_attr == "test_value"
            assert fallback_model.public_attr == 123
            assert fallback_model.complex_attr == {"nested": "data"}
            
            # Test dict method excludes private attributes
            model_dict = fallback_model.dict()
            assert model_dict["test_attr"] == "test_value"
            assert model_dict["public_attr"] == 123
            assert model_dict["complex_attr"] == {"nested": "data"}
            assert "_private_attr" not in model_dict