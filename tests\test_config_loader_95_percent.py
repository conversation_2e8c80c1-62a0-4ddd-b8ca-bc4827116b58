"""
Targeted tests to push config_loader.py to 95%+ coverage.

This module specifically targets the remaining uncovered lines:
- Line 73: .env file not found warning
- Lines 100-102: NameError exception handling
- Line 113: Project marker not found warning
- Lines 318-320: NameError exception handling in Config class
- Lines 329-331: Project marker not found warning in Config class
- Lines 334-335: Project marker not found fallback in Config class
"""

import pytest
import os
import tempfile
from unittest.mock import patch, MagicMock, mock_open
from pathlib import Path

from src.forex_bot.config_loader import Config


class TestConfigLoader95Percent:
    """Targeted tests to achieve 95%+ coverage for config_loader.py."""

    def test_env_file_not_found_warning(self, capsys):
        """Test the warning when .env file is not found - covers line 73."""
        # Mock find_dotenv to return None (no .env file found)
        with patch('src.forex_bot.config_loader.find_dotenv', return_value=None):
            with patch('src.forex_bot.config_loader.load_dotenv'):
                # Re-import the module to trigger the .env loading logic
                import importlib
                import src.forex_bot.config_loader
                importlib.reload(src.forex_bot.config_loader)
                
                # Capture the printed output
                captured = capsys.readouterr()
                assert "[WARNING] .env file not found" in captured.out

    def test_name_error_exception_handling_module_level(self):
        """Test NameError exception handling at module level - covers lines 100-102."""
        # This tests the except NameError block in the module-level code
        # We need to mock __file__ to not exist to trigger the NameError
        
        original_file = None
        if hasattr(src.forex_bot.config_loader, '__file__'):
            original_file = src.forex_bot.config_loader.__file__
        
        try:
            # Remove __file__ to trigger NameError
            if hasattr(src.forex_bot.config_loader, '__file__'):
                delattr(src.forex_bot.config_loader, '__file__')
            
            # Mock os.path.dirname to raise NameError
            with patch('src.forex_bot.config_loader.os.path.dirname', side_effect=NameError("__file__ not defined")):
                with patch('src.forex_bot.config_loader.os.getcwd', return_value='/fallback/path'):
                    # Re-import to trigger the exception handling
                    import importlib
                    import src.forex_bot.config_loader
                    importlib.reload(src.forex_bot.config_loader)
                    
                    # The fallback should have been used
                    assert True  # If we get here, the exception was handled
        finally:
            # Restore __file__ if it existed
            if original_file is not None:
                src.forex_bot.config_loader.__file__ = original_file

    def test_project_marker_not_found_warning_module_level(self, capsys):
        """Test project marker not found warning at module level - covers line 113."""
        # Mock the directory traversal to not find .env or .git
        def mock_exists(path):
            # Never find .env or .git files
            return False
        
        with patch('src.forex_bot.config_loader.os.path.exists', side_effect=mock_exists):
            with patch('src.forex_bot.config_loader.os.getcwd', return_value='/test/cwd'):
                with patch('src.forex_bot.config_loader.os.path.dirname', return_value='/test'):
                    # Re-import to trigger the warning
                    import importlib
                    import src.forex_bot.config_loader
                    importlib.reload(src.forex_bot.config_loader)
                    
                    # Capture the printed output
                    captured = capsys.readouterr()
                    assert "[WARN] KB Path: Project marker not found" in captured.out

    def test_config_name_error_exception_handling(self):
        """Test NameError exception handling in Config class - covers lines 318-320."""
        # Create a Config instance and test the NameError handling
        config = Config()
        
        # Mock __file__ to not exist in the Config class context
        with patch.object(config, '__class__.__module__', 'test_module'):
            # Mock os.path.dirname to raise NameError when accessing __file__
            with patch('src.forex_bot.config_loader.os.path.dirname') as mock_dirname:
                with patch('src.forex_bot.config_loader.os.getcwd', return_value='/fallback/config/path') as mock_getcwd:
                    # Make dirname raise NameError to simulate __file__ not being defined
                    mock_dirname.side_effect = NameError("name '__file__' is not defined")
                    
                    # Call a method that would trigger the path resolution
                    # This should trigger the NameError exception handling
                    config._resolve_knowledge_base_paths()
                    
                    # Verify that getcwd was called as fallback
                    mock_getcwd.assert_called()

    def test_config_project_marker_not_found_warning(self, capsys):
        """Test project marker not found warning in Config class - covers lines 329-331."""
        config = Config()
        
        # Mock directory traversal to never find project markers
        def mock_exists(path):
            return False  # Never find .env or .git
        
        with patch('src.forex_bot.config_loader.os.path.exists', side_effect=mock_exists):
            with patch('src.forex_bot.config_loader.os.getcwd', return_value='/test/config/cwd'):
                with patch('src.forex_bot.config_loader.os.path.dirname') as mock_dirname:
                    # Set up dirname to return different paths for traversal
                    mock_dirname.side_effect = ['/test/config', '/test', '/', '/']
                    
                    # Call the method that resolves paths
                    config._resolve_knowledge_base_paths()
                    
                    # Capture the printed output
                    captured = capsys.readouterr()
                    assert "[WARN] KB Path: Project marker not found" in captured.out

    def test_config_project_marker_not_found_fallback(self, capsys):
        """Test project marker not found fallback in Config class - covers lines 334-335."""
        config = Config()
        
        # Mock directory traversal to exhaust the loop without finding markers
        def mock_exists(path):
            return False  # Never find .env or .git
        
        def mock_dirname(path):
            # Simulate going up directory levels
            if path == '/deep/nested/path':
                return '/deep/nested'
            elif path == '/deep/nested':
                return '/deep'
            elif path == '/deep':
                return '/'
            else:
                return path  # Same path (reached root)
        
        with patch('src.forex_bot.config_loader.os.path.exists', side_effect=mock_exists):
            with patch('src.forex_bot.config_loader.os.getcwd', return_value='/fallback/cwd'):
                with patch('src.forex_bot.config_loader.os.path.dirname', side_effect=mock_dirname):
                    with patch('src.forex_bot.config_loader.os.path.abspath', side_effect=lambda x: x):
                        # Start with a deep path to trigger the loop exhaustion
                        with patch.object(config, '_get_script_directory', return_value='/deep/nested/path'):
                            # Call the method that resolves paths
                            config._resolve_knowledge_base_paths()
                            
                            # Capture the printed output
                            captured = capsys.readouterr()
                            assert "[WARN] KB Path: Project marker not found" in captured.out

    def test_config_could_not_find_project_marker_warning(self, capsys):
        """Test 'could not find project marker' warning in Config class - covers line 329."""
        config = Config()
        
        # Mock directory traversal where parent_dir == project_root (reached filesystem root)
        def mock_exists(path):
            return False  # Never find .env or .git
        
        def mock_dirname(path):
            if path == '/':
                return '/'  # Reached root, parent == current
            else:
                return '/'  # Always return root to trigger the condition
        
        with patch('src.forex_bot.config_loader.os.path.exists', side_effect=mock_exists):
            with patch('src.forex_bot.config_loader.os.getcwd', return_value='/root/fallback'):
                with patch('src.forex_bot.config_loader.os.path.dirname', side_effect=mock_dirname):
                    with patch('src.forex_bot.config_loader.os.path.abspath', side_effect=lambda x: x):
                        # Start with root path to immediately trigger parent_dir == project_root
                        with patch.object(config, '_get_script_directory', return_value='/'):
                            # Call the method that resolves paths
                            config._resolve_knowledge_base_paths()
                            
                            # Capture the printed output
                            captured = capsys.readouterr()
                            assert "[WARN] KB Path: Could not find project marker" in captured.out

    def test_config_fallback_scenarios_comprehensive(self):
        """Test comprehensive fallback scenarios for Config class."""
        config = Config()
        
        # Test various edge cases in path resolution
        test_cases = [
            {
                'name': 'empty_script_dir',
                'script_dir': '',
                'expected_fallback': True
            },
            {
                'name': 'none_script_dir', 
                'script_dir': None,
                'expected_fallback': True
            },
            {
                'name': 'nonexistent_script_dir',
                'script_dir': '/nonexistent/path',
                'expected_fallback': True
            }
        ]
        
        for case in test_cases:
            with patch('src.forex_bot.config_loader.os.path.exists', return_value=False):
                with patch('src.forex_bot.config_loader.os.getcwd', return_value='/test/fallback'):
                    with patch('src.forex_bot.config_loader.os.path.abspath', side_effect=lambda x: x or '/fallback'):
                        with patch.object(config, '_get_script_directory', return_value=case['script_dir']):
                            # This should not raise an exception
                            config._resolve_knowledge_base_paths()
                            
                            # Verify paths were set (even if to fallback values)
                            assert hasattr(config, 'knowledge_base_strategy_file_path')
                            assert hasattr(config, 'knowledge_base_metrics_file_path')

    def test_module_level_fallback_scenarios(self):
        """Test module-level fallback scenarios for edge cases."""
        # Test the module-level path resolution with various edge cases
        
        with patch('src.forex_bot.config_loader.os.path.exists', return_value=False):
            with patch('src.forex_bot.config_loader.os.getcwd', return_value='/module/fallback'):
                with patch('src.forex_bot.config_loader.os.path.dirname', return_value='/module'):
                    with patch('src.forex_bot.config_loader.os.path.abspath', side_effect=lambda x: x):
                        # Re-import to trigger module-level path resolution
                        import importlib
                        import src.forex_bot.config_loader
                        importlib.reload(src.forex_bot.config_loader)
                        
                        # Verify that the module loaded without errors
                        assert hasattr(src.forex_bot.config_loader, 'KNOWLEDGE_BASE_STRATEGY_FILE_PATH')
                        assert hasattr(src.forex_bot.config_loader, 'KNOWLEDGE_BASE_METRICS_FILE_PATH')

    def test_config_class_edge_cases(self):
        """Test Config class edge cases and boundary conditions."""
        # Test Config initialization with various scenarios
        config = Config()
        
        # Test that all required attributes are set
        required_attrs = [
            'kb_strategy_file',
            'kb_metrics_file',
            'knowledge_base_strategy_file_path',
            'knowledge_base_metrics_file_path'
        ]
        
        for attr in required_attrs:
            assert hasattr(config, attr), f"Config missing required attribute: {attr}"
            
        # Test that paths are strings
        assert isinstance(config.knowledge_base_strategy_file_path, str)
        assert isinstance(config.knowledge_base_metrics_file_path, str)
        
        # Test that paths are absolute
        assert os.path.isabs(config.knowledge_base_strategy_file_path)
        assert os.path.isabs(config.knowledge_base_metrics_file_path)