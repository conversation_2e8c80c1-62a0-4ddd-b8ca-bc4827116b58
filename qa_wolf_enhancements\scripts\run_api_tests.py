#!/usr/bin/env python3
"""
QA Wolf Enhancement Suite - API Integration Tests Script

This script runs comprehensive API integration tests for your Forex Trading Bot
to validate connection reliability and performance.

SAFETY LEVEL: MAXIMUM - Testing only, no actual trading operations
"""

import sys
import json
from pathlib import Path
from datetime import datetime

# Add the core directory to the path
sys.path.append(str(Path(__file__).parent.parent / "core"))

def main():
    """Run comprehensive API integration tests."""
    
    print("🧪 QA Wolf Enhancement Suite - API Integration Tests")
    print("🛡️ SAFETY: Testing only, zero trading impact")
    print("=" * 60)
    
    try:
        # Import API integration tester
        from api_integration_tests import APIIntegrationTester
        
        # Get project root
        project_root = Path(__file__).parent.parent.parent
        
        print(f"📁 Project root: {project_root}")
        print("🔧 Initializing API integration tester...")
        
        # Initialize tester
        tester = APIIntegrationTester(str(project_root))
        
        print("🧪 Test categories:")
        print("   - MT5 connection reliability")
        print("   - Broker API response times")
        print("   - Network resilience scenarios")
        print("   - Concurrent API call handling")
        print("   - Error handling and recovery")
        
        print(f"\n🚀 Starting comprehensive API tests...")
        print("⏱️ Estimated duration: 30-60 seconds")
        print("📊 Results will be saved to reports/")
        print("\n" + "=" * 60)
        
        # Run comprehensive tests
        report = tester.run_comprehensive_api_tests()
        
        # Display results
        print("\n🎯 API INTEGRATION TEST RESULTS")
        print("=" * 60)
        
        summary = report['summary']
        print(f"📊 Total Tests: {summary['total_tests']}")
        print(f"✅ Passed: {summary['passed_tests']}")
        print(f"❌ Failed: {summary['failed_tests']}")
        print(f"📈 Success Rate: {summary['success_rate_percent']:.1f}%")
        print(f"🏆 Overall Grade: {summary['overall_grade']}")
        print(f"⏱️ Total Duration: {report['total_duration_ms']:.1f}ms")
        
        # Safety confirmation
        safety = report['safety_confirmation']
        print(f"\n🛡️ Safety Confirmation:")
        print(f"   Trading Operations Affected: {safety['trading_operations_affected']}")
        print(f"   Test Mode Only: {safety['test_mode_only']}")
        print(f"   Zero Trading Impact: {safety['zero_trading_impact']}")
        
        # Save detailed report
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = Path(__file__).parent.parent / "reports" / f"api_test_summary_{timestamp}.json"
        
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        print(f"\n📄 Detailed report saved to: {report_file}")
        print("✅ API Integration Tests completed successfully!")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all dependencies are installed:")
        print("   pip install -r requirements.txt")
        
    except Exception as e:
        print(f"❌ Error running API tests: {e}")
        print("🔧 Check the troubleshooting guide in docs/troubleshooting.md")


if __name__ == "__main__":
    main()