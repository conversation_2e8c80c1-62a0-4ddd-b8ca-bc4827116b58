"""
Phase 5M comprehensive tests to push correlation_matrix/models.py to 90%+ coverage.
"""

import pytest
from datetime import datetime, timezone
from src.forex_bot.correlation_matrix import models

class TestCorrelationMatrixModelsPhase5M:
    """Phase 5M tests to achieve 90%+ coverage for correlation_matrix/models.py."""

    def test_time_window_enum(self):
        """Test TimeWindow enum values."""
        assert models.TimeWindow.HOUR_1.value == "1h"
        assert models.TimeWindow.HOUR_4.value == "4h"
        assert models.TimeWindow.DAY_1.value == "1d"
        assert models.TimeWindow.WEEK_1.value == "1w"
        assert models.TimeWindow.MONTH_1.value == "1m"

    def test_correlation_method_enum(self):
        """Test CorrelationMethod enum values."""
        assert models.CorrelationMethod.PEARSON.value == "pearson"
        assert models.CorrelationMethod.SPEARMAN.value == "spearman"
        assert models.CorrelationMethod.KENDALL.value == "kendall"

    def test_correlation_strength_enum(self):
        """Test CorrelationStrength enum values."""
        assert models.CorrelationStrength.STRONG_POSITIVE.value == "strong_positive"
        assert models.CorrelationStrength.MODERATE_POSITIVE.value == "moderate_positive"
        assert models.CorrelationStrength.WEAK_POSITIVE.value == "weak_positive"
        assert models.CorrelationStrength.WEAK_NEGATIVE.value == "weak_negative"
        assert models.CorrelationStrength.MODERATE_NEGATIVE.value == "moderate_negative"
        assert models.CorrelationStrength.STRONG_NEGATIVE.value == "strong_negative"

    def test_correlation_settings_basic_functionality(self):
        """Test CorrelationSettings class basic functionality."""
        
        # Test with default values
        settings = models.CorrelationSettings()
        assert settings.time_window == models.TimeWindow.DAY_1
        assert settings.method == models.CorrelationMethod.PEARSON
        assert settings.min_periods == 30
        assert settings.symbols == []
        assert settings.include_base_pairs == True
        
        # Test with custom values
        custom_settings = models.CorrelationSettings(
            time_window=models.TimeWindow.HOUR_4,
            method=models.CorrelationMethod.SPEARMAN,
            min_periods=50,
            symbols=["EURUSD", "GBPUSD"],
            include_base_pairs=False
        )
        assert custom_settings.time_window == models.TimeWindow.HOUR_4
        assert custom_settings.method == models.CorrelationMethod.SPEARMAN
        assert custom_settings.min_periods == 50
        assert custom_settings.symbols == ["EURUSD", "GBPUSD"]
        assert custom_settings.include_base_pairs == False

    def test_correlation_settings_validation(self):
        """Test CorrelationSettings validation."""
        
        # Test invalid min_periods
        with pytest.raises(ValueError, match="min_periods must be at least 2"):
            models.CorrelationSettings(min_periods=1)
        
        with pytest.raises(ValueError, match="min_periods must be at least 2"):
            models.CorrelationSettings(min_periods=0)
        
        # Test valid min_periods boundary
        valid_settings = models.CorrelationSettings(min_periods=2)
        assert valid_settings.min_periods == 2

    def test_correlation_pair_basic_functionality(self):
        """Test CorrelationPair class basic functionality."""
        
        timestamp = datetime(2023, 12, 15, 10, 0, 0, tzinfo=timezone.utc)
        
        # Test strong positive correlation
        pair = models.CorrelationPair(
            symbol1="EURUSD",
            symbol2="GBPUSD",
            correlation=0.85,
            strength=models.CorrelationStrength.STRONG_POSITIVE,
            p_value=0.001,
            timestamp=timestamp,
            time_window=models.TimeWindow.DAY_1,
            method=models.CorrelationMethod.PEARSON
        )
        
        assert pair.symbol1 == "EURUSD"
        assert pair.symbol2 == "GBPUSD"
        assert pair.correlation == 0.85
        assert pair.strength == models.CorrelationStrength.STRONG_POSITIVE
        assert pair.p_value == 0.001
        assert pair.timestamp == timestamp
        assert pair.time_window == models.TimeWindow.DAY_1
        assert pair.method == models.CorrelationMethod.PEARSON

    def test_correlation_pair_validation(self):
        """Test CorrelationPair validation."""
        
        timestamp = datetime(2023, 12, 15, 10, 0, 0, tzinfo=timezone.utc)
        
        # Test valid correlation boundaries
        valid_correlations = [-1.0, -0.5, 0.0, 0.5, 1.0]
        for correlation in valid_correlations:
            # Determine expected strength
            if correlation >= 0.7:
                expected_strength = models.CorrelationStrength.STRONG_POSITIVE
            elif correlation >= 0.3:
                expected_strength = models.CorrelationStrength.MODERATE_POSITIVE
            elif correlation >= 0.0:
                expected_strength = models.CorrelationStrength.WEAK_POSITIVE
            elif correlation >= -0.3:
                expected_strength = models.CorrelationStrength.WEAK_NEGATIVE
            elif correlation >= -0.7:
                expected_strength = models.CorrelationStrength.MODERATE_NEGATIVE
            else:
                expected_strength = models.CorrelationStrength.STRONG_NEGATIVE
            
            pair = models.CorrelationPair(
                symbol1="EURUSD",
                symbol2="USDJPY",
                correlation=correlation,
                strength=expected_strength,
                timestamp=timestamp,
                time_window=models.TimeWindow.DAY_1,
                method=models.CorrelationMethod.PEARSON
            )
            assert pair.correlation == correlation
            assert pair.strength == expected_strength
        
        # Test invalid correlation values
        with pytest.raises(ValueError, match="correlation must be between -1.0 and 1.0"):
            models.CorrelationPair(
                symbol1="EURUSD",
                symbol2="USDJPY",
                correlation=1.5,  # Invalid: > 1.0
                strength=models.CorrelationStrength.STRONG_POSITIVE,
                timestamp=timestamp,
                time_window=models.TimeWindow.DAY_1,
                method=models.CorrelationMethod.PEARSON
            )
        
        with pytest.raises(ValueError, match="correlation must be between -1.0 and 1.0"):
            models.CorrelationPair(
                symbol1="EURUSD",
                symbol2="USDJPY",
                correlation=-1.5,  # Invalid: < -1.0
                strength=models.CorrelationStrength.STRONG_NEGATIVE,
                timestamp=timestamp,
                time_window=models.TimeWindow.DAY_1,
                method=models.CorrelationMethod.PEARSON
            )
        
        # Test mismatched strength and correlation
        with pytest.raises(ValueError, match="strength should be"):
            models.CorrelationPair(
                symbol1="EURUSD",
                symbol2="USDJPY",
                correlation=0.8,  # Strong positive
                strength=models.CorrelationStrength.WEAK_POSITIVE,  # Wrong strength
                timestamp=timestamp,
                time_window=models.TimeWindow.DAY_1,
                method=models.CorrelationMethod.PEARSON
            )

    def test_correlation_strength_mapping(self):
        """Test correlation strength mapping for different correlation values."""
        
        timestamp = datetime(2023, 12, 15, 10, 0, 0, tzinfo=timezone.utc)
        
        # Test all strength categories
        test_cases = [
            (0.85, models.CorrelationStrength.STRONG_POSITIVE),
            (0.5, models.CorrelationStrength.MODERATE_POSITIVE),
            (0.1, models.CorrelationStrength.WEAK_POSITIVE),
            (-0.1, models.CorrelationStrength.WEAK_NEGATIVE),
            (-0.5, models.CorrelationStrength.MODERATE_NEGATIVE),
            (-0.85, models.CorrelationStrength.STRONG_NEGATIVE),
        ]
        
        for correlation, expected_strength in test_cases:
            pair = models.CorrelationPair(
                symbol1="EURUSD",
                symbol2="GBPUSD",
                correlation=correlation,
                strength=expected_strength,
                timestamp=timestamp,
                time_window=models.TimeWindow.DAY_1,
                method=models.CorrelationMethod.PEARSON
            )
            assert pair.correlation == correlation
            assert pair.strength == expected_strength

    def test_correlation_matrix_basic_functionality(self):
        """Test CorrelationMatrix class basic functionality."""
        
        timestamp = datetime(2023, 12, 15, 11, 0, 0, tzinfo=timezone.utc)
        symbols = ["EURUSD", "GBPUSD", "USDJPY"]
        
        # Create a valid correlation matrix
        matrix = {
            "EURUSD": {"EURUSD": 1.0, "GBPUSD": 0.7, "USDJPY": -0.3},
            "GBPUSD": {"EURUSD": 0.7, "GBPUSD": 1.0, "USDJPY": -0.2},
            "USDJPY": {"EURUSD": -0.3, "GBPUSD": -0.2, "USDJPY": 1.0}
        }
        
        correlation_matrix = models.CorrelationMatrix(
            timestamp=timestamp,
            time_window=models.TimeWindow.DAY_1,
            method=models.CorrelationMethod.PEARSON,
            symbols=symbols,
            matrix=matrix
        )
        
        assert correlation_matrix.timestamp == timestamp
        assert correlation_matrix.time_window == models.TimeWindow.DAY_1
        assert correlation_matrix.method == models.CorrelationMethod.PEARSON
        assert correlation_matrix.symbols == symbols
        assert correlation_matrix.matrix == matrix
        assert correlation_matrix.pairs == []

    def test_correlation_matrix_validation(self):
        """Test CorrelationMatrix validation."""
        
        timestamp = datetime(2023, 12, 15, 11, 0, 0, tzinfo=timezone.utc)
        symbols = ["EURUSD", "GBPUSD"]
        
        # Test missing symbol in matrix
        incomplete_matrix = {
            "EURUSD": {"EURUSD": 1.0, "GBPUSD": 0.7}
            # Missing GBPUSD row
        }
        
        with pytest.raises(ValueError, match="matrix missing symbol GBPUSD"):
            models.CorrelationMatrix(
                timestamp=timestamp,
                time_window=models.TimeWindow.DAY_1,
                method=models.CorrelationMethod.PEARSON,
                symbols=symbols,
                matrix=incomplete_matrix
            )
        
        # Test missing correlation in matrix
        incomplete_correlation_matrix = {
            "EURUSD": {"EURUSD": 1.0},  # Missing GBPUSD correlation
            "GBPUSD": {"EURUSD": 0.7, "GBPUSD": 1.0}
        }
        
        with pytest.raises(ValueError, match="matrix missing correlation between EURUSD and GBPUSD"):
            models.CorrelationMatrix(
                timestamp=timestamp,
                time_window=models.TimeWindow.DAY_1,
                method=models.CorrelationMethod.PEARSON,
                symbols=symbols,
                matrix=incomplete_correlation_matrix
            )

    def test_correlation_matrix_to_dataframe(self):
        """Test CorrelationMatrix to_dataframe method."""
        
        timestamp = datetime(2023, 12, 15, 11, 0, 0, tzinfo=timezone.utc)
        symbols = ["EURUSD", "GBPUSD"]
        
        matrix = {
            "EURUSD": {"EURUSD": 1.0, "GBPUSD": 0.7},
            "GBPUSD": {"EURUSD": 0.7, "GBPUSD": 1.0}
        }
        
        correlation_matrix = models.CorrelationMatrix(
            timestamp=timestamp,
            time_window=models.TimeWindow.DAY_1,
            method=models.CorrelationMethod.PEARSON,
            symbols=symbols,
            matrix=matrix
        )
        
        # Test to_dataframe method (if pandas is available)
        try:
            df = correlation_matrix.to_dataframe()
            assert df.shape == (2, 2)
            assert list(df.index) == symbols
            assert list(df.columns) == symbols
            assert df.loc["EURUSD", "GBPUSD"] == 0.7
        except ImportError:
            # pandas not available, test that ImportError is raised
            with pytest.raises(ImportError, match="pandas is required"):
                correlation_matrix.to_dataframe()

    def test_correlation_trend_basic_functionality(self):
        """Test CorrelationTrend class basic functionality."""
        
        timestamps = [
            datetime(2023, 12, 15, 10, 0, 0, tzinfo=timezone.utc),
            datetime(2023, 12, 15, 11, 0, 0, tzinfo=timezone.utc),
            datetime(2023, 12, 15, 12, 0, 0, tzinfo=timezone.utc)
        ]
        correlations = [0.5, 0.6, 0.7]
        
        trend = models.CorrelationTrend(
            symbol1="EURUSD",
            symbol2="GBPUSD",
            timestamps=timestamps,
            correlations=correlations,
            time_window=models.TimeWindow.HOUR_1,
            method=models.CorrelationMethod.SPEARMAN
        )
        
        assert trend.symbol1 == "EURUSD"
        assert trend.symbol2 == "GBPUSD"
        assert trend.timestamps == timestamps
        assert trend.correlations == correlations
        assert trend.time_window == models.TimeWindow.HOUR_1
        assert trend.method == models.CorrelationMethod.SPEARMAN

    def test_correlation_trend_validation(self):
        """Test CorrelationTrend validation."""
        
        timestamps = [
            datetime(2023, 12, 15, 10, 0, 0, tzinfo=timezone.utc),
            datetime(2023, 12, 15, 11, 0, 0, tzinfo=timezone.utc)
        ]
        
        # Test mismatched lengths
        with pytest.raises(ValueError, match="correlations and timestamps must have the same length"):
            models.CorrelationTrend(
                symbol1="EURUSD",
                symbol2="GBPUSD",
                timestamps=timestamps,
                correlations=[0.5, 0.6, 0.7],  # Different length
                time_window=models.TimeWindow.HOUR_1,
                method=models.CorrelationMethod.PEARSON
            )
        
        # Test invalid correlation values
        with pytest.raises(ValueError, match="all correlations must be between -1.0 and 1.0"):
            models.CorrelationTrend(
                symbol1="EURUSD",
                symbol2="GBPUSD",
                timestamps=timestamps,
                correlations=[0.5, 1.5],  # Invalid correlation
                time_window=models.TimeWindow.HOUR_1,
                method=models.CorrelationMethod.PEARSON
            )
        
        # Test valid correlation boundaries
        valid_correlations = [-1.0, 0.0, 1.0]
        trend = models.CorrelationTrend(
            symbol1="EURUSD",
            symbol2="GBPUSD",
            timestamps=[timestamps[0]] * 3,
            correlations=valid_correlations,
            time_window=models.TimeWindow.HOUR_1,
            method=models.CorrelationMethod.PEARSON
        )
        assert trend.correlations == valid_correlations

    def test_correlation_trend_to_dataframe(self):
        """Test CorrelationTrend to_dataframe method."""
        
        timestamps = [
            datetime(2023, 12, 15, 10, 0, 0, tzinfo=timezone.utc),
            datetime(2023, 12, 15, 11, 0, 0, tzinfo=timezone.utc)
        ]
        correlations = [0.5, 0.6]
        
        trend = models.CorrelationTrend(
            symbol1="EURUSD",
            symbol2="GBPUSD",
            timestamps=timestamps,
            correlations=correlations,
            time_window=models.TimeWindow.HOUR_1,
            method=models.CorrelationMethod.PEARSON
        )
        
        # Test to_dataframe method (if pandas is available)
        try:
            df = trend.to_dataframe()
            assert df.shape == (2, 2)
            assert list(df.columns) == ['timestamp', 'correlation']
            assert df['correlation'].tolist() == correlations
        except ImportError:
            # pandas not available, test that ImportError is raised
            with pytest.raises(ImportError, match="pandas is required"):
                trend.to_dataframe()

    def test_correlation_alert_basic_functionality(self):
        """Test CorrelationAlert class basic functionality."""
        
        timestamp = datetime(2023, 12, 15, 12, 0, 0, tzinfo=timezone.utc)
        
        alert = models.CorrelationAlert(
            symbol1="EURUSD",
            symbol2="GBPUSD",
            old_correlation=0.5,
            new_correlation=0.8,
            change=0.3,
            timestamp=timestamp,
            time_window=models.TimeWindow.DAY_1,
            method=models.CorrelationMethod.PEARSON,
            is_significant=True
        )
        
        assert alert.symbol1 == "EURUSD"
        assert alert.symbol2 == "GBPUSD"
        assert alert.old_correlation == 0.5
        assert alert.new_correlation == 0.8
        assert alert.change == 0.3
        assert alert.timestamp == timestamp
        assert alert.time_window == models.TimeWindow.DAY_1
        assert alert.method == models.CorrelationMethod.PEARSON
        assert alert.is_significant == True

    def test_correlation_alert_validation(self):
        """Test CorrelationAlert validation."""
        
        timestamp = datetime(2023, 12, 15, 12, 0, 0, tzinfo=timezone.utc)
        
        # Test valid change calculation
        alert = models.CorrelationAlert(
            symbol1="EURUSD",
            symbol2="GBPUSD",
            old_correlation=0.3,
            new_correlation=0.7,
            change=0.4,  # 0.7 - 0.3 = 0.4
            timestamp=timestamp,
            time_window=models.TimeWindow.DAY_1,
            method=models.CorrelationMethod.PEARSON,
            is_significant=True
        )
        assert alert.change == 0.4
        
        # Test invalid change calculation
        with pytest.raises(ValueError, match="change should be"):
            models.CorrelationAlert(
                symbol1="EURUSD",
                symbol2="GBPUSD",
                old_correlation=0.3,
                new_correlation=0.7,
                change=0.5,  # Wrong: should be 0.4
                timestamp=timestamp,
                time_window=models.TimeWindow.DAY_1,
                method=models.CorrelationMethod.PEARSON,
                is_significant=True
            )
        
        # Test negative change
        negative_alert = models.CorrelationAlert(
            symbol1="EURUSD",
            symbol2="USDJPY",
            old_correlation=0.6,
            new_correlation=0.2,
            change=-0.4,  # 0.2 - 0.6 = -0.4
            timestamp=timestamp,
            time_window=models.TimeWindow.DAY_1,
            method=models.CorrelationMethod.PEARSON,
            is_significant=True
        )
        assert negative_alert.change == -0.4

    def test_correlation_visualization_basic_functionality(self):
        """Test CorrelationVisualization class basic functionality."""
        
        timestamp = datetime(2023, 12, 15, 13, 0, 0, tzinfo=timezone.utc)
        symbols = ["EURUSD", "GBPUSD", "USDJPY"]
        image_data = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg=="
        
        visualization = models.CorrelationVisualization(
            timestamp=timestamp,
            time_window=models.TimeWindow.DAY_1,
            method=models.CorrelationMethod.PEARSON,
            symbols=symbols,
            image_data=image_data
        )
        
        assert visualization.timestamp == timestamp
        assert visualization.time_window == models.TimeWindow.DAY_1
        assert visualization.method == models.CorrelationMethod.PEARSON
        assert visualization.symbols == symbols
        assert visualization.image_data == image_data

    def test_correlation_visualization_validation(self):
        """Test CorrelationVisualization validation."""
        
        timestamp = datetime(2023, 12, 15, 13, 0, 0, tzinfo=timezone.utc)
        symbols = ["EURUSD", "GBPUSD"]
        
        # Test empty image_data
        with pytest.raises(ValueError, match="image_data cannot be empty"):
            models.CorrelationVisualization(
                timestamp=timestamp,
                time_window=models.TimeWindow.DAY_1,
                method=models.CorrelationMethod.PEARSON,
                symbols=symbols,
                image_data=""  # Empty string
            )

    def test_edge_cases_and_boundary_conditions(self):
        """Test edge cases and boundary conditions."""
        
        timestamp = datetime(2023, 12, 15, 14, 0, 0, tzinfo=timezone.utc)
        
        # Test correlation pair with None p_value
        pair = models.CorrelationPair(
            symbol1="EURUSD",
            symbol2="GBPUSD",
            correlation=0.0,
            strength=models.CorrelationStrength.WEAK_POSITIVE,
            p_value=None,  # Optional field
            timestamp=timestamp,
            time_window=models.TimeWindow.DAY_1,
            method=models.CorrelationMethod.PEARSON
        )
        assert pair.p_value is None
        
        # Test correlation matrix with empty pairs list
        matrix = {
            "EURUSD": {"EURUSD": 1.0}
        }
        correlation_matrix = models.CorrelationMatrix(
            timestamp=timestamp,
            time_window=models.TimeWindow.DAY_1,
            method=models.CorrelationMethod.PEARSON,
            symbols=["EURUSD"],
            matrix=matrix,
            pairs=[]  # Empty pairs list
        )
        assert correlation_matrix.pairs == []
        
        # Test correlation settings with empty symbols list
        settings = models.CorrelationSettings(symbols=[])
        assert settings.symbols == []