"""
Phase 3 comprehensive tests to push correlation_matrix/models.py to 90%+ coverage.

This module targets the non-pydantic implementations (lines 208-316) to achieve maximum coverage.
"""

import pytest
from unittest.mock import patch
from datetime import datetime, timezone

class TestCorrelationMatrixModelsPhase3:
    """Phase 3 tests to achieve 90%+ coverage for correlation_matrix/models.py."""

    def test_non_pydantic_correlation_settings_comprehensive(self):
        """Test non-pydantic CorrelationSettings comprehensive functionality."""
        with patch('src.forex_bot.correlation_matrix.models.PYDANTIC_AVAILABLE', False):
            import importlib
            from src.forex_bot.correlation_matrix import models
            importlib.reload(models)
            
            # Test successful creation with all parameters
            settings = models.CorrelationSettings(
                time_window=models.TimeWindow.HOUR_4,
                method=models.CorrelationMethod.SPEARMAN,
                min_periods=50,
                symbols=['EURUSD', 'GBPUSD'],
                include_base_pairs=False
            )
            
            assert settings.time_window == models.TimeWindow.HOUR_4
            assert settings.method == models.CorrelationMethod.SPEARMAN
            assert settings.min_periods == 50
            assert settings.symbols == ['EURUSD', 'GBPUSD']
            assert settings.include_base_pairs == False
            
            # Test dict method
            settings_dict = settings.dict()
            assert isinstance(settings_dict, dict)
            assert settings_dict['time_window'] == models.TimeWindow.HOUR_4
            assert settings_dict['method'] == models.CorrelationMethod.SPEARMAN
            assert settings_dict['min_periods'] == 50
            
            # Test min_periods validation
            with pytest.raises(ValueError, match="min_periods must be at least 2"):
                models.CorrelationSettings(min_periods=1)

    def test_non_pydantic_correlation_pair_comprehensive(self):
        """Test non-pydantic CorrelationPair comprehensive functionality."""
        with patch('src.forex_bot.correlation_matrix.models.PYDANTIC_AVAILABLE', False):
            import importlib
            from src.forex_bot.correlation_matrix import models
            importlib.reload(models)
            
            # Test successful creation with strong positive correlation
            timestamp = datetime.now(timezone.utc)
            pair = models.CorrelationPair(
                symbol1='EURUSD',
                symbol2='GBPUSD',
                correlation=0.85,
                strength=models.CorrelationStrength.STRONG_POSITIVE,
                p_value=0.001,
                timestamp=timestamp,
                time_window=models.TimeWindow.DAY_1,
                method=models.CorrelationMethod.PEARSON
            )
            
            assert pair.symbol1 == 'EURUSD'
            assert pair.symbol2 == 'GBPUSD'
            assert pair.correlation == 0.85
            assert pair.strength == models.CorrelationStrength.STRONG_POSITIVE
            assert pair.p_value == 0.001
            assert pair.timestamp == timestamp
            assert pair.time_window == models.TimeWindow.DAY_1
            assert pair.method == models.CorrelationMethod.PEARSON