"""
Enhanced tests to push correlation_matrix/models.py to 90%+ coverage.

This module targets all remaining uncovered lines to achieve comprehensive coverage.
"""

import pytest
from unittest.mock import patch
from datetime import datetime, timezone

class TestCorrelationMatrixModelsEnhanced:
    """Enhanced tests to achieve 90%+ coverage for correlation_matrix/models.py."""

    def test_non_pydantic_correlation_settings_comprehensive(self):
        """Test non-pydantic CorrelationSettings comprehensive functionality."""
        with patch('src.forex_bot.correlation_matrix.models.PYDANTIC_AVAILABLE', False):
            import importlib
            from src.forex_bot.correlation_matrix import models
            importlib.reload(models)
            
            # Test successful creation
            settings = models.CorrelationSettings(
                time_window=models.TimeWindow.DAY_1,
                method=models.CorrelationMethod.PEARSON,
                min_periods=30,
                symbols=["EURUSD", "GBPUSD"],
                include_base_pairs=True
            )
            
            assert settings.time_window == models.TimeWindow.DAY_1
            assert settings.method == models.CorrelationMethod.PEARSON
            assert settings.min_periods == 30
            assert settings.symbols == ["EURUSD", "GBPUSD"]
            assert settings.include_base_pairs == True
            
            # Test dict method
            settings_dict = settings.dict()
            assert "time_window" in settings_dict
            assert "method" in settings_dict
            assert "min_periods" in settings_dict
            
            # Test min_periods validation error
            with pytest.raises(ValueError) as exc_info:
                models.CorrelationSettings(min_periods=1)
            assert "min_periods must be at least 2" in str(exc_info.value)
            
            # Test default symbols
            default_settings = models.CorrelationSettings()
            assert default_settings.symbols == []

    def test_non_pydantic_correlation_pair_comprehensive(self):
        """Test non-pydantic CorrelationPair comprehensive functionality."""
        with patch('src.forex_bot.correlation_matrix.models.PYDANTIC_AVAILABLE', False):
            import importlib
            from src.forex_bot.correlation_matrix import models
            importlib.reload(models)
            
            timestamp = datetime.now(timezone.utc)
            
            # Test strong positive correlation
            pair = models.CorrelationPair(
                symbol1="EURUSD",
                symbol2="GBPUSD",
                correlation=0.8,
                strength=models.CorrelationStrength.STRONG_POSITIVE,
                p_value=0.01,
                timestamp=timestamp,
                time_window=models.TimeWindow.DAY_1,
                method=models.CorrelationMethod.PEARSON
            )
            
            assert pair.symbol1 == "EURUSD"
            assert pair.symbol2 == "GBPUSD"
            assert pair.correlation == 0.8
            assert pair.strength == models.CorrelationStrength.STRONG_POSITIVE
            assert pair.p_value == 0.01
            assert pair.timestamp == timestamp            
            # Test dict method
            pair_dict = pair.dict()
            assert "symbol1" in pair_dict
            assert "correlation" in pair_dict
            
            # Test correlation validation error
            with pytest.raises(ValueError) as exc_info:
                models.CorrelationPair(
                    symbol1="EURUSD",
                    symbol2="GBPUSD",
                    correlation=1.5,  # Invalid correlation > 1.0
                    strength=models.CorrelationStrength.STRONG_POSITIVE
                )
            assert "correlation must be between -1.0 and 1.0" in str(exc_info.value)
            
            # Test strength validation for different correlation ranges
            test_cases = [
                (0.8, models.CorrelationStrength.STRONG_POSITIVE),
                (0.5, models.CorrelationStrength.MODERATE_POSITIVE),
                (0.1, models.CorrelationStrength.WEAK_POSITIVE),
                (-0.1, models.CorrelationStrength.WEAK_NEGATIVE),
                (-0.5, models.CorrelationStrength.MODERATE_NEGATIVE),
                (-0.8, models.CorrelationStrength.STRONG_NEGATIVE)
            ]
            
            for correlation, expected_strength in test_cases:
                pair = models.CorrelationPair(
                    symbol1="EURUSD",
                    symbol2="GBPUSD",
                    correlation=correlation,
                    strength=expected_strength
                )
                assert pair.strength == expected_strength
            
            # Test strength validation error
            with pytest.raises(ValueError) as exc_info:
                models.CorrelationPair(
                    symbol1="EURUSD",
                    symbol2="GBPUSD",
                    correlation=0.8,
                    strength=models.CorrelationStrength.WEAK_POSITIVE  # Wrong strength for 0.8
                )
            assert "strength should be" in str(exc_info.value)

    def test_non_pydantic_correlation_matrix_comprehensive(self):
        """Test non-pydantic CorrelationMatrix comprehensive functionality."""
        with patch('src.forex_bot.correlation_matrix.models.PYDANTIC_AVAILABLE', False):
            import importlib
            from src.forex_bot.correlation_matrix import models
            importlib.reload(models)
            
            timestamp = datetime.now(timezone.utc)
            symbols = ["EURUSD", "GBPUSD"]
            matrix = {
                "EURUSD": {"EURUSD": 1.0, "GBPUSD": 0.8},
                "GBPUSD": {"EURUSD": 0.8, "GBPUSD": 1.0}
            }
            
            # Test successful creation
            corr_matrix = models.CorrelationMatrix(
                timestamp=timestamp,
                time_window=models.TimeWindow.DAY_1,
                method=models.CorrelationMethod.PEARSON,
                symbols=symbols,
                matrix=matrix
            )
            
            assert corr_matrix.timestamp == timestamp
            assert corr_matrix.symbols == symbols
            assert corr_matrix.matrix == matrix
            
            # Test dict method
            matrix_dict = corr_matrix.dict()
            assert "timestamp" in matrix_dict
            assert "symbols" in matrix_dict            
            # Test matrix validation errors
            with pytest.raises(ValueError) as exc_info:
                models.CorrelationMatrix(
                    timestamp=timestamp,
                    time_window=models.TimeWindow.DAY_1,
                    method=models.CorrelationMethod.PEARSON,
                    symbols=["EURUSD", "GBPUSD", "USDJPY"],  # Missing USDJPY in matrix
                    matrix=matrix
                )
            assert "matrix missing symbol USDJPY" in str(exc_info.value)
            
            with pytest.raises(ValueError) as exc_info:
                incomplete_matrix = {
                    "EURUSD": {"EURUSD": 1.0},  # Missing GBPUSD correlation
                    "GBPUSD": {"EURUSD": 0.8, "GBPUSD": 1.0}
                }
                models.CorrelationMatrix(
                    timestamp=timestamp,
                    time_window=models.TimeWindow.DAY_1,
                    method=models.CorrelationMethod.PEARSON,
                    symbols=symbols,
                    matrix=incomplete_matrix
                )
            assert "matrix missing correlation between EURUSD and GBPUSD" in str(exc_info.value)

    def test_non_pydantic_correlation_trend_comprehensive(self):
        """Test non-pydantic CorrelationTrend comprehensive functionality."""
        with patch('src.forex_bot.correlation_matrix.models.PYDANTIC_AVAILABLE', False):
            import importlib
            from src.forex_bot.correlation_matrix import models
            importlib.reload(models)
            
            timestamps = [datetime.now(timezone.utc) for _ in range(3)]
            correlations = [0.8, 0.7, 0.9]
            
            # Test successful creation
            trend = models.CorrelationTrend(
                symbol1="EURUSD",
                symbol2="GBPUSD",
                timestamps=timestamps,
                correlations=correlations,
                time_window=models.TimeWindow.DAY_1,
                method=models.CorrelationMethod.PEARSON
            )
            
            assert trend.symbol1 == "EURUSD"
            assert trend.symbol2 == "GBPUSD"
            assert trend.timestamps == timestamps
            assert trend.correlations == correlations
            
            # Test dict method
            trend_dict = trend.dict()
            assert "symbol1" in trend_dict
            assert "correlations" in trend_dict
            
            # Test length mismatch validation error
            with pytest.raises(ValueError) as exc_info:
                models.CorrelationTrend(
                    symbol1="EURUSD",
                    symbol2="GBPUSD",
                    timestamps=timestamps,
                    correlations=[0.8, 0.7],  # Different length
                    time_window=models.TimeWindow.DAY_1,
                    method=models.CorrelationMethod.PEARSON
                )
            assert "correlations and timestamps must have the same length" in str(exc_info.value)
            
            # Test correlation range validation error
            with pytest.raises(ValueError) as exc_info:
                models.CorrelationTrend(
                    symbol1="EURUSD",
                    symbol2="GBPUSD",
                    timestamps=timestamps,
                    correlations=[0.8, 1.5, 0.9],  # Invalid correlation 1.5
                    time_window=models.TimeWindow.DAY_1,
                    method=models.CorrelationMethod.PEARSON
                )
            assert "all correlations must be between -1.0 and 1.0" in str(exc_info.value)