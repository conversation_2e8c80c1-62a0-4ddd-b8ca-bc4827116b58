"""
Phase 5D comprehensive tests to push metrics_dashboard/models.py to 90%+ coverage.
"""

import pytest
from datetime import datetime, timezone

class TestMetricsDashboardModelsPhase5:
    """Phase 5D tests to achieve 90%+ coverage for metrics_dashboard/models.py."""

    def test_comprehensive_enum_validation(self):
        """Test all enum values and their usage."""
        from src.forex_bot.metrics_dashboard import models
        
        # Test TimeFrame enum
        time_frames = [
            models.TimeFrame.TODAY,
            models.TimeFrame.YESTERDAY,
            models.TimeFrame.THIS_WEEK,
            models.TimeFrame.LAST_WEEK,
            models.TimeFrame.THIS_MONTH,
            models.TimeFrame.LAST_MONTH,
            models.TimeFrame.THIS_YEAR,
            models.TimeFrame.LAST_YEAR,
            models.TimeFrame.CUSTOM
        ]
        
        for time_frame in time_frames:
            assert isinstance(time_frame, models.TimeFrame)
        
        # Test MetricCategory enum
        categories = [
            models.MetricCategory.PERFORMANCE,
            models.MetricCategory.TRADE,
            models.MetricCategory.MARKET,
            models.MetricCategory.SYSTEM
        ]
        
        for category in categories:
            assert isinstance(category, models.MetricCategory)
        
        # Test ChartType enum
        chart_types = [
            models.ChartType.LINE,
            models.ChartType.BAR,
            models.ChartType.SCATTER,
            models.ChartType.PIE,
            models.ChartType.HEATMAP,
            models.ChartType.HISTOGRAM,
            models.ChartType.GAUGE,
            models.ChartType.TABLE,
            models.ChartType.CANDLESTICK
        ]
        
        for chart_type in chart_types:
            assert isinstance(chart_type, models.ChartType)    def test_comprehensive_metric_value_validation(self):
        """Test MetricValue validation scenarios."""
        from src.forex_bot.metrics_dashboard import models
        
        timestamp = datetime.now(timezone.utc)
        
        # Test successful creation
        metric = models.MetricValue(
            name='test_metric',
            value=100.5,
            unit='USD',
            timestamp=timestamp,
            category=models.MetricCategory.PERFORMANCE,
            symbol='EURUSD'
        )
        assert metric.name == 'test_metric'
        assert metric.value == 100.5
        assert metric.unit == 'USD'
        assert metric.symbol == 'EURUSD'
        
        # Test without optional fields
        metric_minimal = models.MetricValue(
            name='minimal',
            value=50.0,
            timestamp=timestamp,
            category=models.MetricCategory.TRADE
        )
        assert metric_minimal.unit is None
        assert metric_minimal.symbol is None
        
        # Test value validation - NaN and Inf should be rejected if numpy is available
        try:
            import numpy as np
            with pytest.raises(ValueError, match="value cannot be NaN or Inf"):
                models.MetricValue(
                    name='test', value=float('nan'), timestamp=timestamp,
                    category=models.MetricCategory.PERFORMANCE
                )
            
            with pytest.raises(ValueError, match="value cannot be NaN or Inf"):
                models.MetricValue(
                    name='test', value=float('inf'), timestamp=timestamp,
                    category=models.MetricCategory.PERFORMANCE
                )
        except ImportError:
            # If numpy is not available, these validations won't run
            pass    def test_comprehensive_metric_time_series_validation(self):
        """Test MetricTimeSeries validation scenarios."""
        from src.forex_bot.metrics_dashboard import models
        
        timestamp = datetime.now(timezone.utc)
        timestamps = [timestamp, timestamp]
        values = [100.0, 200.0]
        
        # Test successful creation
        time_series = models.MetricTimeSeries(
            name='test_series',
            values=values,
            timestamps=timestamps,
            unit='USD',
            category=models.MetricCategory.MARKET,
            symbol='GBPUSD'
        )
        assert time_series.name == 'test_series'
        assert len(time_series.values) == 2
        assert len(time_series.timestamps) == 2
        
        # Test length validation
        with pytest.raises(ValueError, match="values and timestamps must have the same length"):
            models.MetricTimeSeries(
                name='test', values=[1.0, 2.0, 3.0], timestamps=[timestamp],
                category=models.MetricCategory.PERFORMANCE
            )
        
        # Test NaN/Inf validation if numpy is available
        try:
            import numpy as np
            with pytest.raises(ValueError, match="values cannot contain NaN or Inf"):
                models.MetricTimeSeries(
                    name='test', values=[1.0, float('nan')], timestamps=[timestamp, timestamp],
                    category=models.MetricCategory.PERFORMANCE
                )
        except ImportError:
            pass
        
        # Test to_dataframe method if pandas is available
        try:
            import pandas as pd
            df = time_series.to_dataframe()
            assert len(df) == 2
            assert 'timestamp' in df.columns
            assert 'value' in df.columns
        except ImportError:
            # Test that ImportError is raised when pandas is not available
            with pytest.raises(ImportError, match="pandas is required for to_dataframe"):
                time_series.to_dataframe()    def test_comprehensive_performance_metrics_validation(self):
        """Test PerformanceMetrics validation scenarios."""
        from src.forex_bot.metrics_dashboard import models
        
        timestamp = datetime.now(timezone.utc)
        
        # Test successful creation
        perf_metrics = models.PerformanceMetrics(
            total_profit_loss=1500.0,
            win_loss_ratio=1.5,
            average_win=250.0,
            average_loss=-150.0,
            sharpe_ratio=1.2,
            sortino_ratio=1.8,
            max_drawdown=-500.0,
            roi=15.5,
            profit_factor=1.3,
            timestamp=timestamp,
            symbol='EURUSD'
        )
        assert perf_metrics.total_profit_loss == 1500.0
        assert perf_metrics.win_loss_ratio == 1.5
        assert perf_metrics.sharpe_ratio == 1.2
        
        # Test without optional fields
        perf_minimal = models.PerformanceMetrics(
            total_profit_loss=1000.0,
            win_loss_ratio=2.0,
            average_win=200.0,
            average_loss=-100.0,
            max_drawdown=-300.0,
            roi=10.0,
            profit_factor=1.5,
            timestamp=timestamp
        )
        assert perf_minimal.sharpe_ratio is None
        assert perf_minimal.sortino_ratio is None
        assert perf_minimal.symbol is None
        
        # Test positive validation
        with pytest.raises(ValueError, match="value must be positive"):
            models.PerformanceMetrics(
                total_profit_loss=1000.0, win_loss_ratio=-1.0, average_win=200.0,
                average_loss=-100.0, max_drawdown=-300.0, roi=10.0, profit_factor=1.5,
                timestamp=timestamp
            )
        
        # Test max_drawdown validation
        with pytest.raises(ValueError, match="max_drawdown must be negative or zero"):
            models.PerformanceMetrics(
                total_profit_loss=1000.0, win_loss_ratio=1.5, average_win=200.0,
                average_loss=-100.0, max_drawdown=100.0, roi=10.0, profit_factor=1.5,
                timestamp=timestamp
            )    def test_comprehensive_trade_metrics_validation(self):
        """Test TradeMetrics validation scenarios."""
        from src.forex_bot.metrics_dashboard import models
        
        timestamp = datetime.now(timezone.utc)
        
        # Test successful creation
        trade_metrics = models.TradeMetrics(
            total_trades=100,
            winning_trades=60,
            losing_trades=40,
            average_trade_duration=3600.0,
            trade_frequency=5.5,
            average_position_size=10000.0,
            timestamp=timestamp,
            symbol='GBPUSD'
        )
        assert trade_metrics.total_trades == 100
        assert trade_metrics.winning_trades == 60
        assert trade_metrics.losing_trades == 40
        
        # Test non-negative validation
        with pytest.raises(ValueError, match="value must be non-negative"):
            models.TradeMetrics(
                total_trades=-1, winning_trades=60, losing_trades=40,
                average_trade_duration=3600.0, trade_frequency=5.5,
                average_position_size=10000.0, timestamp=timestamp
            )
        
        # Test trades validation - winning + losing cannot exceed total
        with pytest.raises(ValueError, match="winning_trades and losing_trades cannot exceed total_trades"):
            models.TradeMetrics(
                total_trades=50, winning_trades=60, losing_trades=40,
                average_trade_duration=3600.0, trade_frequency=5.5,
                average_position_size=10000.0, timestamp=timestamp
            )    def test_comprehensive_market_metrics_validation(self):
        """Test MarketMetrics validation scenarios."""
        from src.forex_bot.metrics_dashboard import models
        
        timestamp = datetime.now(timezone.utc)
        
        # Test successful creation
        market_metrics = models.MarketMetrics(
            volatility=0.15,
            trend_strength=0.8,
            support_level=1.2000,
            resistance_level=1.2500,
            correlation_with_major_pairs={'GBPUSD': 0.7, 'USDJPY': -0.3},
            sentiment_score=0.6,
            timestamp=timestamp,
            symbol='EURUSD'
        )
        assert market_metrics.volatility == 0.15
        assert market_metrics.trend_strength == 0.8
        assert market_metrics.sentiment_score == 0.6
        
        # Test without optional fields
        market_minimal = models.MarketMetrics(
            volatility=0.10,
            trend_strength=-0.5,
            timestamp=timestamp,
            symbol='GBPUSD'
        )
        assert market_minimal.support_level is None
        assert market_minimal.resistance_level is None
        assert market_minimal.sentiment_score is None
        
        # Test volatility validation
        with pytest.raises(ValueError, match="volatility must be non-negative"):
            models.MarketMetrics(
                volatility=-0.1, trend_strength=0.5, timestamp=timestamp, symbol='TEST'
            )
        
        # Test trend_strength validation
        with pytest.raises(ValueError, match="trend_strength must be between -1.0 and 1.0"):
            models.MarketMetrics(
                volatility=0.1, trend_strength=1.5, timestamp=timestamp, symbol='TEST'
            )
        
        # Test sentiment_score validation
        with pytest.raises(ValueError, match="sentiment_score must be between -1.0 and 1.0"):
            models.MarketMetrics(
                volatility=0.1, trend_strength=0.5, sentiment_score=2.0,
                timestamp=timestamp, symbol='TEST'
            )    def test_comprehensive_system_metrics_validation(self):
        """Test SystemMetrics validation scenarios."""
        from src.forex_bot.metrics_dashboard import models
        
        timestamp = datetime.now(timezone.utc)
        
        # Test successful creation
        system_metrics = models.SystemMetrics(
            execution_latency=50.5,
            order_fill_rate=98.5,
            system_uptime=86400.0,
            error_rate=0.1,
            cpu_usage=45.0,
            memory_usage=60.0,
            timestamp=timestamp
        )
        assert system_metrics.execution_latency == 50.5
        assert system_metrics.order_fill_rate == 98.5
        assert system_metrics.cpu_usage == 45.0
        
        # Test non-negative validation
        with pytest.raises(ValueError, match="value must be non-negative"):
            models.SystemMetrics(
                execution_latency=-10.0, order_fill_rate=98.5, system_uptime=86400.0,
                error_rate=0.1, cpu_usage=45.0, memory_usage=60.0, timestamp=timestamp
            )
        
        # Test percentage validation
        with pytest.raises(ValueError, match="value must be between 0 and 100"):
            models.SystemMetrics(
                execution_latency=50.0, order_fill_rate=150.0, system_uptime=86400.0,
                error_rate=0.1, cpu_usage=45.0, memory_usage=60.0, timestamp=timestamp
            )

    def test_comprehensive_chart_data_validation(self):
        """Test ChartData validation scenarios."""
        from src.forex_bot.metrics_dashboard import models
        
        timestamp = datetime.now(timezone.utc)
        
        # Test successful creation
        chart_data = models.ChartData(
            chart_type=models.ChartType.LINE,
            title='Test Chart',
            data=[1, 2, 3, 4, 5],
            x_label='Time',
            y_label='Value',
            category=models.MetricCategory.PERFORMANCE,
            symbol='EURUSD',
            timestamp=timestamp
        )
        assert chart_data.chart_type == models.ChartType.LINE
        assert chart_data.title == 'Test Chart'
        assert chart_data.data == [1, 2, 3, 4, 5]    def test_comprehensive_dashboard_layout_validation(self):
        """Test DashboardLayout validation scenarios."""
        from src.forex_bot.metrics_dashboard import models
        
        # Test successful creation
        layout = models.DashboardLayout(
            rows=3,
            columns=4,
            charts=[
                {'chart_id': 'chart1', 'row': 0, 'column': 0, 'row_span': 1, 'col_span': 2},
                {'chart_id': 'chart2', 'row': 1, 'column': 2, 'row_span': 2, 'col_span': 1}
            ]
        )
        assert layout.rows == 3
        assert layout.columns == 4
        assert len(layout.charts) == 2
        
        # Test positive validation
        with pytest.raises(ValueError, match="value must be positive"):
            models.DashboardLayout(rows=0, columns=2, charts=[])
        
        # Test chart position validation
        with pytest.raises(ValueError, match="row must be between 0 and 2"):
            models.DashboardLayout(
                rows=3, columns=3,
                charts=[{'chart_id': 'test', 'row': 3, 'column': 0}]
            )
        
        with pytest.raises(ValueError, match="column must be between 0 and 2"):
            models.DashboardLayout(
                rows=3, columns=3,
                charts=[{'chart_id': 'test', 'row': 0, 'column': 3}]
            )
        
        # Test span validation
        with pytest.raises(ValueError, match="row_span must be positive"):
            models.DashboardLayout(
                rows=3, columns=3,
                charts=[{'chart_id': 'test', 'row': 0, 'column': 0, 'row_span': 0}]
            )

    def test_comprehensive_dashboard_validation(self):
        """Test Dashboard validation scenarios."""
        from src.forex_bot.metrics_dashboard import models
        
        timestamp = datetime.now(timezone.utc)
        
        layout = models.DashboardLayout(rows=2, columns=2, charts=[])
        chart_data = models.ChartData(
            chart_type=models.ChartType.BAR, title='Test', data=[],
            category=models.MetricCategory.TRADE, timestamp=timestamp
        )
        
        # Test successful creation with non-custom timeframe
        dashboard = models.Dashboard(
            name='Test Dashboard',
            description='A test dashboard',
            layout=layout,
            charts={'chart1': chart_data},
            time_frame=models.TimeFrame.TODAY,
            last_updated=timestamp
        )
        assert dashboard.name == 'Test Dashboard'
        assert dashboard.time_frame == models.TimeFrame.TODAY
        
        # Test custom timeframe validation - should require custom dates
        with pytest.raises(ValueError, match="custom_start_date and custom_end_date are required when time_frame is CUSTOM"):
            models.Dashboard(
                name='Custom Dashboard', layout=layout, charts={},
                time_frame=models.TimeFrame.CUSTOM, last_updated=timestamp
            )
        
        # Test custom date validation - end date must be after start date
        start_date = datetime(2023, 1, 1, tzinfo=timezone.utc)
        end_date = datetime(2022, 12, 31, tzinfo=timezone.utc)
        
        with pytest.raises(ValueError, match="custom_end_date must be after custom_start_date"):
            models.Dashboard(
                name='Custom Dashboard', layout=layout, charts={},
                time_frame=models.TimeFrame.CUSTOM, custom_start_date=start_date,
                custom_end_date=end_date, last_updated=timestamp
            )