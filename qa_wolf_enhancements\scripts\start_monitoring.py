#!/usr/bin/env python3
"""
QA Wolf Enhancement Suite - Start Monitoring Script

This script starts the real-time monitoring overlay for your Forex Trading Bot.
Provides system performance tracking with zero trading impact.

SAFETY LEVEL: MAXIMUM - Read-only monitoring, no trading interference
"""

import sys
import time
import logging
from pathlib import Path

# Add the core directory to the path
sys.path.append(str(Path(__file__).parent.parent / "core"))

def main():
    """Start the QA Wolf monitoring overlay."""
    
    print("🚀 QA Wolf Enhancement Suite - Monitoring Overlay")
    print("🛡️ SAFETY: Zero trading impact guaranteed")
    print("=" * 60)
    
    try:
        # Import monitoring overlay
        from monitoring_overlay import QAWolfMonitoringOverlay
        
        # Get project root (go up from scripts -> qa_wolf_enhancements -> project_root)
        project_root = Path(__file__).parent.parent.parent
        
        print(f"📁 Project root: {project_root}")
        print("🔧 Initializing monitoring overlay...")
        
        # Initialize monitoring with 60-second intervals
        monitor = QAWolfMonitoringOverlay(str(project_root), monitoring_interval=60)
        
        print("✅ Monitoring overlay initialized")
        print("🚀 Starting real-time monitoring...")
        print("📊 Monitoring interval: 60 seconds")
        print("💾 Data storage: qa_wolf_enhancements/data/monitoring.db")
        print("\n📈 Monitoring metrics:")
        print("   - System performance (CPU, Memory, Disk)")
        print("   - Trading bot status detection")
        print("   - Resource usage tracking")
        print("   - Alert threshold monitoring")
        
        # Start monitoring
        monitor.start_monitoring()
        
        print("\n✅ Monitoring is now ACTIVE!")
        print("🌐 To view real-time dashboard, run: python scripts/start_dashboard.py")
        print("⏹️ To stop monitoring, press Ctrl+C")
        print("\n" + "=" * 60)
        
        # Keep the script running
        try:
            while True:
                status = monitor.get_current_status()
                print(f"📊 Status: {status['monitoring_active']} | "
                      f"Metrics: {status['metrics_count']} | "
                      f"Health snapshots: {status['health_snapshots']}")
                time.sleep(300)  # Status update every 5 minutes
                
        except KeyboardInterrupt:
            print("\n⏹️ Stopping monitoring...")
            monitor.stop_monitoring()
            print("✅ Monitoring stopped successfully")
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all dependencies are installed:")
        print("   pip install -r requirements.txt")
        
    except Exception as e:
        print(f"❌ Error starting monitoring: {e}")
        print("🔧 Check the troubleshooting guide in docs/troubleshooting.md")


if __name__ == "__main__":
    main()