"""
Phase 5B comprehensive tests to push correlation_matrix/models.py to 90%+ coverage.
"""

import pytest
import numpy as np
from datetime import datetime, timezone

class TestCorrelationMatrixModelsPhase5Clean:
    """Phase 5B tests to achieve 90%+ coverage for correlation_matrix/models.py."""

    def test_comprehensive_model_validation(self):
        """Test comprehensive validation for all correlation matrix model classes."""
        from src.forex_bot.correlation_matrix import models
        
        timestamp = datetime.now(timezone.utc)
        
        # Test CorrelationConfig validation
        config = models.CorrelationConfig(
            symbols=['EURUSD', 'GBPUSD', 'USDJPY'],
            timeframe='1H',
            lookback_periods=100,
            min_periods=50,
            method='pearson'
        )
        assert config.symbols == ['EURUSD', 'GBPUSD', 'USDJPY']
        assert config.min_periods == 50
        
        # Test min_periods validation
        with pytest.raises(ValueError, match="min_periods must be positive"):
            models.CorrelationConfig(
                symbols=['EURUSD'], timeframe='1H', lookback_periods=100,
                min_periods=0, method='pearson'
            )
        
        # Test CorrelationPair validation
        pair = models.CorrelationPair(
            symbol1='EURUSD',
            symbol2='GBPUSD',
            correlation=0.75,
            strength=0.75,
            timestamp=timestamp
        )
        assert pair.correlation == 0.75
        assert pair.strength == 0.75
        
        # Test correlation boundary validation
        with pytest.raises(ValueError, match="Correlation must be between -1.0 and 1.0"):
            models.CorrelationPair(
                symbol1='EUR', symbol2='GBP', correlation=1.1,
                strength=0.5, timestamp=timestamp
            )
        
        # Test strength boundary validation
        with pytest.raises(ValueError, match="Strength must be between 0.0 and 1.0"):
            models.CorrelationPair(
                symbol1='EUR', symbol2='GBP', correlation=0.5,
                strength=1.1, timestamp=timestamp
            )        
        # Test CorrelationMatrix validation
        symbols = ['EURUSD', 'GBPUSD', 'USDJPY']
        matrix_data = np.array([
            [1.0, 0.8, -0.3],
            [0.8, 1.0, -0.2],
            [-0.3, -0.2, 1.0]
        ])
        
        matrix = models.CorrelationMatrix(
            symbols=symbols,
            matrix=matrix_data,
            timestamp=timestamp,
            timeframe='1H'
        )
        assert matrix.symbols == symbols
        assert np.array_equal(matrix.matrix, matrix_data)
        
        # Test matrix validation - must be square
        invalid_matrix = np.array([
            [1.0, 0.8],
            [0.8, 1.0],
            [-0.3, -0.2]
        ])
        
        with pytest.raises(ValueError, match="Matrix must be square"):
            models.CorrelationMatrix(
                symbols=symbols, matrix=invalid_matrix,
                timestamp=timestamp, timeframe='1H'
            )
        
        # Test CorrelationAnalysis validation
        correlations = [pair]
        analysis = models.CorrelationAnalysis(
            correlations=correlations,
            matrix=matrix,
            timestamp=timestamp,
            summary='Test analysis'
        )
        
        assert len(analysis.correlations) == 1
        assert analysis.matrix == matrix
        assert analysis.summary == 'Test analysis'
        
        # Test with empty correlations
        empty_analysis = models.CorrelationAnalysis(
            correlations=[],
            matrix=matrix,
            timestamp=timestamp,
            summary='No correlations'
        )
        
        assert len(empty_analysis.correlations) == 0