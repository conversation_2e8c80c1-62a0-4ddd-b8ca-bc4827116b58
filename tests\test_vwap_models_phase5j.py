"""
Phase 5J comprehensive tests to push vwap/models.py to 90%+ coverage.
"""

import pytest
import numpy as np
import pandas as pd
from datetime import datetime
from src.forex_bot.vwap import models

class TestVWAPModelsPhase5J:
    """Phase 5J tests to achieve 90%+ coverage for vwap/models.py."""

    def test_vwap_result_basic_functionality(self):
        """Test VWAPResult class basic functionality."""
        
        # Create test data
        timestamps = np.array([
            pd.Timestamp('2023-12-15 09:00:00'),
            pd.Timestamp('2023-12-15 10:00:00'),
            pd.Timestamp('2023-12-15 11:00:00')
        ])
        vwap_values = np.array([1.1000, 1.1010, 1.1020])
        
        # Test successful creation
        vwap_result = models.VWAPResult(
            timestamps=timestamps,
            vwap_values=vwap_values,
            symbol='EURUSD',
            timeframe=60,
            start_time=pd.Timestamp('2023-12-15 09:00:00'),
            end_time=pd.Timestamp('2023-12-15 11:00:00'),
            anchor_time=pd.Timestamp('2023-12-15 09:00:00'),
            is_anchored=True
        )
        
        # Test basic attributes
        assert vwap_result.symbol == 'EURUSD'
        assert vwap_result.timeframe == 60
        assert vwap_result.start_time == pd.Timestamp('2023-12-15 09:00:00')
        assert vwap_result.end_time == pd.Timestamp('2023-12-15 11:00:00')
        assert vwap_result.anchor_time == pd.Timestamp('2023-12-15 09:00:00')
        assert vwap_result.is_anchored == True
        
        # Test arrays
        np.testing.assert_array_equal(vwap_result.timestamps, timestamps)
        np.testing.assert_array_equal(vwap_result.vwap_values, vwap_values)

    def test_vwap_result_to_dataframe_basic(self):
        """Test VWAPResult to_dataframe method with basic data."""
        
        # Create test data
        timestamps = np.array([
            pd.Timestamp('2023-12-15 09:00:00'),
            pd.Timestamp('2023-12-15 10:00:00'),
            pd.Timestamp('2023-12-15 11:00:00')
        ])
        vwap_values = np.array([1.1000, 1.1010, 1.1020])
        
        # Test with basic data only
        vwap_result = models.VWAPResult(
            timestamps=timestamps,
            vwap_values=vwap_values,
            symbol='GBPUSD',
            timeframe=240
        )
        
        df = vwap_result.to_dataframe()
        
        # Test DataFrame structure
        assert isinstance(df, pd.DataFrame)
        assert len(df) == 3
        assert list(df.columns) == ['timestamp', 'vwap']
        
        # Test DataFrame values
        pd.testing.assert_series_equal(df['timestamp'], pd.Series(timestamps, name='timestamp'))
        np.testing.assert_array_equal(df['vwap'].values, vwap_values)

    def test_vwap_result_to_dataframe_with_bands(self):
        """Test VWAPResult to_dataframe method with all bands."""
        
        # Create test data
        timestamps = np.array([
            pd.Timestamp('2023-12-15 09:00:00'),
            pd.Timestamp('2023-12-15 10:00:00'),
            pd.Timestamp('2023-12-15 11:00:00')
        ])
        vwap_values = np.array([1.1000, 1.1010, 1.1020])
        upper_band_1sd = np.array([1.1005, 1.1015, 1.1025])
        lower_band_1sd = np.array([1.0995, 1.1005, 1.1015])
        upper_band_2sd = np.array([1.1010, 1.1020, 1.1030])
        lower_band_2sd = np.array([1.0990, 1.1000, 1.1010])
        
        # Test with all bands
        vwap_result = models.VWAPResult(
            timestamps=timestamps,
            vwap_values=vwap_values,
            symbol='USDJPY',
            timeframe=60,
            upper_band_1sd=upper_band_1sd,
            lower_band_1sd=lower_band_1sd,
            upper_band_2sd=upper_band_2sd,
            lower_band_2sd=lower_band_2sd
        )
        
        df = vwap_result.to_dataframe()
        
        # Test DataFrame structure with all bands
        assert isinstance(df, pd.DataFrame)
        assert len(df) == 3
        expected_columns = ['timestamp', 'vwap', 'upper_band_1sd', 'lower_band_1sd', 'upper_band_2sd', 'lower_band_2sd']
        assert list(df.columns) == expected_columns
        
        # Test DataFrame values
        pd.testing.assert_series_equal(df['timestamp'], pd.Series(timestamps, name='timestamp'))
        np.testing.assert_array_equal(df['vwap'].values, vwap_values)
        np.testing.assert_array_equal(df['upper_band_1sd'].values, upper_band_1sd)
        np.testing.assert_array_equal(df['lower_band_1sd'].values, lower_band_1sd)
        np.testing.assert_array_equal(df['upper_band_2sd'].values, upper_band_2sd)
        np.testing.assert_array_equal(df['lower_band_2sd'].values, lower_band_2sd)

    def test_vwap_result_to_dataframe_partial_bands(self):
        """Test VWAPResult to_dataframe method with partial bands."""
        
        # Create test data
        timestamps = np.array([
            pd.Timestamp('2023-12-15 09:00:00'),
            pd.Timestamp('2023-12-15 10:00:00')
        ])
        vwap_values = np.array([1.1000, 1.1010])
        upper_band_1sd = np.array([1.1005, 1.1015])
        lower_band_2sd = np.array([1.0990, 1.1000])
        
        # Test with only some bands
        vwap_result = models.VWAPResult(
            timestamps=timestamps,
            vwap_values=vwap_values,
            symbol='AUDUSD',
            timeframe=60,
            upper_band_1sd=upper_band_1sd,
            lower_band_2sd=lower_band_2sd
        )
        
        df = vwap_result.to_dataframe()
        
        # Test DataFrame structure with partial bands
        assert isinstance(df, pd.DataFrame)
        assert len(df) == 2
        expected_columns = ['timestamp', 'vwap', 'upper_band_1sd', 'lower_band_2sd']
        assert list(df.columns) == expected_columns
        
        # Test DataFrame values
        pd.testing.assert_series_equal(df['timestamp'], pd.Series(timestamps, name='timestamp'))
        np.testing.assert_array_equal(df['vwap'].values, vwap_values)
        np.testing.assert_array_equal(df['upper_band_1sd'].values, upper_band_1sd)
        np.testing.assert_array_equal(df['lower_band_2sd'].values, lower_band_2sd)

    def test_vwap_result_to_dataframe_mismatched_bands(self):
        """Test VWAPResult to_dataframe method with mismatched band lengths."""
        
        # Create test data
        timestamps = np.array([
            pd.Timestamp('2023-12-15 09:00:00'),
            pd.Timestamp('2023-12-15 10:00:00'),
            pd.Timestamp('2023-12-15 11:00:00')
        ])
        vwap_values = np.array([1.1000, 1.1010, 1.1020])
        upper_band_1sd = np.array([1.1005, 1.1015])  # Shorter than timestamps
        lower_band_1sd = np.array([1.0995, 1.1005, 1.1015, 1.1025])  # Longer than timestamps
        
        # Test with mismatched band lengths (should be excluded)
        vwap_result = models.VWAPResult(
            timestamps=timestamps,
            vwap_values=vwap_values,
            symbol='USDCAD',
            timeframe=60,
            upper_band_1sd=upper_band_1sd,
            lower_band_1sd=lower_band_1sd
        )
        
        df = vwap_result.to_dataframe()
        
        # Test DataFrame structure - mismatched bands should be excluded
        assert isinstance(df, pd.DataFrame)
        assert len(df) == 3
        assert list(df.columns) == ['timestamp', 'vwap']  # No bands included
        
        # Test DataFrame values
        pd.testing.assert_series_equal(df['timestamp'], pd.Series(timestamps, name='timestamp'))
        np.testing.assert_array_equal(df['vwap'].values, vwap_values)

    def test_vwap_result_optional_fields(self):
        """Test VWAPResult with optional fields."""
        
        # Test with minimal required fields
        timestamps = np.array([pd.Timestamp('2023-12-15 09:00:00')])
        vwap_values = np.array([1.1000])
        
        vwap_result = models.VWAPResult(
            timestamps=timestamps,
            vwap_values=vwap_values,
            symbol='NZDUSD',
            timeframe=1440
        )
        
        # Test default values
        assert vwap_result.start_time is None
        assert vwap_result.end_time is None
        assert vwap_result.anchor_time is None
        assert vwap_result.is_anchored == False
        assert vwap_result.upper_band_1sd is None
        assert vwap_result.lower_band_1sd is None
        assert vwap_result.upper_band_2sd is None
        assert vwap_result.lower_band_2sd is None

    def test_vwap_crossover_bullish_scenario(self):
        """Test VWAPCrossover with bullish scenario."""
        
        # Test bullish crossover (price crosses above VWAP)
        crossover = models.VWAPCrossover(
            timestamp=pd.Timestamp('2023-12-15 10:30:00'),
            price=1.1025,
            vwap=1.1020,
            direction='above',
            strength=0.75
        )
        
        # Test basic attributes
        assert crossover.timestamp == pd.Timestamp('2023-12-15 10:30:00')
        assert crossover.price == 1.1025
        assert crossover.vwap == 1.1020
        assert crossover.direction == 'above'
        assert crossover.strength == 0.75
        
        # Test properties
        assert crossover.is_bullish == True
        assert crossover.is_bearish == False

    def test_vwap_crossover_bearish_scenario(self):
        """Test VWAPCrossover with bearish scenario."""
        
        # Test bearish crossover (price crosses below VWAP)
        crossover = models.VWAPCrossover(
            timestamp=pd.Timestamp('2023-12-15 14:15:00'),
            price=1.1015,
            vwap=1.1020,
            direction='below',
            strength=0.85
        )
        
        # Test basic attributes
        assert crossover.timestamp == pd.Timestamp('2023-12-15 14:15:00')
        assert crossover.price == 1.1015
        assert crossover.vwap == 1.1020
        assert crossover.direction == 'below'
        assert crossover.strength == 0.85
        
        # Test properties
        assert crossover.is_bullish == False
        assert crossover.is_bearish == True

    def test_vwap_crossover_edge_cases(self):
        """Test VWAPCrossover with edge cases."""
        
        # Test with minimum strength
        crossover_min = models.VWAPCrossover(
            timestamp=pd.Timestamp('2023-12-15 12:00:00'),
            price=1.1020,
            vwap=1.1020,
            direction='above',
            strength=0.0
        )
        
        assert crossover_min.strength == 0.0
        assert crossover_min.is_bullish == True
        assert crossover_min.is_bearish == False
        
        # Test with maximum strength
        crossover_max = models.VWAPCrossover(
            timestamp=pd.Timestamp('2023-12-15 16:45:00'),
            price=1.1000,
            vwap=1.1030,
            direction='below',
            strength=1.0
        )
        
        assert crossover_max.strength == 1.0
        assert crossover_max.is_bullish == False
        assert crossover_max.is_bearish == True

    def test_vwap_crossover_different_directions(self):
        """Test VWAPCrossover with different direction values."""
        
        # Test various direction strings
        directions = ['above', 'below']
        
        for direction in directions:
            crossover = models.VWAPCrossover(
                timestamp=pd.Timestamp('2023-12-15 13:30:00'),
                price=1.1025,
                vwap=1.1020,
                direction=direction,
                strength=0.6
            )
            
            assert crossover.direction == direction
            
            if direction == 'above':
                assert crossover.is_bullish == True
                assert crossover.is_bearish == False
            elif direction == 'below':
                assert crossover.is_bullish == False
                assert crossover.is_bearish == True