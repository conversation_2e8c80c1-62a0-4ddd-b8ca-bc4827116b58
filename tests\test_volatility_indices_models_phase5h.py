"""
Phase 5H comprehensive tests to push volatility_indices/models.py to 90%+ coverage.
"""

import pytest
from datetime import date
from src.forex_bot.volatility_indices import models

class TestVolatilityIndicesModelsPhase5H:
    """Phase 5H tests to achieve 90%+ coverage for volatility_indices/models.py."""

    def test_volatility_data_basic_functionality(self):
        """Test VolatilityData class basic functionality."""
        
        # Test successful creation with change calculation
        vol_data = models.VolatilityData(
            index_name='VIX',
            date=date(2023, 12, 15),
            value=18.5,
            previous_value=16.2
        )
        
        # Test basic attributes
        assert vol_data.index_name == 'VIX'
        assert vol_data.date == date(2023, 12, 15)
        assert vol_data.value == 18.5
        assert vol_data.previous_value == 16.2
        
        # Test post_init calculations
        assert abs(vol_data.change - 2.3) < 1e-10  # 18.5 - 16.2
        assert abs(vol_data.change_percent - 14.197530864197531) < 1e-10  # (2.3 / 16.2) * 100

    def test_volatility_data_edge_cases(self):
        """Test VolatilityData with edge cases."""
        
        # Test without previous value
        vol_no_previous = models.VolatilityData(
            index_name='MOVE',
            date=date(2023, 12, 15),
            value=120.5
        )
        
        assert vol_no_previous.previous_value is None
        assert vol_no_previous.change is None
        assert vol_no_previous.change_percent is None
        
        # Test with zero previous value (division by zero protection)
        vol_zero_previous = models.VolatilityData(
            index_name='VIX',
            date=date(2023, 12, 15),
            value=15.0,
            previous_value=0.0
        )
        
        assert vol_zero_previous.change == 15.0
        assert vol_zero_previous.change_percent is None  # Should not calculate when previous_value is 0
        
        # Test with pre-calculated values (should not override)
        vol_precalc = models.VolatilityData(
            index_name='VIX',
            date=date(2023, 12, 15),
            value=20.0,
            previous_value=18.0,
            change=1.5,  # Pre-calculated, different from actual
            change_percent=8.0  # Pre-calculated, different from actual
        )
        
        assert vol_precalc.change == 1.5  # Should keep pre-calculated value
        assert vol_precalc.change_percent == 8.0  # Should keep pre-calculated value    def test_volatility_trend_basic_functionality(self):
        """Test VolatilityTrend class basic functionality."""
        
        # Test successful creation
        vol_trend = models.VolatilityTrend(
            index_name='VIX',
            latest_date=date(2023, 12, 15),
            latest_value=22.5,
            trend_direction='up',
            trend_strength=0.75,
            values_5d=[18.2, 19.1, 20.5, 21.8, 22.5],
            values_20d=[15.0, 15.5, 16.2, 17.1, 17.8, 18.2, 19.1, 20.5, 21.8, 22.5, 21.9, 22.1, 22.3, 22.5, 22.2, 22.4, 22.6, 22.5, 22.3, 22.5],
            values_60d=[12.0] * 50 + [18.2, 19.1, 20.5, 21.8, 22.5, 21.9, 22.1, 22.3, 22.5, 22.2],
            avg_5d=20.42,
            avg_20d=20.15,
            avg_60d=18.85,
            percentile_1y=85.5,
            percentile_5y=78.2,
            is_rising=True,
            is_falling=False,
            is_above_5d_avg=True,
            is_above_20d_avg=True,
            is_above_60d_avg=True,
            is_extreme_high=False,
            is_extreme_low=False,
            is_elevated=True,
            is_subdued=False
        )
        
        # Test basic attributes
        assert vol_trend.index_name == 'VIX'
        assert vol_trend.latest_date == date(2023, 12, 15)
        assert vol_trend.latest_value == 22.5
        assert vol_trend.trend_direction == 'up'
        assert vol_trend.trend_strength == 0.75
        assert vol_trend.is_rising == True
        assert vol_trend.is_falling == False
        assert vol_trend.is_elevated == True

    def test_volatility_trend_regime_properties(self):
        """Test VolatilityTrend regime properties."""
        
        # Test Extreme High regime
        trend_extreme_high = models.VolatilityTrend(
            index_name='VIX',
            latest_date=date(2023, 12, 15),
            latest_value=45.0,
            trend_direction='up',
            trend_strength=0.9,
            values_5d=[40.0, 41.5, 43.0, 44.2, 45.0],
            values_20d=[35.0] * 15 + [40.0, 41.5, 43.0, 44.2, 45.0],
            values_60d=[25.0] * 55 + [40.0, 41.5, 43.0, 44.2, 45.0],
            avg_5d=42.74,
            avg_20d=38.85,
            avg_60d=30.42,
            percentile_1y=98.5,
            is_extreme_high=True,
            is_elevated=False,
            is_extreme_low=False,
            is_subdued=False
        )
        
        assert trend_extreme_high.volatility_regime == "Extreme High"
        assert trend_extreme_high.market_sentiment == "Fear"
        
        # Test Elevated regime
        trend_elevated = models.VolatilityTrend(
            index_name='VIX',
            latest_date=date(2023, 12, 15),
            latest_value=25.0,
            trend_direction='up',
            trend_strength=0.6,
            values_5d=[22.0, 23.0, 24.0, 24.5, 25.0],
            values_20d=[20.0] * 15 + [22.0, 23.0, 24.0, 24.5, 25.0],
            values_60d=[18.0] * 55 + [22.0, 23.0, 24.0, 24.5, 25.0],
            avg_5d=23.7,
            avg_20d=21.85,
            avg_60d=19.42,
            percentile_1y=75.0,
            is_extreme_high=False,
            is_elevated=True,
            is_extreme_low=False,
            is_subdued=False
        )
        
        assert trend_elevated.volatility_regime == "Elevated"
        assert trend_elevated.market_sentiment == "Caution"

    def test_volatility_trend_low_regimes(self):
        """Test VolatilityTrend low volatility regimes."""
        
        # Test Extreme Low regime
        trend_extreme_low = models.VolatilityTrend(
            index_name='VIX',
            latest_date=date(2023, 12, 15),
            latest_value=9.5,
            trend_direction='down',
            trend_strength=0.8,
            values_5d=[11.0, 10.5, 10.0, 9.8, 9.5],
            values_20d=[15.0] * 15 + [11.0, 10.5, 10.0, 9.8, 9.5],
            values_60d=[18.0] * 55 + [11.0, 10.5, 10.0, 9.8, 9.5],
            avg_5d=10.16,
            avg_20d=13.85,
            avg_60d=16.42,
            percentile_1y=5.0,
            is_extreme_high=False,
            is_elevated=False,
            is_extreme_low=True,
            is_subdued=False
        )
        
        assert trend_extreme_low.volatility_regime == "Extreme Low"
        assert trend_extreme_low.market_sentiment == "Extreme Complacency"
        
        # Test Subdued regime
        trend_subdued = models.VolatilityTrend(
            index_name='VIX',
            latest_date=date(2023, 12, 15),
            latest_value=12.5,
            trend_direction='flat',
            trend_strength=0.2,
            values_5d=[12.2, 12.4, 12.3, 12.6, 12.5],
            values_20d=[14.0] * 15 + [12.2, 12.4, 12.3, 12.6, 12.5],
            values_60d=[16.0] * 55 + [12.2, 12.4, 12.3, 12.6, 12.5],
            avg_5d=12.4,
            avg_20d=13.65,
            avg_60d=15.25,
            percentile_1y=25.0,
            is_extreme_high=False,
            is_elevated=False,
            is_extreme_low=False,
            is_subdued=True
        )
        
        assert trend_subdued.volatility_regime == "Subdued"
        assert trend_subdued.market_sentiment == "Complacency"
        
        # Test Normal regime
        trend_normal = models.VolatilityTrend(
            index_name='VIX',
            latest_date=date(2023, 12, 15),
            latest_value=16.5,
            trend_direction='flat',
            trend_strength=0.3,
            values_5d=[16.2, 16.4, 16.3, 16.6, 16.5],
            values_20d=[16.0] * 15 + [16.2, 16.4, 16.3, 16.6, 16.5],
            values_60d=[16.0] * 55 + [16.2, 16.4, 16.3, 16.6, 16.5],
            avg_5d=16.4,
            avg_20d=16.15,
            avg_60d=16.08,
            percentile_1y=50.0,
            is_extreme_high=False,
            is_elevated=False,
            is_extreme_low=False,
            is_subdued=False
        )
        
        assert trend_normal.volatility_regime == "Normal"
        assert trend_normal.market_sentiment == "Neutral"

    def test_volatility_regime_basic_functionality(self):
        """Test VolatilityRegime class basic functionality."""
        
        # Test successful creation with risk-on scenario
        vol_regime = models.VolatilityRegime(
            date=date(2023, 12, 15),
            vix_regime='Subdued',
            move_regime='Normal',
            overall_regime='Low Volatility',
            currency_impact={
                'USD': 'negative',
                'EUR': 'positive',
                'JPY': 'negative',
                'GBP': 'positive',
                'AUD': 'positive',
                'CAD': 'neutral'
            },
            risk_on_assets=True,
            risk_off_assets=False,
            volatility_trend='decreasing'
        )
        
        # Test basic attributes
        assert vol_regime.date == date(2023, 12, 15)
        assert vol_regime.vix_regime == 'Subdued'
        assert vol_regime.move_regime == 'Normal'
        assert vol_regime.overall_regime == 'Low Volatility'
        assert vol_regime.risk_on_assets == True
        assert vol_regime.risk_off_assets == False
        assert vol_regime.volatility_trend == 'decreasing'
        
        # Test trading bias property
        assert vol_regime.trading_bias == "Risk-On"

    def test_volatility_regime_currency_bias(self):
        """Test VolatilityRegime currency bias methods."""
        
        vol_regime = models.VolatilityRegime(
            date=date(2023, 12, 15),
            vix_regime='Elevated',
            move_regime='Elevated',
            overall_regime='High Volatility',
            currency_impact={
                'USD': 'positive',
                'EUR': 'negative',
                'JPY': 'positive',
                'GBP': 'negative',
                'AUD': 'negative',
                'CAD': 'neutral'
            },
            risk_on_assets=False,
            risk_off_assets=True,
            volatility_trend='increasing'
        )
        
        # Test get_currency_bias method
        assert vol_regime.get_currency_bias('USD') == 'BUY'
        assert vol_regime.get_currency_bias('EUR') == 'SELL'
        assert vol_regime.get_currency_bias('CAD') == 'NEUTRAL'
        assert vol_regime.get_currency_bias('CHF') is None  # Not in currency_impact
        
        # Test trading bias property for risk-off scenario
        assert vol_regime.trading_bias == "Risk-Off"

    def test_volatility_regime_pair_bias(self):
        """Test VolatilityRegime pair bias method."""
        
        vol_regime = models.VolatilityRegime(
            date=date(2023, 12, 15),
            vix_regime='Normal',
            move_regime='Normal',
            overall_regime='Normal Volatility',
            currency_impact={
                'USD': 'positive',
                'EUR': 'negative',
                'JPY': 'positive',
                'GBP': 'negative',
                'AUD': 'neutral',
                'CAD': 'neutral'
            },
            risk_on_assets=False,
            risk_off_assets=False,
            volatility_trend='stable'
        )
        
        # Test strong signals (opposite impacts)
        assert vol_regime.get_pair_bias('USD', 'EUR') == 'BUY'  # positive vs negative
        assert vol_regime.get_pair_bias('EUR', 'JPY') == 'SELL'  # negative vs positive
        
        # Test moderate signals
        assert vol_regime.get_pair_bias('USD', 'AUD') == 'BUY'  # positive vs neutral
        assert vol_regime.get_pair_bias('EUR', 'CAD') == 'SELL'  # negative vs neutral
        assert vol_regime.get_pair_bias('AUD', 'JPY') == 'SELL'  # neutral vs positive
        assert vol_regime.get_pair_bias('CAD', 'EUR') == 'BUY'  # neutral vs negative
        
        # Test neutral signals
        assert vol_regime.get_pair_bias('USD', 'JPY') == 'SELL'  # positive vs positive (logic returns SELL)
        assert vol_regime.get_pair_bias('EUR', 'GBP') == 'BUY'  # negative vs negative (logic returns BUY)
        assert vol_regime.get_pair_bias('AUD', 'CAD') == 'NEUTRAL'  # neutral vs neutral
        
        # Test missing currencies
        assert vol_regime.get_pair_bias('USD', 'CHF') is None  # CHF not in currency_impact
        assert vol_regime.get_pair_bias('NOK', 'EUR') is None  # NOK not in currency_impact

    def test_volatility_regime_neutral_bias(self):
        """Test VolatilityRegime with neutral trading bias."""
        
        vol_regime_neutral = models.VolatilityRegime(
            date=date(2023, 12, 15),
            vix_regime='Normal',
            move_regime='Normal',
            overall_regime='Normal Volatility',
            currency_impact={
                'USD': 'neutral',
                'EUR': 'neutral',
                'JPY': 'neutral'
            },
            risk_on_assets=False,
            risk_off_assets=False,
            volatility_trend='stable'
        )
        
        # Test neutral trading bias
        assert vol_regime_neutral.trading_bias == "Neutral"