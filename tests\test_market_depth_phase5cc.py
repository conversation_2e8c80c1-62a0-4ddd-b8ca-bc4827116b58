"""
Phase 5CC: Test for market_depth_visualizer/models.py to push from 69% to 90%+ coverage.
Targeting the remaining missing lines: import fallbacks, validators, and pydantic fallback classes.
"""

import pytest
from datetime import datetime, timezone
from unittest.mock import patch
from src.forex_bot.market_depth_visualizer.models import (
    VisualizationType, ColorScheme, MarketDepthSnapshot, MarketDepthVisualization,
    MarketDepthDashboard, DashboardSettings
)


class TestMarketDepthPhase5CC:
    """Test class to push market_depth_visualizer/models.py to 90%+ coverage"""

    def test_import_fallbacks_comprehensive(self):
        """Test comprehensive import fallback behavior"""
        
        # Test numpy import fallback (lines 14-15)
        with patch.dict('sys.modules', {'numpy': None}):
            # Force module reload to trigger import error handling
            import importlib
            import src.forex_bot.market_depth_visualizer.models as models_module
            importlib.reload(models_module)
            
            # Verify NUMPY_AVAILABLE is False
            assert not hasattr(models_module, 'np') or models_module.NUMPY_AVAILABLE is False

        # Test pandas import fallback (lines 20-29)  
        with patch.dict('sys.modules', {'pandas': None}):
            import importlib
            import src.forex_bot.market_depth_visualizer.models as models_module
            importlib.reload(models_module)
            
            # Verify PANDAS_AVAILABLE is False
            assert not hasattr(models_module, 'pd') or models_module.PANDAS_AVAILABLE is False

    def test_validator_edge_cases(self):
        """Test validator edge cases"""
        
        now = datetime.now(timezone.utc)
        
        # Test bid volumes validator edge case (line 165)
        with pytest.raises(ValueError, match="Bid volumes must have the same length as bid prices"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340, 1.2339, 1.2338, 1.2337],
                bid_volumes=[1000.0, 1500.0],  # Different length
                ask_prices=[1.2341],
                ask_volumes=[800.0]
            )
        
        # Test ask volumes validator edge case (line 177)
        with pytest.raises(ValueError, match="Ask volumes must have the same length as ask prices"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340],
                bid_volumes=[1000.0],
                ask_prices=[1.2341, 1.2342, 1.2343, 1.2344],
                ask_volumes=[800.0, 1200.0]  # Different length
            )    def test_property_edge_cases(self):
        """Test property edge cases"""
        
        now = datetime.now(timezone.utc)
        
        # Test property calculations with edge cases (lines 194, 201)
        snapshot = MarketDepthSnapshot(
            symbol="GBPUSD",
            timestamp=now,
            bid_prices=[1.3000],
            bid_volumes=[1000.0],
            ask_prices=[1.3001],
            ask_volumes=[800.0]
        )
        
        # Access all properties to trigger coverage
        assert snapshot.best_bid == 1.3000  # line 194
        assert snapshot.best_ask == 1.3001  # line 201
        assert snapshot.spread == 0.0001
        assert snapshot.mid_price == 1.30005
        assert snapshot.total_bid_volume == 1000.0
        assert snapshot.total_ask_volume == 800.0
        
        # Test imbalance ratio calculation (line 225)
        expected_imbalance = (1000.0 - 800.0) / (1000.0 + 800.0)
        assert abs(snapshot.imbalance_ratio - expected_imbalance) < 1e-10

    def test_visualization_validators(self):
        """Test MarketDepthVisualization and MarketDepthDashboard validators"""
        
        now = datetime.now(timezone.utc)
        
        # Test MarketDepthVisualization validator
        viz = MarketDepthVisualization(
            symbol="EURUSD",
            timestamp=now,
            visualization_type=VisualizationType.DEPTH_CHART,
            image_data="base64_encoded_data",
            settings={"price_levels": 20}
        )
        assert viz.image_data == "base64_encoded_data"
        
        # Test MarketDepthDashboard validator - empty visualizations should fail
        with pytest.raises(ValueError, match="Visualizations cannot be empty"):
            MarketDepthDashboard(
                symbol="EURUSD",
                timestamp=now,
                visualizations={},  # Empty dict should trigger validator
                settings=DashboardSettings()
            )

    def test_pydantic_fallback_classes(self):
        """Test pydantic fallback classes (lines 276-371)"""
        
        # Test with pydantic unavailable to trigger fallback classes
        with patch('src.forex_bot.market_depth_visualizer.models.PYDANTIC_AVAILABLE', False):
            # Force module reload to use fallback classes
            import importlib
            import src.forex_bot.market_depth_visualizer.models as models_module
            importlib.reload(models_module)
            
            # Test fallback DepthChartSettings
            depth_settings = models_module.DepthChartSettings(
                price_levels=15,
                show_imbalances=False,
                color_scheme=models_module.ColorScheme.DARK
            )
            assert depth_settings.price_levels == 15
            assert depth_settings.show_imbalances is False
            
            # Test dict method
            settings_dict = depth_settings.dict()
            assert settings_dict["price_levels"] == 15
            assert "_private_attr" not in settings_dict
            
            # Test fallback HeatmapSettings
            heatmap_settings = models_module.HeatmapSettings(
                time_window=120,
                normalization="log"
            )
            assert heatmap_settings.time_window == 120
            assert heatmap_settings.normalization == "log"
            
            # Test fallback TimeAndSalesSettings
            time_sales_settings = models_module.TimeAndSalesSettings(
                max_entries=200,
                large_trade_threshold=5.0
            )
            assert time_sales_settings.max_entries == 200
            assert time_sales_settings.large_trade_threshold == 5.0
            
            # Test fallback LiquidityMapSettings
            liquidity_settings = models_module.LiquidityMapSettings(
                price_levels=25,
                normalization="log"
            )
            assert liquidity_settings.price_levels == 25
            assert liquidity_settings.normalization == "log"
            
            # Test fallback OrderFlowFootprintSettings
            footprint_settings = models_module.OrderFlowFootprintSettings(
                time_window=90,
                delta_type="trades"
            )
            assert footprint_settings.time_window == 90
            assert footprint_settings.delta_type == "trades"