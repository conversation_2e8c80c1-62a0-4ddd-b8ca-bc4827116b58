"""
Phase 5EE: Simple test for market_depth_visualizer/models.py to push from 71% to 90%+ coverage.
"""

import pytest
from datetime import datetime, timezone
from unittest.mock import patch
from src.forex_bot.market_depth_visualizer.models import MarketDepthSnapshot


class TestMarketDepthPhase5EESimple:
    """Simple test to push market_depth_visualizer/models.py to 90%+ coverage"""

    def test_property_edge_cases(self):
        """Test property edge cases for empty lists"""
        
        now = datetime.now(timezone.utc)
        
        # Test empty bid_prices edge case (line 194)
        with pytest.raises(ValueError, match="Bid prices cannot be empty"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[],  # Empty list
                bid_volumes=[],
                ask_prices=[1.2341],
                ask_volumes=[800.0]
            )
        
        # Test empty ask_prices edge case (line 201)
        with pytest.raises(ValueError, match="Ask prices cannot be empty"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340],
                bid_volumes=[1000.0],
                ask_prices=[],  # Empty list
                ask_volumes=[]
            )

    def test_imbalance_ratio_calculation(self):
        """Test imbalance ratio calculation edge case"""
        
        now = datetime.now(timezone.utc)
        
        # Test specific imbalance ratio calculation
        snapshot = MarketDepthSnapshot(
            symbol="GBPUSD",
            timestamp=now,
            bid_prices=[1.3000, 1.2999],
            bid_volumes=[1000.0, 2000.0],  # total: 3000
            ask_prices=[1.3001, 1.3002],
            ask_volumes=[1500.0, 1500.0]   # total: 3000
        )
        
        # This should trigger the imbalance ratio calculation
        # (3000 - 3000) / (3000 + 3000) = 0 / 6000 = 0
        assert snapshot.imbalance_ratio == 0.0