#!/usr/bin/env python3
"""
QA Wolf Enhancement: Real-Time Web Dashboard

This module provides a web-based dashboard for monitoring the Forex Trading Bot
with real-time charts, metrics, and alerts without interfering with trading operations.

SAFETY LEVEL: MAXIMUM - Web interface only, zero trading logic interaction
"""

import json
import sqlite3
import threading
import time
from datetime import datetime, timezone, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging

# Web framework imports
try:
    from flask import Flask, render_template, jsonify, request
    from flask_socketio import SocketIO, emit
    FLASK_AVAILABLE = True
except ImportError:
    FLASK_AVAILABLE = False
    print("Flask not available. Install with: pip install flask flask-socketio")

# Import our monitoring overlay
try:
    from .monitoring_overlay import QAWolfMonitoringOverlay
    MONITORING_AVAILABLE = True
except ImportError:
    MONITORING_AVAILABLE = False
    print("Monitoring overlay not available")

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class QAWolfWebDashboard:
    """
    Web-based dashboard for real-time monitoring of the Forex Trading Bot.
    
    SAFETY GUARANTEE: This dashboard only displays monitoring data,
    never modifies trading logic or sends commands to the trading bot.
    """
    
    def __init__(self, project_root: str, port: int = 5000):
        if not FLASK_AVAILABLE:
            raise ImportError("Flask is required for web dashboard")
        
        self.project_root = Path(project_root)
        self.port = port
        self.monitoring_overlay = None
        
        # Initialize Flask app
        self.app = Flask(__name__, 
                        template_folder=str(self.project_root / "qa_wolf_enhancements" / "templates"),
                        static_folder=str(self.project_root / "qa_wolf_enhancements" / "static"))
        self.app.config['SECRET_KEY'] = 'qa_wolf_dashboard_secret_key'
        
        # Initialize SocketIO for real-time updates
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        
        # Database path
        self.db_path = self.project_root / "qa_wolf_enhancements" / "monitoring.db"
        
        # Setup routes
        self._setup_routes()
        self._setup_socketio_events()
        
        # Background thread for real-time updates
        self.update_thread = None
        self.is_running = False
        
        logger.info("QA Wolf Web Dashboard initialized - SAFE MODE")
    
    def _setup_routes(self):
        """Setup Flask routes for the dashboard."""
        
        @self.app.route('/')
        def dashboard():
            """Main dashboard page."""
            return render_template('dashboard.html')
        
        @self.app.route('/api/status')
        def api_status():
            """API endpoint for current system status."""
            try:
                status = self._get_current_status()
                return jsonify(status)
            except Exception as e:
                logger.error(f"Error getting status: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/metrics')
        def api_metrics():
            """API endpoint for recent metrics."""
            try:
                hours = request.args.get('hours', 1, type=int)
                metrics = self._get_recent_metrics(hours)
                return jsonify(metrics)
            except Exception as e:
                logger.error(f"Error getting metrics: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/health')
        def api_health():
            """API endpoint for system health data."""
            try:
                hours = request.args.get('hours', 1, type=int)
                health_data = self._get_recent_health_data(hours)
                return jsonify(health_data)
            except Exception as e:
                logger.error(f"Error getting health data: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/alerts')
        def api_alerts():
            """API endpoint for recent alerts."""
            try:
                alerts = self._get_recent_alerts()
                return jsonify(alerts)
            except Exception as e:
                logger.error(f"Error getting alerts: {e}")
                return jsonify({'error': str(e)}), 500
        
        @self.app.route('/api/trading_status')
        def api_trading_status():
            """API endpoint for trading bot status."""
            try:
                trading_status = self._get_trading_bot_status()
                return jsonify(trading_status)
            except Exception as e:
                logger.error(f"Error getting trading status: {e}")
                return jsonify({'error': str(e)}), 500
    
    def _setup_socketio_events(self):
        """Setup SocketIO events for real-time communication."""
        
        @self.socketio.on('connect')
        def handle_connect():
            """Handle client connection."""
            logger.info("Dashboard client connected")
            emit('status', {'message': 'Connected to QA Wolf Dashboard'})
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """Handle client disconnection."""
            logger.info("Dashboard client disconnected")
        
        @self.socketio.on('request_update')
        def handle_update_request():
            """Handle client request for data update."""
            try:
                status = self._get_current_status()
                emit('status_update', status)
            except Exception as e:
                logger.error(f"Error handling update request: {e}")
                emit('error', {'message': str(e)})
    
    def _get_current_status(self) -> Dict[str, Any]:
        """Get current system status."""
        try:
            # Get latest health data
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute('''
                    SELECT * FROM system_health 
                    ORDER BY timestamp DESC 
                    LIMIT 1
                ''')
                row = cursor.fetchone()
                
                if row:
                    columns = [desc[0] for desc in cursor.description]
                    health_data = dict(zip(columns, row))
                else:
                    health_data = {}
            
            # Get monitoring overlay status
            monitoring_status = {}
            if self.monitoring_overlay:
                monitoring_status = self.monitoring_overlay.get_current_status()
            
            return {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'dashboard_status': 'ACTIVE',
                'system_health': health_data,
                'monitoring_status': monitoring_status,
                'safety_status': {
                    'trading_interference': False,
                    'read_only_mode': True,
                    'zero_trading_impact': True
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting current status: {e}")
            return {'error': str(e)}
    
    def _get_recent_metrics(self, hours: int = 1) -> List[Dict[str, Any]]:
        """Get recent performance metrics."""
        try:
            since_time = datetime.now(timezone.utc) - timedelta(hours=hours)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute('''
                    SELECT * FROM performance_metrics 
                    WHERE timestamp > ? 
                    ORDER BY timestamp DESC
                ''', (since_time.isoformat(),))
                
                columns = [desc[0] for desc in cursor.description]
                metrics = [dict(zip(columns, row)) for row in cursor.fetchall()]
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error getting recent metrics: {e}")
            return []
    
    def _get_recent_health_data(self, hours: int = 1) -> List[Dict[str, Any]]:
        """Get recent system health data."""
        try:
            since_time = datetime.now(timezone.utc) - timedelta(hours=hours)
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute('''
                    SELECT * FROM system_health 
                    WHERE timestamp > ? 
                    ORDER BY timestamp ASC
                ''', (since_time.isoformat(),))
                
                columns = [desc[0] for desc in cursor.description]
                health_data = [dict(zip(columns, row)) for row in cursor.fetchall()]
            
            return health_data
            
        except Exception as e:
            logger.error(f"Error getting recent health data: {e}")
            return []
    
    def _get_recent_alerts(self) -> List[Dict[str, Any]]:
        """Get recent alerts (placeholder - would be implemented with alert storage)."""
        # For now, return sample alerts
        return [
            {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'level': 'INFO',
                'message': 'QA Wolf Dashboard active',
                'category': 'system'
            }
        ]
    
    def _get_trading_bot_status(self) -> Dict[str, Any]:
        """Get trading bot status information."""
        try:
            # Check log files for trading bot activity
            log_file = self.project_root / "trading_bot.log"
            perf_log_file = self.project_root / "trading_performance.log"
            
            status = {
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'log_files': {
                    'trading_log': {
                        'exists': log_file.exists(),
                        'size_mb': log_file.stat().st_size / (1024*1024) if log_file.exists() else 0,
                        'last_modified': datetime.fromtimestamp(log_file.stat().st_mtime, tz=timezone.utc).isoformat() if log_file.exists() else None
                    },
                    'performance_log': {
                        'exists': perf_log_file.exists(),
                        'size_mb': perf_log_file.stat().st_size / (1024*1024) if perf_log_file.exists() else 0,
                        'last_modified': datetime.fromtimestamp(perf_log_file.stat().st_mtime, tz=timezone.utc).isoformat() if perf_log_file.exists() else None
                    }
                }
            }
            
            # Determine bot status based on log activity
            if log_file.exists():
                last_modified = datetime.fromtimestamp(log_file.stat().st_mtime, tz=timezone.utc)
                minutes_since_update = (datetime.now(timezone.utc) - last_modified).total_seconds() / 60
                
                if minutes_since_update < 5:
                    status['bot_status'] = 'ACTIVE'
                elif minutes_since_update < 30:
                    status['bot_status'] = 'IDLE'
                else:
                    status['bot_status'] = 'INACTIVE'
            else:
                status['bot_status'] = 'NO_LOGS'
            
            return status
            
        except Exception as e:
            logger.error(f"Error getting trading bot status: {e}")
            return {'error': str(e)}
    
    def start_dashboard(self, monitoring_overlay: Optional[QAWolfMonitoringOverlay] = None):
        """Start the web dashboard."""
        
        self.monitoring_overlay = monitoring_overlay
        self.is_running = True
        
        # Start background update thread
        self.update_thread = threading.Thread(target=self._background_updates, daemon=True)
        self.update_thread.start()
        
        logger.info(f"🚀 QA Wolf Web Dashboard starting on port {self.port}")
        logger.info("🛡️ SAFETY: Read-only dashboard, zero trading impact")
        
        # Start Flask-SocketIO server
        self.socketio.run(self.app, host='0.0.0.0', port=self.port, debug=False)
    
    def stop_dashboard(self):
        """Stop the web dashboard."""
        self.is_running = False
        if self.update_thread:
            self.update_thread.join(timeout=5)
        logger.info("⏹️ QA Wolf Web Dashboard stopped")
    
    def _background_updates(self):
        """Background thread for sending real-time updates to connected clients."""
        logger.info("Background update thread started")
        
        while self.is_running:
            try:
                # Get current status
                status = self._get_current_status()
                
                # Emit to all connected clients
                self.socketio.emit('status_update', status)
                
                # Sleep for 30 seconds before next update
                time.sleep(30)
                
            except Exception as e:
                logger.error(f"Error in background updates: {e}")
                time.sleep(60)  # Wait longer on error


def create_dashboard_templates():
    """Create HTML templates for the dashboard."""
    
    templates_dir = Path("qa_wolf_enhancements/templates")
    templates_dir.mkdir(exist_ok=True)
    
    # Main dashboard template
    dashboard_html = '''<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QA Wolf - Forex Bot Dashboard</title>
    <script src="https://cdn.socket.io/4.0.0/socket.io.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background: #f5f5f5; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .status-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
        .status-card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .status-value { font-size: 2em; font-weight: bold; color: #27ae60; }
        .status-label { color: #7f8c8d; margin-bottom: 10px; }
        .alert { padding: 10px; margin: 10px 0; border-radius: 4px; }
        .alert-info { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .alert-warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .alert-danger { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .chart-container { height: 300px; margin: 20px 0; }
        .safety-indicator { background: #27ae60; color: white; padding: 5px 10px; border-radius: 20px; font-size: 0.8em; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 QA Wolf - Forex Trading Bot Dashboard</h1>
        <p>Real-time monitoring with <span class="safety-indicator">🛡️ ZERO TRADING IMPACT</span></p>
        <p id="last-update">Last Update: Connecting...</p>
    </div>
    
    <div class="status-grid">
        <div class="status-card">
            <div class="status-label">Trading Bot Status</div>
            <div class="status-value" id="bot-status">CHECKING...</div>
        </div>
        
        <div class="status-card">
            <div class="status-label">CPU Usage</div>
            <div class="status-value" id="cpu-usage">--%</div>
        </div>
        
        <div class="status-card">
            <div class="status-label">Memory Usage</div>
            <div class="status-value" id="memory-usage">--%</div>
        </div>
        
        <div class="status-card">
            <div class="status-label">System Health</div>
            <div class="status-value" id="system-health">MONITORING</div>
        </div>
    </div>
    
    <div class="status-card">
        <h3>System Performance Chart</h3>
        <div class="chart-container">
            <canvas id="performance-chart"></canvas>
        </div>
    </div>
    
    <div class="status-card">
        <h3>Recent Alerts</h3>
        <div id="alerts-container">
            <div class="alert alert-info">Dashboard initialized - monitoring active</div>
        </div>
    </div>

    <script>
        // Initialize Socket.IO connection
        const socket = io();
        
        // Chart setup
        const ctx = document.getElementById('performance-chart').getContext('2d');
        const chart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: 'CPU %',
                    data: [],
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1
                }, {
                    label: 'Memory %',
                    data: [],
                    borderColor: 'rgb(255, 99, 132)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        max: 100
                    }
                }
            }
        });
        
        // Socket event handlers
        socket.on('connect', function() {
            console.log('Connected to dashboard');
            document.getElementById('last-update').textContent = 'Last Update: Connected';
        });
        
        socket.on('status_update', function(data) {
            updateDashboard(data);
        });
        
        function updateDashboard(data) {
            const now = new Date().toLocaleTimeString();
            document.getElementById('last-update').textContent = `Last Update: ${now}`;
            
            if (data.system_health) {
                const health = data.system_health;
                document.getElementById('cpu-usage').textContent = `${health.cpu_percent?.toFixed(1) || '--'}%`;
                document.getElementById('memory-usage').textContent = `${health.memory_percent?.toFixed(1) || '--'}%`;
                document.getElementById('bot-status').textContent = health.trading_bot_status || 'UNKNOWN';
                
                // Update chart
                chart.data.labels.push(now);
                chart.data.datasets[0].data.push(health.cpu_percent || 0);
                chart.data.datasets[1].data.push(health.memory_percent || 0);
                
                // Keep only last 20 data points
                if (chart.data.labels.length > 20) {
                    chart.data.labels.shift();
                    chart.data.datasets[0].data.shift();
                    chart.data.datasets[1].data.shift();
                }
                
                chart.update();
            }
        }
        
        // Request initial update
        socket.emit('request_update');
        
        // Auto-refresh every 30 seconds
        setInterval(() => {
            socket.emit('request_update');
        }, 30000);
    </script>
</body>
</html>'''
    
    with open(templates_dir / "dashboard.html", "w") as f:
        f.write(dashboard_html)
    
    logger.info("Dashboard templates created")


def main():
    """Main function for testing the dashboard."""
    
    if not FLASK_AVAILABLE:
        print("Flask is required for the web dashboard.")
        print("Install with: pip install flask flask-socketio")
        return
    
    # Get project root
    project_root = Path(__file__).parent.parent
    
    # Create templates
    create_dashboard_templates()
    
    # Initialize monitoring overlay
    if MONITORING_AVAILABLE:
        from .monitoring_overlay import QAWolfMonitoringOverlay
        monitoring = QAWolfMonitoringOverlay(str(project_root))
        monitoring.start_monitoring()
    else:
        monitoring = None
    
    # Initialize and start dashboard
    dashboard = QAWolfWebDashboard(str(project_root))
    
    try:
        print("🚀 Starting QA Wolf Web Dashboard...")
        print("🛡️ SAFETY: Read-only monitoring, zero trading impact")
        print("🌐 Dashboard will be available at: http://localhost:5000")
        
        dashboard.start_dashboard(monitoring)
        
    except KeyboardInterrupt:
        print("\n⏹️ Dashboard stopped by user")
    finally:
        if monitoring:
            monitoring.stop_monitoring()
        dashboard.stop_dashboard()


if __name__ == "__main__":
    main()