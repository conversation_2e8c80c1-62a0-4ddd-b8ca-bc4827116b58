"""
Phase 5A comprehensive tests to push order_flow_analyzer/models.py to 90%+ coverage.
"""

import pytest
from datetime import datetime, timezone

class TestOrderFlowAnalyzerModelsPhase5Clean:
    """Phase 5A tests to achieve 90%+ coverage for order_flow_analyzer/models.py."""

    def test_comprehensive_model_validation(self):
        """Test comprehensive validation for all model classes."""
        from src.forex_bot.order_flow_analyzer import models
        
        timestamp = datetime.now(timezone.utc)
        
        # Test all ImbalanceLevel enum values
        for level in [models.ImbalanceLevel.EXTREME_BUY, models.ImbalanceLevel.STRONG_BUY, 
                     models.ImbalanceLevel.MODERATE_BUY, models.ImbalanceLevel.NEUTRAL, 
                     models.ImbalanceLevel.MODERATE_SELL, models.ImbalanceLevel.STRONG_SELL]:
            imbalance = models.OrderFlowImbalance(
                symbol='TEST',
                timestamp=timestamp,
                price_level=1.0,
                bid_volume=100.0,
                ask_volume=50.0,
                imbalance_ratio=0.5,
                imbalance_level=level,
                is_significant=True
            )
            assert imbalance.imbalance_level == level
        
        # Test imbalance_ratio validation
        with pytest.raises(ValueError, match="Imbalance ratio must be between -1.0 and 1.0"):
            models.OrderFlowImbalance(
                symbol='TEST', timestamp=timestamp, price_level=1.0,
                bid_volume=100, ask_volume=100, imbalance_ratio=1.1,
                imbalance_level=models.ImbalanceLevel.NEUTRAL, is_significant=True
            )
        
        # Test LargeOrder validation
        for order_type in ['bid', 'ask']:
            large_order = models.LargeOrder(
                symbol='TEST',
                timestamp=timestamp,
                price_level=1.0,
                volume=1000.0,
                type=order_type,
                is_market_moving=True,
                standard_deviations=2.5
            )
            assert large_order.type == order_type
        
        # Test volume validation
        with pytest.raises(ValueError, match="Volume must be positive"):
            models.LargeOrder(
                symbol='TEST', timestamp=timestamp, price_level=1.0,
                volume=0.0, type='bid', is_market_moving=True, standard_deviations=2.0
            )        
        # Test SupportResistanceLevel validation
        for sr_type in ['support', 'resistance']:
            sr_level = models.SupportResistanceLevel(
                symbol='TEST',
                timestamp=timestamp,
                price_level=1.0,
                type=sr_type,
                strength=0.8,
                volume_concentration=500.0,
                is_active=True
            )
            assert sr_level.type == sr_type
        
        # Test strength validation
        with pytest.raises(ValueError, match="Strength must be between 0.0 and 1.0"):
            models.SupportResistanceLevel(
                symbol='TEST', timestamp=timestamp, price_level=1.0,
                type='support', strength=1.1, volume_concentration=100.0, is_active=True
            )
        
        # Test OrderFlowSignal validation
        signal = models.OrderFlowSignal(
            symbol='TEST',
            timestamp=timestamp,
            signal_type='buy',
            confidence=0.85,
            price_level=1.0,
            reason='Test signal'
        )
        assert signal.confidence == 0.85
        
        # Test OrderFlowContext validation
        imbalance = models.OrderFlowImbalance(
            symbol='TEST', timestamp=timestamp, price_level=1.0,
            bid_volume=100.0, ask_volume=50.0, imbalance_ratio=0.33,
            imbalance_level=models.ImbalanceLevel.MODERATE_BUY, is_significant=True
        )
        
        large_order = models.LargeOrder(
            symbol='TEST', timestamp=timestamp, price_level=1.0,
            volume=1000.0, type='bid', is_market_moving=True, standard_deviations=2.5
        )
        
        sr_level = models.SupportResistanceLevel(
            symbol='TEST', timestamp=timestamp, price_level=1.0,
            type='support', strength=0.8, volume_concentration=500.0, is_active=True
        )
        
        # Test OrderFlowContext with all components
        context = models.OrderFlowContext(
            symbol='EURUSD',
            timestamp=timestamp,
            imbalances=[imbalance],
            large_orders=[large_order],
            support_resistance_levels=[sr_level],
            signals=[signal],
            overall_bias='bullish',
            confidence=0.85
        )
        
        assert context.symbol == 'EURUSD'
        assert context.confidence == 0.85
        assert len(context.imbalances) == 1
        assert len(context.large_orders) == 1
        assert len(context.support_resistance_levels) == 1
        assert len(context.signals) == 1
        
        # Test confidence validation
        with pytest.raises(ValueError, match="Confidence must be between 0.0 and 1.0"):
            models.OrderFlowContext(
                symbol='TEST', timestamp=timestamp, imbalances=[], large_orders=[],
                support_resistance_levels=[], signals=[], overall_bias='neutral', confidence=1.1
            )