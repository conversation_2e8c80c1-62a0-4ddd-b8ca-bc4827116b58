"""
Phase 5I comprehensive tests to push volume_profile/models.py to 100% coverage.
"""

import pytest
import numpy as np
import pandas as pd
from datetime import datetime
from src.forex_bot.volume_profile import models

class TestVolumeProfileModelsPhase5I:
    """Phase 5I tests to achieve 100% coverage for volume_profile/models.py."""

    def test_volume_profile_result_basic_functionality(self):
        """Test VolumeProfileResult class basic functionality."""
        
        # Create test data
        price_levels = np.array([1.1000, 1.1010, 1.1020, 1.1030, 1.1040])
        volumes = np.array([1000, 1500, 2000, 1200, 800])
        normalized_volumes = np.array([0.2, 0.3, 0.4, 0.24, 0.16])
        cumulative_volumes = np.array([1000, 2500, 4500, 5700, 6500])
        
        # Test successful creation
        vp_result = models.VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.1020,
            poc_volume=2000,
            value_area_high=1.1030,
            value_area_low=1.1010,
            symbol='EURUSD',
            timeframe=60,
            start_time=pd.Timestamp('2023-12-15 09:00:00'),
            end_time=pd.Timestamp('2023-12-15 17:00:00'),
            num_bins=100,
            value_area_percent=70.0,
            normalized_volumes=normalized_volumes,
            cumulative_volumes=cumulative_volumes
        )
        
        # Test basic attributes
        assert vp_result.symbol == 'EURUSD'
        assert vp_result.timeframe == 60
        assert vp_result.poc_price == 1.1020
        assert vp_result.poc_volume == 2000
        assert vp_result.value_area_high == 1.1030
        assert vp_result.value_area_low == 1.1010
        assert vp_result.num_bins == 100
        assert vp_result.value_area_percent == 70.0
        
        # Test arrays
        np.testing.assert_array_equal(vp_result.price_levels, price_levels)
        np.testing.assert_array_equal(vp_result.volumes, volumes)
        np.testing.assert_array_equal(vp_result.normalized_volumes, normalized_volumes)
        np.testing.assert_array_equal(vp_result.cumulative_volumes, cumulative_volumes)

    def test_volume_profile_result_to_dataframe(self):
        """Test VolumeProfileResult to_dataframe method."""
        
        # Create test data
        price_levels = np.array([1.1000, 1.1010, 1.1020])
        volumes = np.array([1000, 1500, 2000])
        normalized_volumes = np.array([0.2, 0.3, 0.4])
        cumulative_volumes = np.array([1000, 2500, 4500])
        
        # Test with all arrays populated
        vp_result = models.VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.1020,
            poc_volume=2000,
            value_area_high=1.1020,
            value_area_low=1.1000,
            symbol='EURUSD',
            timeframe=60,
            normalized_volumes=normalized_volumes,
            cumulative_volumes=cumulative_volumes
        )
        
        df = vp_result.to_dataframe()
        
        # Test DataFrame structure
        assert isinstance(df, pd.DataFrame)
        assert len(df) == 3
        assert list(df.columns) == ['price_level', 'volume', 'normalized_volume', 'cumulative_volume']
        
        # Test DataFrame values
        np.testing.assert_array_equal(df['price_level'].values, price_levels)
        np.testing.assert_array_equal(df['volume'].values, volumes)
        np.testing.assert_array_equal(df['normalized_volume'].values, normalized_volumes)
        np.testing.assert_array_equal(df['cumulative_volume'].values, cumulative_volumes)

    def test_volume_profile_result_to_dataframe_empty_arrays(self):
        """Test VolumeProfileResult to_dataframe method with empty normalized/cumulative arrays."""
        
        # Create test data with empty normalized and cumulative arrays
        price_levels = np.array([1.1000, 1.1010, 1.1020])
        volumes = np.array([1000, 1500, 2000])
        
        # Test with empty normalized and cumulative arrays (default behavior)
        vp_result = models.VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.1020,
            poc_volume=2000,
            value_area_high=1.1020,
            value_area_low=1.1000,
            symbol='EURUSD',
            timeframe=60
        )
        
        df = vp_result.to_dataframe()
        
        # Test DataFrame structure
        assert isinstance(df, pd.DataFrame)
        assert len(df) == 3
        assert list(df.columns) == ['price_level', 'volume', 'normalized_volume', 'cumulative_volume']
        
        # Test DataFrame values - normalized and cumulative should be zeros
        np.testing.assert_array_equal(df['price_level'].values, price_levels)
        np.testing.assert_array_equal(df['volume'].values, volumes)
        np.testing.assert_array_equal(df['normalized_volume'].values, np.zeros_like(volumes))
        np.testing.assert_array_equal(df['cumulative_volume'].values, np.zeros_like(volumes))

    def test_volume_profile_result_optional_fields(self):
        """Test VolumeProfileResult with optional fields."""
        
        # Test with minimal required fields
        price_levels = np.array([1.1000, 1.1010])
        volumes = np.array([1000, 1500])
        
        vp_result = models.VolumeProfileResult(
            price_levels=price_levels,
            volumes=volumes,
            poc_price=1.1010,
            poc_volume=1500,
            value_area_high=1.1010,
            value_area_low=1.1000,
            symbol='GBPUSD',
            timeframe=240
        )
        
        # Test default values
        assert vp_result.start_time is None
        assert vp_result.end_time is None
        assert vp_result.num_bins == 100
        assert vp_result.value_area_percent == 70.0
        assert len(vp_result.normalized_volumes) == 0
        assert len(vp_result.cumulative_volumes) == 0

    def test_volume_zone_basic_functionality(self):
        """Test VolumeZone class basic functionality."""
        
        # Test successful creation
        volume_zone = models.VolumeZone(
            zone_type='high_volume',
            price_high=1.1050,
            price_low=1.1030,
            volume=5000,
            strength=0.85,
            description='Strong resistance zone'
        )
        
        # Test basic attributes
        assert volume_zone.zone_type == 'high_volume'
        assert volume_zone.price_high == 1.1050
        assert volume_zone.price_low == 1.1030
        assert volume_zone.volume == 5000
        assert volume_zone.strength == 0.85
        assert volume_zone.description == 'Strong resistance zone'

    def test_volume_zone_properties(self):
        """Test VolumeZone calculated properties."""
        
        # Test mid_price property
        volume_zone = models.VolumeZone(
            zone_type='poc',
            price_high=1.1040,
            price_low=1.1020,
            volume=3000,
            strength=1.0
        )
        
        # Test mid_price calculation
        expected_mid_price = (1.1040 + 1.1020) / 2
        assert abs(volume_zone.mid_price - expected_mid_price) < 1e-10
        
        # Test price_range calculation
        expected_price_range = 1.1040 - 1.1020
        assert abs(volume_zone.price_range - expected_price_range) < 1e-10

    def test_volume_zone_edge_cases(self):
        """Test VolumeZone with edge cases."""
        
        # Test with same high and low price (zero range)
        volume_zone_zero_range = models.VolumeZone(
            zone_type='poc',
            price_high=1.1030,
            price_low=1.1030,
            volume=2000,
            strength=0.5
        )
        
        assert volume_zone_zero_range.mid_price == 1.1030
        assert volume_zone_zero_range.price_range == 0.0
        
        # Test with minimum strength
        volume_zone_min = models.VolumeZone(
            zone_type='low_volume',
            price_high=1.1010,
            price_low=1.1000,
            volume=100,
            strength=0.0
        )
        
        assert volume_zone_min.strength == 0.0
        
        # Test with maximum strength
        volume_zone_max = models.VolumeZone(
            zone_type='value_area',
            price_high=1.1060,
            price_low=1.1040,
            volume=8000,
            strength=1.0
        )
        
        assert volume_zone_max.strength == 1.0

    def test_volume_zone_different_types(self):
        """Test VolumeZone with different zone types."""
        
        # Test different zone types
        zone_types = ['high_volume', 'low_volume', 'poc', 'value_area']
        
        for zone_type in zone_types:
            volume_zone = models.VolumeZone(
                zone_type=zone_type,
                price_high=1.1050,
                price_low=1.1030,
                volume=2500,
                strength=0.7,
                description=f'Test {zone_type} zone'
            )
            
            assert volume_zone.zone_type == zone_type
            assert volume_zone.description == f'Test {zone_type} zone'

    def test_volume_zone_default_description(self):
        """Test VolumeZone with default empty description."""
        
        volume_zone = models.VolumeZone(
            zone_type='high_volume',
            price_high=1.1040,
            price_low=1.1020,
            volume=3500,
            strength=0.6
        )
        
        # Test default empty description
        assert volume_zone.description == ""