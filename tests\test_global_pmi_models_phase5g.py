"""
Phase 5G comprehensive tests to push global_pmi/models.py to 90%+ coverage.
"""

import pytest
from datetime import date
from src.forex_bot.global_pmi import models

class TestGlobalPMIModelsPhase5G:
    """Phase 5G tests to achieve 90%+ coverage for global_pmi/models.py."""

    def test_pmi_data_basic_functionality(self):
        """Test PMIData class basic functionality."""
        
        # Test successful creation with expansion
        pmi_data = models.PMIData(
            country='United States',
            date=date(2023, 12, 15),
            pmi_value=55.2,
            pmi_type='Manufacturing',
            previous_value=52.8,
            forecast_value=53.5
        )
        
        # Test basic attributes
        assert pmi_data.country == 'United States'
        assert pmi_data.date == date(2023, 12, 15)
        assert pmi_data.pmi_value == 55.2
        assert pmi_data.pmi_type == 'Manufacturing'
        assert pmi_data.previous_value == 52.8
        assert pmi_data.forecast_value == 53.5
        
        # Test post_init calculations
        assert pmi_data.is_expansion == True  # 55.2 > 50
        assert pmi_data.is_contraction == False
        assert abs(pmi_data.change - 2.4) < 1e-10  # 55.2 - 52.8

    def test_pmi_data_contraction_scenario(self):
        """Test PMIData with contraction scenario."""
        
        # Test PMI in contraction territory
        pmi_data = models.PMIData(
            country='Germany',
            date=date(2023, 12, 15),
            pmi_value=48.5,
            pmi_type='Services',
            previous_value=49.2
        )
        
        # Test contraction flags
        assert pmi_data.is_expansion == False
        assert pmi_data.is_contraction == True  # 48.5 < 50
        assert abs(pmi_data.change - (-0.7)) < 1e-10  # 48.5 - 49.2    def test_pmi_data_edge_cases(self):
        """Test PMIData with edge cases."""
        
        # Test PMI exactly at 50 (neutral)
        pmi_neutral = models.PMIData(
            country='Japan',
            date=date(2023, 12, 15),
            pmi_value=50.0,
            pmi_type='Composite'
        )
        
        assert pmi_neutral.is_expansion == False  # 50.0 is not > 50
        assert pmi_neutral.is_contraction == False  # 50.0 is not < 50
        assert pmi_neutral.change is None  # No previous value
        
        # Test with pre-calculated change
        pmi_with_change = models.PMIData(
            country='UK',
            date=date(2023, 12, 15),
            pmi_value=52.0,
            pmi_type='Manufacturing',
            previous_value=51.0,
            change=1.5  # Pre-calculated, should not be overridden
        )
        
        assert pmi_with_change.change == 1.5  # Should keep pre-calculated value
        
        # Test without previous value
        pmi_no_previous = models.PMIData(
            country='France',
            date=date(2023, 12, 15),
            pmi_value=53.2,
            pmi_type='Services'
        )
        
        assert pmi_no_previous.previous_value is None
        assert pmi_no_previous.change is None

    def test_pmi_trend_basic_functionality(self):
        """Test PMITrend class basic functionality."""
        
        # Test successful creation
        pmi_trend = models.PMITrend(
            country='United States',
            pmi_type='Manufacturing',
            latest_date=date(2023, 12, 15),
            latest_value=55.2,
            trend_direction='up',
            trend_strength=0.75,
            values_3m=[53.1, 54.2, 55.2],
            values_6m=[51.8, 52.3, 52.9, 53.1, 54.2, 55.2],
            values_12m=[49.5, 50.1, 50.8, 51.2, 51.8, 52.3, 52.9, 53.1, 54.2, 55.2, 54.8, 55.2],
            avg_3m=54.17,
            avg_6m=53.08,
            avg_12m=52.58,
            is_improving=True,
            is_deteriorating=False,
            is_above_3m_avg=True,
            is_above_6m_avg=True,
            is_above_12m_avg=True,
            crossed_above_50=True
        )
        
        # Test basic attributes
        assert pmi_trend.country == 'United States'
        assert pmi_trend.pmi_type == 'Manufacturing'
        assert pmi_trend.latest_date == date(2023, 12, 15)
        assert pmi_trend.latest_value == 55.2
        assert pmi_trend.trend_direction == 'up'
        assert pmi_trend.trend_strength == 0.75
        assert pmi_trend.is_improving == True
        assert pmi_trend.is_deteriorating == False
        assert pmi_trend.crossed_above_50 == True
        assert pmi_trend.crossed_below_50 == False

    def test_pmi_trend_economic_state_scenarios(self):
        """Test PMITrend economic_state property with various scenarios."""
        
        # Test Strongly Expanding (PMI > 50, trend up)
        trend_strong_expand = models.PMITrend(
            country='Germany',
            pmi_type='Services',
            latest_date=date(2023, 12, 15),
            latest_value=55.0,
            trend_direction='up',
            trend_strength=0.8,
            values_3m=[52.0, 53.5, 55.0],
            values_6m=[50.5, 51.2, 52.0, 53.5, 54.2, 55.0],
            values_12m=[48.0, 49.0, 50.5, 51.2, 52.0, 53.5, 54.2, 55.0, 54.8, 55.0, 55.2, 55.0],
            avg_3m=53.5,
            avg_6m=52.73,
            avg_12m=52.02,
            is_improving=True,
            is_deteriorating=False,
            is_above_3m_avg=True,
            is_above_6m_avg=True,
            is_above_12m_avg=True
        )
        
        assert trend_strong_expand.economic_state == "Strongly Expanding"
        
        # Test Moderately Expanding (PMI > 50, trend down)
        trend_moderate_expand = models.PMITrend(
            country='Japan',
            pmi_type='Manufacturing',
            latest_date=date(2023, 12, 15),
            latest_value=52.0,
            trend_direction='down',
            trend_strength=0.6,
            values_3m=[54.0, 53.0, 52.0],
            values_6m=[55.0, 54.5, 54.0, 53.0, 52.5, 52.0],
            values_12m=[56.0, 55.5, 55.0, 54.5, 54.0, 53.0, 52.5, 52.0, 52.2, 52.0, 51.8, 52.0],
            avg_3m=53.0,
            avg_6m=53.5,
            avg_12m=53.38,
            is_improving=False,
            is_deteriorating=True,
            is_above_3m_avg=False,
            is_above_6m_avg=False,
            is_above_12m_avg=False
        )
        
        assert trend_moderate_expand.economic_state == "Moderately Expanding"
        
        # Test Stable Expansion (PMI > 50, trend flat)
        trend_stable_expand = models.PMITrend(
            country='UK',
            pmi_type='Composite',
            latest_date=date(2023, 12, 15),
            latest_value=51.5,
            trend_direction='flat',
            trend_strength=0.2,
            values_3m=[51.4, 51.6, 51.5],
            values_6m=[51.3, 51.4, 51.6, 51.5, 51.4, 51.5],
            values_12m=[51.2, 51.3, 51.4, 51.6, 51.5, 51.4, 51.5, 51.6, 51.4, 51.5, 51.3, 51.5],
            avg_3m=51.5,
            avg_6m=51.45,
            avg_12m=51.43,
            is_improving=False,
            is_deteriorating=False,
            is_above_3m_avg=False,
            is_above_6m_avg=True,
            is_above_12m_avg=True
        )
        
        assert trend_stable_expand.economic_state == "Stable Expansion"

    def test_pmi_trend_contraction_scenarios(self):
        """Test PMITrend economic_state property with contraction scenarios."""
        
        # Test Improving Contraction (PMI < 50, trend up)
        trend_improving_contract = models.PMITrend(
            country='France',
            pmi_type='Manufacturing',
            latest_date=date(2023, 12, 15),
            latest_value=48.5,
            trend_direction='up',
            trend_strength=0.7,
            values_3m=[46.0, 47.2, 48.5],
            values_6m=[44.5, 45.2, 46.0, 47.2, 47.8, 48.5],
            values_12m=[42.0, 43.5, 44.5, 45.2, 46.0, 47.2, 47.8, 48.5, 48.2, 48.5, 48.3, 48.5],
            avg_3m=47.23,
            avg_6m=46.53,
            avg_12m=46.18,
            is_improving=True,
            is_deteriorating=False,
            is_above_3m_avg=True,
            is_above_6m_avg=True,
            is_above_12m_avg=True
        )
        
        assert trend_improving_contract.economic_state == "Improving Contraction"
        
        # Test Deepening Contraction (PMI < 50, trend down)
        trend_deepening_contract = models.PMITrend(
            country='Italy',
            pmi_type='Services',
            latest_date=date(2023, 12, 15),
            latest_value=45.0,
            trend_direction='down',
            trend_strength=0.8,
            values_3m=[47.5, 46.2, 45.0],
            values_6m=[49.0, 48.2, 47.5, 46.2, 45.8, 45.0],
            values_12m=[51.0, 50.2, 49.0, 48.2, 47.5, 46.2, 45.8, 45.0, 45.3, 45.0, 44.8, 45.0],
            avg_3m=46.23,
            avg_6m=46.95,
            avg_12m=47.18,
            is_improving=False,
            is_deteriorating=True,
            is_above_3m_avg=False,
            is_above_6m_avg=False,
            is_above_12m_avg=False
        )
        
        assert trend_deepening_contract.economic_state == "Deepening Contraction"
        
        # Test Stable Contraction (PMI < 50, trend flat)
        trend_stable_contract = models.PMITrend(
            country='Spain',
            pmi_type='Composite',
            latest_date=date(2023, 12, 15),
            latest_value=49.0,
            trend_direction='flat',
            trend_strength=0.1,
            values_3m=[49.1, 48.9, 49.0],
            values_6m=[49.2, 49.1, 48.9, 49.0, 48.8, 49.0],
            values_12m=[49.3, 49.2, 49.1, 48.9, 49.0, 48.8, 49.0, 49.1, 48.9, 49.0, 49.2, 49.0],
            avg_3m=49.0,
            avg_6m=49.0,
            avg_12m=49.04,
            is_improving=False,
            is_deteriorating=False,
            is_above_3m_avg=False,
            is_above_6m_avg=False,
            is_above_12m_avg=False
        )
        
        assert trend_stable_contract.economic_state == "Stable Contraction"

    def test_pmi_divergence_positive_scenario(self):
        """Test PMIDivergence with positive divergence scenario."""
        
        # Test positive divergence (PMI up, currency down)
        divergence = models.PMIDivergence(
            country='United States',
            currency='USD',
            start_date=date(2023, 11, 15),
            end_date=date(2023, 12, 15),
            start_pmi=52.0,
            end_pmi=55.5,
            pmi_change=3.5,
            start_currency_value=1.0800,
            end_currency_value=1.0750,
            currency_change=-0.0050,
            divergence_type='positive',
            strength=0.75
        )
        
        # Test basic attributes
        assert divergence.country == 'United States'
        assert divergence.currency == 'USD'
        assert divergence.start_date == date(2023, 11, 15)
        assert divergence.end_date == date(2023, 12, 15)
        assert divergence.start_pmi == 52.0
        assert divergence.end_pmi == 55.5
        assert divergence.pmi_change == 3.5
        assert divergence.start_currency_value == 1.0800
        assert divergence.end_currency_value == 1.0750
        assert divergence.currency_change == -0.0050
        assert divergence.divergence_type == 'positive'
        assert divergence.strength == 0.75
        
        # Test properties
        assert divergence.is_positive_divergence == True
        assert divergence.is_negative_divergence == False
        assert divergence.trading_signal == "BUY"  # Strong positive divergence

    def test_pmi_divergence_negative_scenario(self):
        """Test PMIDivergence with negative divergence scenario."""
        
        # Test negative divergence (PMI down, currency up)
        divergence = models.PMIDivergence(
            country='Germany',
            currency='EUR',
            start_date=date(2023, 11, 15),
            end_date=date(2023, 12, 15),
            start_pmi=54.0,
            end_pmi=49.5,
            pmi_change=-4.5,
            start_currency_value=1.0950,
            end_currency_value=1.1020,
            currency_change=0.0070,
            divergence_type='negative',
            strength=0.85
        )
        
        # Test properties
        assert divergence.is_positive_divergence == False
        assert divergence.is_negative_divergence == True
        assert divergence.trading_signal == "SELL"  # Strong negative divergence

    def test_pmi_divergence_weak_signals(self):
        """Test PMIDivergence with weak signals."""
        
        # Test weak positive divergence (no trading signal)
        weak_positive = models.PMIDivergence(
            country='Japan',
            currency='JPY',
            start_date=date(2023, 11, 15),
            end_date=date(2023, 12, 15),
            start_pmi=51.0,
            end_pmi=52.0,
            pmi_change=1.0,
            start_currency_value=150.50,
            end_currency_value=150.20,
            currency_change=-0.30,
            divergence_type='positive',
            strength=0.3  # Weak strength
        )
        
        assert weak_positive.is_positive_divergence == True
        assert weak_positive.trading_signal is None  # Weak signal, no trade
        
        # Test weak negative divergence (no trading signal)
        weak_negative = models.PMIDivergence(
            country='UK',
            currency='GBP',
            start_date=date(2023, 11, 15),
            end_date=date(2023, 12, 15),
            start_pmi=52.5,
            end_pmi=51.8,
            pmi_change=-0.7,
            start_currency_value=1.2650,
            end_currency_value=1.2680,
            currency_change=0.0030,
            divergence_type='negative',
            strength=0.4  # Weak strength
        )
        
        assert weak_negative.is_negative_divergence == True
        assert weak_negative.trading_signal is None  # Weak signal, no trade