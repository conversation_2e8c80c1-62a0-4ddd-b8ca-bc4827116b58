#!/usr/bin/env python3
"""
QA Wolf Enhancement Suite - Documentation Generator Script

This script generates comprehensive documentation for your Forex Trading Bot
including API references, installation guides, and user manuals.

SAFETY LEVEL: MAXIMUM - Documentation generation only, no code modification
"""

import sys
from pathlib import Path

# Add the core directory to the path
sys.path.append(str(Path(__file__).parent.parent / "core"))

def main():
    """Generate comprehensive documentation."""
    
    print("📚 QA Wolf Enhancement Suite - Documentation Generator")
    print("🛡️ SAFETY: Documentation generation only, no code modification")
    print("=" * 60)
    
    try:
        # Import documentation generator
        from documentation_generator import DocumentationGenerator
        
        # Get project root
        project_root = Path(__file__).parent.parent.parent
        
        print(f"📁 Project root: {project_root}")
        print("🔧 Initializing documentation generator...")
        
        # Initialize generator
        generator = DocumentationGenerator(str(project_root))
        
        print("📚 Generating documentation:")
        print("   - API Reference (from code analysis)")
        print("   - Installation Guide (setup instructions)")
        print("   - User Manual (complete usage guide)")
        
        print(f"\n🚀 Starting documentation generation...")
        print("⏱️ Estimated duration: 10-30 seconds")
        print("📁 Output directory: qa_wolf_enhancements/docs/")
        print("\n" + "=" * 60)
        
        # Generate all documentation
        docs = generator.generate_all_documentation()
        
        # Display results
        print("\n📚 DOCUMENTATION GENERATION COMPLETE")
        print("=" * 60)
        
        for doc_type, file_path in docs.items():
            print(f"✅ {doc_type.replace('_', ' ').title()}: {file_path}")
        
        print(f"\n📁 All documentation saved to: qa_wolf_enhancements/docs/")
        print("🌐 Ready for client delivery and usage")
        print("✅ Documentation generation completed successfully!")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all dependencies are installed:")
        print("   pip install -r requirements.txt")
        
    except Exception as e:
        print(f"❌ Error generating documentation: {e}")
        print("🔧 Check the troubleshooting guide in docs/troubleshooting.md")


if __name__ == "__main__":
    main()