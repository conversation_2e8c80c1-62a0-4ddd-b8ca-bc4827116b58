"""
Phase 6A: Test for correlation_matrix/models.py to push from 61% to 90%+ coverage.
Targeting all missing areas including import fallbacks, validators, and fallback classes.
"""

import pytest
from datetime import datetime, timezone
from unittest.mock import patch
from src.forex_bot.correlation_matrix.models import (
    TimeWindow, CorrelationMethod, CorrelationStrength,
    CorrelationSettings, CorrelationPair, CorrelationMatrix,
    CorrelationTrend, CorrelationAlert, CorrelationVisualization
)


class TestCorrelationMatrixModelsPhase6A:
    """Test class to push correlation_matrix/models.py to 90%+ coverage"""

    def test_import_fallbacks(self):
        """Test import fallbacks for numpy, pandas, and pydantic"""
        
        # Test numpy unavailable fallback (lines 14-15)
        with patch('src.forex_bot.correlation_matrix.models.NUMPY_AVAILABLE', False):
            import importlib
            import src.forex_bot.correlation_matrix.models as models_module
            importlib.reload(models_module)
            assert not models_module.NUMPY_AVAILABLE

        # Test pandas unavailable fallback (lines 20-21)
        with patch('src.forex_bot.correlation_matrix.models.PANDAS_AVAILABLE', False):
            import importlib
            import src.forex_bot.correlation_matrix.models as models_module
            importlib.reload(models_module)
            assert not models_module.PANDAS_AVAILABLE

    def test_pydantic_fallback_classes(self):
        """Test all pydantic fallback classes when pydantic is unavailable"""
        
        # Test with pydantic unavailable to trigger fallback classes (lines 26-35, 203-316)
        with patch('src.forex_bot.correlation_matrix.models.PYDANTIC_AVAILABLE', False):
            import importlib
            import src.forex_bot.correlation_matrix.models as models_module
            importlib.reload(models_module)
            
            # Test fallback CorrelationSettings
            settings = models_module.CorrelationSettings(
                time_window=models_module.TimeWindow.HOUR_4,
                method=models_module.CorrelationMethod.SPEARMAN,
                min_periods=50,
                symbols=["EURUSD", "GBPUSD"],
                include_base_pairs=False
            )
            assert settings.time_window == models_module.TimeWindow.HOUR_4
            assert settings.method == models_module.CorrelationMethod.SPEARMAN
            assert settings.min_periods == 50
            assert settings.symbols == ["EURUSD", "GBPUSD"]
            assert settings.include_base_pairs is False
            
            # Test fallback CorrelationSettings dict method
            settings_dict = settings.dict()
            assert settings_dict["time_window"] == models_module.TimeWindow.HOUR_4
            assert settings_dict["min_periods"] == 50            # Test fallback CorrelationPair
            now = datetime.now(timezone.utc)
            pair = models_module.CorrelationPair(
                symbol1="EURUSD",
                symbol2="GBPUSD", 
                correlation=0.75,
                strength=models_module.CorrelationStrength.STRONG_POSITIVE,
                p_value=0.001,
                timestamp=now,
                time_window=models_module.TimeWindow.DAY_1,
                method=models_module.CorrelationMethod.PEARSON
            )
            assert pair.symbol1 == "EURUSD"
            assert pair.symbol2 == "GBPUSD"
            assert pair.correlation == 0.75
            assert pair.strength == models_module.CorrelationStrength.STRONG_POSITIVE
            
            # Test fallback CorrelationPair dict method
            pair_dict = pair.dict()
            assert pair_dict["symbol1"] == "EURUSD"
            assert pair_dict["correlation"] == 0.75
            
            # Test fallback CorrelationMatrix
            matrix_data = {
                "EURUSD": {"EURUSD": 1.0, "GBPUSD": 0.75},
                "GBPUSD": {"EURUSD": 0.75, "GBPUSD": 1.0}
            }
            matrix = models_module.CorrelationMatrix(
                timestamp=now,
                time_window=models_module.TimeWindow.DAY_1,
                method=models_module.CorrelationMethod.PEARSON,
                symbols=["EURUSD", "GBPUSD"],
                matrix=matrix_data,
                pairs=[pair]
            )
            assert matrix.symbols == ["EURUSD", "GBPUSD"]
            assert matrix.matrix == matrix_data
            
            # Test fallback CorrelationMatrix dict method
            matrix_dict = matrix.dict()
            assert matrix_dict["symbols"] == ["EURUSD", "GBPUSD"]
            
            # Test fallback CorrelationTrend
            trend = models_module.CorrelationTrend(
                symbol1="EURUSD",
                symbol2="GBPUSD",
                timestamps=[now],
                correlations=[0.75],
                time_window=models_module.TimeWindow.DAY_1,
                method=models_module.CorrelationMethod.PEARSON
            )
            assert trend.symbol1 == "EURUSD"
            assert trend.correlations == [0.75]
            
            # Test fallback CorrelationTrend dict method
            trend_dict = trend.dict()
            assert trend_dict["symbol1"] == "EURUSD"
            assert trend_dict["correlations"] == [0.75]    def test_validator_edge_cases(self):
        """Test validator edge cases and error conditions"""
        
        now = datetime.now(timezone.utc)
        
        # Test CorrelationSettings min_periods validator (lines 72-74)
        with pytest.raises(ValueError, match="min_periods must be at least 2"):
            CorrelationSettings(min_periods=1)
        
        # Test CorrelationPair correlation validator (lines 89-91)
        with pytest.raises(ValueError, match="correlation must be between -1.0 and 1.0"):
            CorrelationPair(
                symbol1="EURUSD",
                symbol2="GBPUSD",
                correlation=1.5,  # Invalid correlation
                strength=CorrelationStrength.STRONG_POSITIVE,
                timestamp=now,
                time_window=TimeWindow.DAY_1,
                method=CorrelationMethod.PEARSON
            )
        
        # Test CorrelationPair strength validator (lines 95-111)
        with pytest.raises(ValueError, match="strength should be"):
            CorrelationPair(
                symbol1="EURUSD",
                symbol2="GBPUSD",
                correlation=0.75,  # Strong positive
                strength=CorrelationStrength.WEAK_POSITIVE,  # Wrong strength
                timestamp=now,
                time_window=TimeWindow.DAY_1,
                method=CorrelationMethod.PEARSON
            )
        
        # Test CorrelationMatrix matrix validator (lines 124-131)
        with pytest.raises(ValueError, match="matrix missing symbol"):
            CorrelationMatrix(
                timestamp=now,
                time_window=TimeWindow.DAY_1,
                method=CorrelationMethod.PEARSON,
                symbols=["EURUSD", "GBPUSD"],
                matrix={"EURUSD": {"EURUSD": 1.0}},  # Missing GBPUSD
                pairs=[]
            )
        
        # Test CorrelationTrend correlations validator (lines 154-160)
        with pytest.raises(ValueError, match="correlations and timestamps must have the same length"):
            CorrelationTrend(
                symbol1="EURUSD",
                symbol2="GBPUSD",
                timestamps=[now],
                correlations=[0.75, 0.80],  # Different length
                time_window=TimeWindow.DAY_1,
                method=CorrelationMethod.PEARSON
            )
        
        # Test CorrelationAlert change validator (lines 186-191)
        with pytest.raises(ValueError, match="change should be"):
            CorrelationAlert(
                symbol1="EURUSD",
                symbol2="GBPUSD",
                old_correlation=0.5,
                new_correlation=0.7,
                change=0.1,  # Wrong change (should be 0.2)
                timestamp=now,
                time_window=TimeWindow.DAY_1,
                method=CorrelationMethod.PEARSON,
                is_significant=True
            )
        
        # Test CorrelationVisualization image_data validator (lines 203)
        with pytest.raises(ValueError, match="image_data cannot be empty"):
            CorrelationVisualization(
                timestamp=now,
                time_window=TimeWindow.DAY_1,
                method=CorrelationMethod.PEARSON,
                symbols=["EURUSD", "GBPUSD"],
                image_data=""  # Empty image data
            )