"""
Phase 5C comprehensive tests to push market_depth_visualizer/models.py to 90%+ coverage.
"""

import pytest
from datetime import datetime, timezone

class TestMarketDepthVisualizerModelsPhase5:
    """Phase 5C tests to achieve 90%+ coverage for market_depth_visualizer/models.py."""

    def test_comprehensive_enum_validation(self):
        """Test all enum values and their usage."""
        from src.forex_bot.market_depth_visualizer import models

        # Test VisualizationType enum
        viz_types = [
            models.VisualizationType.DEPTH_CHART,
            models.VisualizationType.HEATMAP,
            models.VisualizationType.TIME_AND_SALES,
            models.VisualizationType.LIQUIDITY_MAP,
            models.VisualizationType.ORDER_FLOW_FOOTPRINT,
            models.VisualizationType.DASHBOARD
        ]

        for viz_type in viz_types:
            assert isinstance(viz_type, models.VisualizationType)

        # Test ColorScheme enum
        color_schemes = [
            models.ColorScheme.DEFAULT,
            models.ColorScheme.DARK,
            models.ColorScheme.LIGHT,
            models.ColorScheme.COLORBLIND,
            models.ColorScheme.CUSTOM
        ]

        for scheme in color_schemes:
            assert isinstance(scheme, models.ColorScheme)

    def test_comprehensive_settings_validation(self):
        """Test all settings classes with various configurations."""
        from src.forex_bot.market_depth_visualizer import models

        # Test DepthChartSettings
        depth_settings = models.DepthChartSettings(
            price_levels=15,
            show_imbalances=False,
            show_current_price=True,
            color_scheme=models.ColorScheme.DARK,
            custom_bid_color="#00FF00",
            custom_ask_color="#FF0000",
            log_scale=True,
            show_tooltips=False,
            highlight_large_orders=True,
            large_order_threshold=3.0
        )
        assert depth_settings.price_levels == 15
        assert depth_settings.show_imbalances is False
        assert depth_settings.large_order_threshold == 3.0
        # Test HeatmapSettings
        heatmap_settings = models.HeatmapSettings(
            price_levels=25,
            time_window=120,
            color_scheme=models.ColorScheme.COLORBLIND,
            custom_colormap="viridis",
            show_current_price=False,
            show_tooltips=True,
            interpolation="bilinear",
            normalization="log"
        )
        assert heatmap_settings.price_levels == 25
        assert heatmap_settings.time_window == 120
        assert heatmap_settings.normalization == "log"

        # Test TimeAndSalesSettings
        time_sales_settings = models.TimeAndSalesSettings(
            max_entries=200,
            show_direction=False,
            show_time=True,
            show_price=False,
            show_volume=True,
            color_scheme=models.ColorScheme.LIGHT,
            custom_buy_color="#0000FF",
            custom_sell_color="#FF00FF",
            highlight_large_trades=False,
            large_trade_threshold=1.5
        )
        assert time_sales_settings.max_entries == 200
        assert time_sales_settings.show_direction is False
        assert time_sales_settings.large_trade_threshold == 1.5

        # Test LiquidityMapSettings
        liquidity_settings = models.LiquidityMapSettings(
            price_levels=30,
            show_bid_liquidity=False,
            show_ask_liquidity=True,
            color_scheme=models.ColorScheme.CUSTOM,
            custom_bid_color="#FFFF00",
            custom_ask_color="#00FFFF",
            show_tooltips=False,
            normalization="log"
        )
        assert liquidity_settings.price_levels == 30
        assert liquidity_settings.show_bid_liquidity is False
        assert liquidity_settings.normalization == "log"
        # Test OrderFlowFootprintSettings
        footprint_settings = models.OrderFlowFootprintSettings(
            price_levels=40,
            time_window=180,
            show_imbalances=False,
            color_scheme=models.ColorScheme.DEFAULT,
            custom_buy_color="#123456",
            custom_sell_color="#654321",
            show_tooltips=True,
            show_delta=False,
            delta_type="trades"
        )
        assert footprint_settings.price_levels == 40
        assert footprint_settings.time_window == 180
        assert footprint_settings.delta_type == "trades"

        # Test DashboardSettings
        dashboard_settings = models.DashboardSettings(
            layout=[
                [models.VisualizationType.DEPTH_CHART],
                [models.VisualizationType.HEATMAP, models.VisualizationType.TIME_AND_SALES]
            ],
            refresh_interval=2000,
            show_title=False,
            show_timestamp=True,
            color_scheme=models.ColorScheme.DARK,
            custom_background_color="#000000",
            custom_text_color="#FFFFFF"
        )
        assert dashboard_settings.refresh_interval == 2000
        assert dashboard_settings.show_title is False
        assert len(dashboard_settings.layout) == 2

    def test_comprehensive_trade_entry_validation(self):
        """Test TradeEntry validation scenarios."""
        from src.forex_bot.market_depth_visualizer import models

        timestamp = datetime.now(timezone.utc)

        # Test successful creation
        trade = models.TradeEntry(
            timestamp=timestamp,
            price=1.2345,
            volume=1000.0,
            direction="buy",
            is_large=True
        )
        assert trade.price == 1.2345
        assert trade.volume == 1000.0
        assert trade.direction == "buy"
        assert trade.is_large is True

        # Test volume validation
        with pytest.raises(ValueError, match="Volume must be positive"):
            models.TradeEntry(
                timestamp=timestamp, price=1.0, volume=0.0, direction="sell"
            )
        # Test negative volume validation
        with pytest.raises(ValueError, match="Volume must be positive"):
            models.TradeEntry(
                timestamp=timestamp, price=1.0, volume=-100.0, direction="buy"
            )

    def test_comprehensive_market_depth_snapshot_validation(self):
        """Test MarketDepthSnapshot validation and properties."""
        from src.forex_bot.market_depth_visualizer import models

        timestamp = datetime.now(timezone.utc)

        # Create sample trade entries
        trades = [
            models.TradeEntry(
                timestamp=timestamp, price=1.2345, volume=100.0, direction="buy"
            ),
            models.TradeEntry(
                timestamp=timestamp, price=1.2346, volume=200.0, direction="sell"
            )
        ]

        # Test successful creation
        snapshot = models.MarketDepthSnapshot(
            symbol='EURUSD',
            timestamp=timestamp,
            bid_prices=[1.2340, 1.2339, 1.2338],
            bid_volumes=[100.0, 150.0, 200.0],
            ask_prices=[1.2341, 1.2342, 1.2343],
            ask_volumes=[120.0, 180.0, 250.0],
            trades=trades
        )

        assert snapshot.symbol == 'EURUSD'
        assert len(snapshot.bid_prices) == 3
        assert len(snapshot.ask_prices) == 3
        assert len(snapshot.trades) == 2

        # Test properties
        assert snapshot.best_bid == 1.2340
        assert snapshot.best_ask == 1.2341
        assert abs(snapshot.spread - 0.0001) < 1e-10  # Handle floating point precision
        assert abs(snapshot.mid_price - 1.23405) < 1e-10  # Handle floating point precision
        assert snapshot.total_bid_volume == 450.0
        assert snapshot.total_ask_volume == 550.0

        # Test imbalance ratio calculation
        expected_imbalance = (450.0 - 550.0) / (450.0 + 550.0)
        assert abs(snapshot.imbalance_ratio - expected_imbalance) < 1e-10

        # Test cumulative volumes
        assert snapshot.cumulative_bid_volumes == [100.0, 250.0, 450.0]
        assert snapshot.cumulative_ask_volumes == [120.0, 300.0, 550.0]
        # Test validation errors
        with pytest.raises(ValueError, match="Bid prices cannot be empty"):
            models.MarketDepthSnapshot(
                symbol='TEST', timestamp=timestamp, bid_prices=[],
                bid_volumes=[], ask_prices=[1.0], ask_volumes=[100.0]
            )

        with pytest.raises(ValueError, match="Ask prices cannot be empty"):
            models.MarketDepthSnapshot(
                symbol='TEST', timestamp=timestamp, bid_prices=[1.0],
                bid_volumes=[100.0], ask_prices=[], ask_volumes=[]
            )

        with pytest.raises(ValueError, match="Bid volumes must have the same length as bid prices"):
            models.MarketDepthSnapshot(
                symbol='TEST', timestamp=timestamp, bid_prices=[1.0, 1.1],
                bid_volumes=[100.0], ask_prices=[1.2], ask_volumes=[100.0]
            )

        with pytest.raises(ValueError, match="Ask volumes must have the same length as ask prices"):
            models.MarketDepthSnapshot(
                symbol='TEST', timestamp=timestamp, bid_prices=[1.0],
                bid_volumes=[100.0], ask_prices=[1.2, 1.3], ask_volumes=[100.0]
            )

        # Test edge cases with empty volumes
        empty_snapshot = models.MarketDepthSnapshot(
            symbol='EMPTY', timestamp=timestamp, bid_prices=[1.0],
            bid_volumes=[0.0], ask_prices=[1.1], ask_volumes=[0.0]
        )
        assert empty_snapshot.total_bid_volume == 0.0
        assert empty_snapshot.total_ask_volume == 0.0
        assert empty_snapshot.imbalance_ratio is None  # Division by zero case

    def test_comprehensive_visualization_models(self):
        """Test MarketDepthVisualization and MarketDepthDashboard."""
        from src.forex_bot.market_depth_visualizer import models

        timestamp = datetime.now(timezone.utc)

        # Test MarketDepthVisualization
        visualization = models.MarketDepthVisualization(
            symbol='EURUSD',
            timestamp=timestamp,
            visualization_type=models.VisualizationType.DEPTH_CHART,
            image_data='iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
            settings={'price_levels': 10, 'color_scheme': 'default'}
        )
        assert visualization.symbol == 'EURUSD'
        assert visualization.visualization_type == models.VisualizationType.DEPTH_CHART
        assert len(visualization.image_data) > 0

        # Test image_data validation
        with pytest.raises(ValueError, match="Image data cannot be empty"):
            models.MarketDepthVisualization(
                symbol='TEST', timestamp=timestamp,
                visualization_type=models.VisualizationType.HEATMAP,
                image_data='', settings={}
            )
        # Test MarketDepthDashboard
        dashboard_settings = models.DashboardSettings()
        visualizations = {
            models.VisualizationType.DEPTH_CHART: visualization,
            models.VisualizationType.HEATMAP: models.MarketDepthVisualization(
                symbol='EURUSD', timestamp=timestamp,
                visualization_type=models.VisualizationType.HEATMAP,
                image_data='base64data', settings={}
            )
        }

        dashboard = models.MarketDepthDashboard(
            symbol='EURUSD',
            timestamp=timestamp,
            visualizations=visualizations,
            settings=dashboard_settings
        )
        assert dashboard.symbol == 'EURUSD'
        assert len(dashboard.visualizations) == 2

        # Test visualizations validation
        with pytest.raises(ValueError, match="Visualizations cannot be empty"):
            models.MarketDepthDashboard(
                symbol='TEST', timestamp=timestamp,
                visualizations={}, settings=dashboard_settings
            )

    def test_comprehensive_visualization_settings(self):
        """Test VisualizationSettings combined model."""
        from src.forex_bot.market_depth_visualizer import models

        # Test with default settings
        settings = models.VisualizationSettings()
        assert isinstance(settings.depth_chart, models.DepthChartSettings)
        assert isinstance(settings.heatmap, models.HeatmapSettings)
        assert isinstance(settings.time_and_sales, models.TimeAndSalesSettings)
        assert isinstance(settings.liquidity_map, models.LiquidityMapSettings)
        assert isinstance(settings.order_flow_footprint, models.OrderFlowFootprintSettings)
        assert isinstance(settings.dashboard, models.DashboardSettings)

        # Test with custom settings
        custom_depth = models.DepthChartSettings(price_levels=20)
        custom_heatmap = models.HeatmapSettings(time_window=120)

        custom_settings = models.VisualizationSettings(
            depth_chart=custom_depth,
            heatmap=custom_heatmap
        )
        assert custom_settings.depth_chart.price_levels == 20
        assert custom_settings.heatmap.time_window == 120