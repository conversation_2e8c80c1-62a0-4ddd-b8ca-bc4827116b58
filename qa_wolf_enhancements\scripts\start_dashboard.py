#!/usr/bin/env python3
"""
QA Wolf Enhancement Suite - Start Web Dashboard Script

This script launches the professional web dashboard for real-time monitoring
of your Forex Trading Bot with zero trading impact.

SAFETY LEVEL: MAXIMUM - Read-only dashboard, no trading interference
"""

import sys
import time
import logging
from pathlib import Path

# Add the core directory to the path
sys.path.append(str(Path(__file__).parent.parent / "core"))

def main():
    """Start the QA Wolf web dashboard."""
    
    print("🌐 QA Wolf Enhancement Suite - Web Dashboard")
    print("🛡️ SAFETY: Read-only interface, zero trading impact")
    print("=" * 60)
    
    try:
        # Check Flask availability
        try:
            import flask
            import flask_socketio
            print("✅ Flask and SocketIO available")
        except ImportError:
            print("❌ Flask not available")
            print("💡 Install with: pip install flask flask-socketio")
            return
        
        # Import dashboard and monitoring
        from web_dashboard import QAWolfWebDashboard
        from monitoring_overlay import QAWolfMonitoringOverlay
        
        # Get project root
        project_root = Path(__file__).parent.parent.parent
        
        print(f"📁 Project root: {project_root}")
        print("🔧 Initializing web dashboard...")
        
        # Initialize monitoring overlay
        print("📊 Starting monitoring overlay...")
        monitor = QAWolfMonitoringOverlay(str(project_root), monitoring_interval=30)
        monitor.start_monitoring()
        print("✅ Monitoring overlay active")
        
        # Initialize dashboard
        dashboard = QAWolfWebDashboard(str(project_root), port=5000)
        
        print("🌐 Dashboard features:")
        print("   - Real-time performance charts")
        print("   - Trading bot status monitoring")
        print("   - Interactive alert system")
        print("   - Mobile-responsive design")
        print("   - Live WebSocket updates")
        
        print(f"\n🚀 Starting web dashboard on port 5000...")
        print("🌐 Dashboard URL: http://localhost:5000")
        print("📱 Mobile accessible: Yes")
        print("🔄 Update frequency: 30 seconds")
        print("⏹️ To stop dashboard, press Ctrl+C")
        print("\n" + "=" * 60)
        
        # Start dashboard (this will block)
        dashboard.start_dashboard(monitor)
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure all dependencies are installed:")
        print("   pip install -r requirements.txt")
        
    except Exception as e:
        print(f"❌ Error starting dashboard: {e}")
        print("🔧 Check the troubleshooting guide in docs/troubleshooting.md")
    
    finally:
        print("\n⏹️ Dashboard stopped")


if __name__ == "__main__":
    main()