"""
Phase 6B: Clean test for market_depth_visualizer/models.py to push from 54% to 90%+ coverage.
"""

import pytest
from datetime import datetime, timezone
from src.forex_bot.market_depth_visualizer.models import (
    MarketDepthSnapshot, MarketDepthVisualization, MarketDepthDashboard,
    DashboardSettings, VisualizationType
)


class TestMarketDepthPhase6BClean:
    """Clean test to push market_depth_visualizer/models.py to 90%+ coverage"""

    def test_basic_validators(self):
        """Test basic validator edge cases"""
        
        now = datetime.now(timezone.utc)
        
        # Test empty bid prices
        with pytest.raises(ValueError):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[],
                bid_volumes=[],
                ask_prices=[1.2341],
                ask_volumes=[800.0]
            )
        
        # Test empty ask prices
        with pytest.raises(ValueError):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340],
                bid_volumes=[1000.0],
                ask_prices=[],
                ask_volumes=[]
            )

    def test_property_calculations(self):
        """Test property calculations"""
        
        now = datetime.now(timezone.utc)
        
        snapshot = MarketDepthSnapshot(
            symbol="EURUSD",
            timestamp=now,
            bid_prices=[1.2340, 1.2339],
            bid_volumes=[1000.0, 1500.0],
            ask_prices=[1.2341, 1.2342],
            ask_volumes=[800.0, 1200.0]
        )
        
        assert snapshot.best_bid == 1.2340
        assert snapshot.best_ask == 1.2341
        assert snapshot.total_bid_volume == 2500.0
        assert snapshot.total_ask_volume == 2000.0