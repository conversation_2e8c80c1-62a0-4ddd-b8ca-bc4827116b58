"""
PHASE 5P: Comprehensive tests for metrics/metrics_config.py to achieve 90%+ coverage.
Target: metrics/metrics_config.py (73% → 90%+)
"""

import pytest
import os
from unittest.mock import patch, MagicMock
from typing import Dict, Any


class TestMetricsConfigPhase5P:
    """Phase 5P: Comprehensive tests for metrics/metrics_config.py."""

    def test_prometheus_config_basic_functionality(self):
        """Test PrometheusConfig basic functionality."""
        from src.forex_bot.metrics.metrics_config import PrometheusConfig
        
        # Test default values
        config = PrometheusConfig()
        assert config.enabled == True
        assert config.port == 8000
        assert config.path == "/metrics"
        assert config.namespace == "forex_bot"
        assert config.labels == {}
        
        # Test custom values
        custom_config = PrometheusConfig(
            enabled=False,
            port=9090,
            path="/custom-metrics",
            namespace="custom_bot",
            labels={"env": "test", "version": "1.0"}
        )
        assert custom_config.enabled == False
        assert custom_config.port == 9090
        assert custom_config.path == "/custom-metrics"
        assert custom_config.namespace == "custom_bot"
        assert custom_config.labels == {"env": "test", "version": "1.0"}

    def test_prometheus_config_port_validation(self):
        """Test PrometheusConfig port validation."""
        from src.forex_bot.metrics.metrics_config import PrometheusConfig
        
        # Test valid ports
        valid_config = PrometheusConfig(port=8080)
        assert valid_config.port == 8080
        
        # Test edge cases
        edge_config_low = PrometheusConfig(port=0)
        assert edge_config_low.port == 0
        
        edge_config_high = PrometheusConfig(port=65535)
        assert edge_config_high.port == 65535
        
        # Test invalid ports
        with pytest.raises(ValueError, match="Port must be between 0 and 65535"):
            PrometheusConfig(port=-1)
        
        with pytest.raises(ValueError, match="Port must be between 0 and 65535"):
            PrometheusConfig(port=65536)

    def test_opentelemetry_config_basic_functionality(self):
        """Test OpenTelemetryConfig basic functionality."""
        from src.forex_bot.metrics.metrics_config import OpenTelemetryConfig
        
        # Test default values
        config = OpenTelemetryConfig()
        assert config.enabled == True
        assert config.service_name == "forex_bot"
        assert config.exporter_type == "console"
        assert config.exporter_endpoint is None
        assert config.sample_rate == 1.0
        
        # Test custom values
        custom_config = OpenTelemetryConfig(
            enabled=False,
            service_name="custom_service",
            exporter_type="jaeger",
            exporter_endpoint="http://localhost:14268/api/traces",
            sample_rate=0.5
        )
        assert custom_config.enabled == False
        assert custom_config.service_name == "custom_service"
        assert custom_config.exporter_type == "jaeger"
        assert custom_config.exporter_endpoint == "http://localhost:14268/api/traces"
        assert custom_config.sample_rate == 0.5

    def test_opentelemetry_config_exporter_type_validation(self):
        """Test OpenTelemetryConfig exporter type validation."""
        from src.forex_bot.metrics.metrics_config import OpenTelemetryConfig
        
        # Test valid exporter types
        for exporter_type in ["console", "jaeger", "otlp"]:
            config = OpenTelemetryConfig(exporter_type=exporter_type)
            assert config.exporter_type == exporter_type
        
        # Test invalid exporter type
        with pytest.raises(ValueError, match="Exporter type must be one of"):
            OpenTelemetryConfig(exporter_type="invalid")

    def test_opentelemetry_config_sample_rate_validation(self):
        """Test OpenTelemetryConfig sample rate validation."""
        from src.forex_bot.metrics.metrics_config import OpenTelemetryConfig
        
        # Test valid sample rates
        for sample_rate in [0.0, 0.5, 1.0]:
            config = OpenTelemetryConfig(sample_rate=sample_rate)
            assert config.sample_rate == sample_rate
        
        # Test invalid sample rates
        with pytest.raises(ValueError, match="Sample rate must be between 0.0 and 1.0"):
            OpenTelemetryConfig(sample_rate=-0.1)
        
        with pytest.raises(ValueError, match="Sample rate must be between 0.0 and 1.0"):
            OpenTelemetryConfig(sample_rate=1.1)

    def test_alerting_config_basic_functionality(self):
        """Test AlertingConfig basic functionality."""
        from src.forex_bot.metrics.metrics_config import AlertingConfig
        
        # Test default values
        config = AlertingConfig()
        assert config.enabled == True
        assert config.latency_threshold_ms == 200
        assert config.error_rate_threshold == 0.005
        assert config.consecutive_errors_threshold == 5
        assert config.alert_cooldown_seconds == 300
        
        # Test custom values
        custom_config = AlertingConfig(
            enabled=False,
            latency_threshold_ms=500,
            error_rate_threshold=0.01,
            consecutive_errors_threshold=10,
            alert_cooldown_seconds=600
        )
        assert custom_config.enabled == False
        assert custom_config.latency_threshold_ms == 500
        assert custom_config.error_rate_threshold == 0.01
        assert custom_config.consecutive_errors_threshold == 10
        assert custom_config.alert_cooldown_seconds == 600

    def test_metrics_config_basic_functionality(self):
        """Test MetricsConfig basic functionality."""
        from src.forex_bot.metrics.metrics_config import MetricsConfig, PrometheusConfig, OpenTelemetryConfig, AlertingConfig
        
        # Test default values
        config = MetricsConfig()
        assert config.enabled == True
        assert isinstance(config.prometheus, PrometheusConfig)
        assert isinstance(config.opentelemetry, OpenTelemetryConfig)
        assert isinstance(config.alerting, AlertingConfig)
        assert config.log_metrics == True
        assert config.collect_system_metrics == True
        assert config.collect_gc_metrics == True
        
        # Test custom values
        custom_prometheus = PrometheusConfig(port=9090)
        custom_opentelemetry = OpenTelemetryConfig(service_name="custom")
        custom_alerting = AlertingConfig(enabled=False)
        
        custom_config = MetricsConfig(
            enabled=False,
            prometheus=custom_prometheus,
            opentelemetry=custom_opentelemetry,
            alerting=custom_alerting,
            log_metrics=False,
            collect_system_metrics=False,
            collect_gc_metrics=False
        )
        assert custom_config.enabled == False
        assert custom_config.prometheus.port == 9090
        assert custom_config.opentelemetry.service_name == "custom"
        assert custom_config.alerting.enabled == False
        assert custom_config.log_metrics == False
        assert custom_config.collect_system_metrics == False
        assert custom_config.collect_gc_metrics == False

    @patch.dict(os.environ, {}, clear=True)
    def test_get_metrics_config_default_values(self):
        """Test get_metrics_config with default values."""
        from src.forex_bot.metrics.metrics_config import get_metrics_config, _metrics_config
        
        # Clear the singleton
        import src.forex_bot.metrics.metrics_config as config_module
        config_module._metrics_config = None
        
        # Mock get_app_config to avoid dependencies
        with patch('src.forex_bot.metrics.metrics_config.get_app_config') as mock_get_app_config:
            mock_app_config = MagicMock()
            mock_app_config.instance_id = "test_instance"
            mock_get_app_config.return_value = mock_app_config
            
            config = get_metrics_config()
            
            # Test default values
            assert config.enabled == True
            assert config.prometheus.enabled == True
            assert config.prometheus.port == 8000
            assert config.prometheus.path == "/metrics"
            assert config.prometheus.namespace == "forex_bot"
            assert config.prometheus.labels["instance"] == "test_instance"
            assert config.prometheus.labels["environment"] == "development"
            
            assert config.opentelemetry.enabled == True
            assert config.opentelemetry.service_name == "forex_bot"
            assert config.opentelemetry.exporter_type == "console"
            assert config.opentelemetry.exporter_endpoint is None
            assert config.opentelemetry.sample_rate == 1.0
            
            assert config.alerting.enabled == True
            assert config.alerting.latency_threshold_ms == 200
            assert config.alerting.error_rate_threshold == 0.005
            assert config.alerting.consecutive_errors_threshold == 5
            assert config.alerting.alert_cooldown_seconds == 300
            
            assert config.log_metrics == True
            assert config.collect_system_metrics == True
            assert config.collect_gc_metrics == True

    @patch.dict(os.environ, {
        "METRICS_ENABLED": "false",
        "PROMETHEUS_ENABLED": "false",
        "PROMETHEUS_PORT": "9090",
        "PROMETHEUS_PATH": "/custom-metrics",
        "PROMETHEUS_NAMESPACE": "custom_bot",
        "ENVIRONMENT": "production",
        "OPENTELEMETRY_ENABLED": "false",
        "OPENTELEMETRY_SERVICE_NAME": "custom_service",
        "OPENTELEMETRY_EXPORTER_TYPE": "jaeger",
        "OPENTELEMETRY_EXPORTER_ENDPOINT": "http://localhost:14268/api/traces",
        "OPENTELEMETRY_SAMPLE_RATE": "0.5",
        "ALERTING_ENABLED": "false",
        "ALERTING_LATENCY_THRESHOLD_MS": "500",
        "ALERTING_ERROR_RATE_THRESHOLD": "0.01",
        "ALERTING_CONSECUTIVE_ERRORS_THRESHOLD": "10",
        "ALERTING_COOLDOWN_SECONDS": "600",
        "LOG_METRICS": "false",
        "COLLECT_SYSTEM_METRICS": "false",
        "COLLECT_GC_METRICS": "false"
    }, clear=True)
    def test_get_metrics_config_environment_variables(self):
        """Test get_metrics_config with environment variables."""
        from src.forex_bot.metrics.metrics_config import get_metrics_config
        
        # Clear the singleton
        import src.forex_bot.metrics.metrics_config as config_module
        config_module._metrics_config = None
        
        # Mock get_app_config to avoid dependencies
        with patch('src.forex_bot.metrics.metrics_config.get_app_config') as mock_get_app_config:
            mock_app_config = MagicMock()
            mock_app_config.instance_id = "prod_instance"
            mock_get_app_config.return_value = mock_app_config
            
            config = get_metrics_config()
            
            # Test environment variable values
            assert config.enabled == False
            assert config.prometheus.enabled == False
            assert config.prometheus.port == 9090
            assert config.prometheus.path == "/custom-metrics"
            assert config.prometheus.namespace == "custom_bot"
            assert config.prometheus.labels["instance"] == "prod_instance"
            assert config.prometheus.labels["environment"] == "production"
            
            assert config.opentelemetry.enabled == False
            assert config.opentelemetry.service_name == "custom_service"
            assert config.opentelemetry.exporter_type == "jaeger"
            assert config.opentelemetry.exporter_endpoint == "http://localhost:14268/api/traces"
            assert config.opentelemetry.sample_rate == 0.5
            
            assert config.alerting.enabled == False
            assert config.alerting.latency_threshold_ms == 500
            assert config.alerting.error_rate_threshold == 0.01
            assert config.alerting.consecutive_errors_threshold == 10
            assert config.alerting.alert_cooldown_seconds == 600
            
            assert config.log_metrics == False
            assert config.collect_system_metrics == False
            assert config.collect_gc_metrics == False

    @patch.dict(os.environ, {
        "METRICS_ENABLED": "1",
        "PROMETHEUS_ENABLED": "yes",
        "OPENTELEMETRY_ENABLED": "true",
        "ALERTING_ENABLED": "1",
        "LOG_METRICS": "yes",
        "COLLECT_SYSTEM_METRICS": "true",
        "COLLECT_GC_METRICS": "1"
    }, clear=True)
    def test_get_metrics_config_boolean_variations(self):
        """Test get_metrics_config with various boolean representations."""
        from src.forex_bot.metrics.metrics_config import get_metrics_config
        
        # Clear the singleton
        import src.forex_bot.metrics.metrics_config as config_module
        config_module._metrics_config = None
        
        # Mock get_app_config to avoid dependencies
        with patch('src.forex_bot.metrics.metrics_config.get_app_config') as mock_get_app_config:
            mock_app_config = MagicMock()
            mock_app_config.instance_id = "test_instance"
            mock_get_app_config.return_value = mock_app_config
            
            config = get_metrics_config()
            
            # Test that various boolean representations work
            assert config.enabled == True
            assert config.prometheus.enabled == True
            assert config.opentelemetry.enabled == True
            assert config.alerting.enabled == True
            assert config.log_metrics == True
            assert config.collect_system_metrics == True
            assert config.collect_gc_metrics == True

    def test_get_metrics_config_singleton_behavior(self):
        """Test that get_metrics_config returns the same instance."""
        from src.forex_bot.metrics.metrics_config import get_metrics_config
        
        # Clear the singleton
        import src.forex_bot.metrics.metrics_config as config_module
        config_module._metrics_config = None
        
        # Mock get_app_config to avoid dependencies
        with patch('src.forex_bot.metrics.metrics_config.get_app_config') as mock_get_app_config:
            mock_app_config = MagicMock()
            mock_app_config.instance_id = "test_instance"
            mock_get_app_config.return_value = mock_app_config
            
            config1 = get_metrics_config()
            config2 = get_metrics_config()
            
            # Should be the same instance
            assert config1 is config2
            
            # get_app_config should only be called once due to singleton
            assert mock_get_app_config.call_count == 1

    def test_get_metrics_config_without_instance_id(self):
        """Test get_metrics_config when app_config doesn't have instance_id."""
        from src.forex_bot.metrics.metrics_config import get_metrics_config
        
        # Clear the singleton
        import src.forex_bot.metrics.metrics_config as config_module
        config_module._metrics_config = None
        
        # Mock get_app_config without instance_id
        with patch('src.forex_bot.metrics.metrics_config.get_app_config') as mock_get_app_config:
            mock_app_config = MagicMock()
            # Remove instance_id attribute
            del mock_app_config.instance_id
            mock_get_app_config.return_value = mock_app_config
            
            config = get_metrics_config()
            
            # Should use "default" when instance_id is not available
            assert config.prometheus.labels["instance"] == "default"

    @patch.dict(os.environ, {
        "PROMETHEUS_PORT": "invalid",
        "OPENTELEMETRY_SAMPLE_RATE": "invalid",
        "ALERTING_LATENCY_THRESHOLD_MS": "invalid",
        "ALERTING_ERROR_RATE_THRESHOLD": "invalid",
        "ALERTING_CONSECUTIVE_ERRORS_THRESHOLD": "invalid",
        "ALERTING_COOLDOWN_SECONDS": "invalid"
    }, clear=True)
    def test_get_metrics_config_invalid_environment_values(self):
        """Test get_metrics_config with invalid environment variable values."""
        from src.forex_bot.metrics.metrics_config import get_metrics_config
        
        # Clear the singleton
        import src.forex_bot.metrics.metrics_config as config_module
        config_module._metrics_config = None
        
        # Mock get_app_config to avoid dependencies
        with patch('src.forex_bot.metrics.metrics_config.get_app_config') as mock_get_app_config:
            mock_app_config = MagicMock()
            mock_get_app_config.return_value = mock_app_config
            
            # Should raise ValueError for invalid integer/float values
            with pytest.raises(ValueError):
                get_metrics_config()

    def test_config_field_descriptions(self):
        """Test that all config classes have proper field descriptions."""
        from src.forex_bot.metrics.metrics_config import (
            PrometheusConfig, OpenTelemetryConfig, AlertingConfig, MetricsConfig
        )
        
        # Test PrometheusConfig field descriptions (using model_fields for Pydantic v2)
        prometheus_fields = PrometheusConfig.model_fields
        assert prometheus_fields["enabled"].description is not None
        assert prometheus_fields["port"].description is not None
        assert prometheus_fields["path"].description is not None
        assert prometheus_fields["namespace"].description is not None
        assert prometheus_fields["labels"].description is not None
        
        # Test OpenTelemetryConfig field descriptions
        otel_fields = OpenTelemetryConfig.model_fields
        assert otel_fields["enabled"].description is not None
        assert otel_fields["service_name"].description is not None
        assert otel_fields["exporter_type"].description is not None
        assert otel_fields["exporter_endpoint"].description is not None
        assert otel_fields["sample_rate"].description is not None
        
        # Test AlertingConfig field descriptions
        alerting_fields = AlertingConfig.model_fields
        assert alerting_fields["enabled"].description is not None
        assert alerting_fields["latency_threshold_ms"].description is not None
        assert alerting_fields["error_rate_threshold"].description is not None
        assert alerting_fields["consecutive_errors_threshold"].description is not None
        assert alerting_fields["alert_cooldown_seconds"].description is not None
        
        # Test MetricsConfig field descriptions
        metrics_fields = MetricsConfig.model_fields
        assert metrics_fields["enabled"].description is not None
        assert metrics_fields["prometheus"].description is not None
        assert metrics_fields["opentelemetry"].description is not None
        assert metrics_fields["alerting"].description is not None
        assert metrics_fields["log_metrics"].description is not None
        assert metrics_fields["collect_system_metrics"].description is not None
        assert metrics_fields["collect_gc_metrics"].description is not None

    def test_config_edge_cases(self):
        """Test edge cases for configuration classes."""
        from src.forex_bot.metrics.metrics_config import (
            PrometheusConfig, OpenTelemetryConfig, AlertingConfig
        )
        
        # Test PrometheusConfig with empty labels
        prometheus_config = PrometheusConfig(labels={})
        assert prometheus_config.labels == {}
        
        # Test PrometheusConfig with complex labels
        complex_labels = {
            "env": "production",
            "version": "1.0.0",
            "region": "us-east-1",
            "team": "trading"
        }
        prometheus_config = PrometheusConfig(labels=complex_labels)
        assert prometheus_config.labels == complex_labels
        
        # Test OpenTelemetryConfig with None endpoint
        otel_config = OpenTelemetryConfig(exporter_endpoint=None)
        assert otel_config.exporter_endpoint is None
        
        # Test OpenTelemetryConfig with empty string endpoint
        otel_config = OpenTelemetryConfig(exporter_endpoint="")
        assert otel_config.exporter_endpoint == ""
        
        # Test AlertingConfig with zero values
        alerting_config = AlertingConfig(
            latency_threshold_ms=0,
            error_rate_threshold=0.0,
            consecutive_errors_threshold=0,
            alert_cooldown_seconds=0
        )
        assert alerting_config.latency_threshold_ms == 0
        assert alerting_config.error_rate_threshold == 0.0
        assert alerting_config.consecutive_errors_threshold == 0
        assert alerting_config.alert_cooldown_seconds == 0