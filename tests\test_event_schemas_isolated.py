"""
Isolated tests for the event_bus.event_schemas module.
"""

import pytest
import sys
import os
from datetime import datetime, timezone
from pydantic import ValidationError

# Add the specific module path directly
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src', 'forex_bot', 'event_bus'))

# Import the module directly
import event_schemas

# Extract the classes we need
BaseEvent = event_schemas.BaseEvent
MarketDataEvent = event_schemas.MarketDataEvent
OrderEvent = event_schemas.OrderEvent
TradeEvent = event_schemas.TradeEvent
AnalysisEvent = event_schemas.AnalysisEvent
EventType = event_schemas.EventType
TimeFrame = event_schemas.TimeFrame
OrderType = event_schemas.OrderType
OrderStatus = event_schemas.OrderStatus
TradeStatus = event_schemas.TradeStatus
AnalysisType = event_schemas.AnalysisType
OHLCVData = event_schemas.OHLCVData


class TestMarketDataEventValidation:
    """Tests for MarketDataEvent validation that covers missing lines 74-79."""

    def test_market_data_event_validation_error_tick_not_dict(self):
        """Test validation error when tick data is not a dictionary (covers line 75-76)."""
        # Create an OHLCVData object that will pass Union validation but fail custom validation for tick
        ohlcv_data = OHLCVData(
            open=1.1234,
            high=1.1240,
            low=1.1230,
            close=1.1235,
            volume=100.0,
            time=datetime(2023, 1, 1, 12, 0, 0, tzinfo=timezone.utc)
        )

        with pytest.raises(ValidationError, match="Tick data must be a dictionary"):
            MarketDataEvent(
                event_id="test-id",
                source="test",
                symbol="EURUSD",
                timeframe=TimeFrame.M1,
                data_type="tick",
                data=ohlcv_data  # This passes Union validation but should fail custom validation
            )

    def test_market_data_event_validation_error_ohlcv_wrong_type(self):
        """Test validation error when OHLCV data is not OHLCVData or list (covers line 77-78)."""
        # Create a dict that will pass Union validation but fail custom validation for ohlcv
        tick_data = {"bid": 1.1234, "ask": 1.1235}

        with pytest.raises(ValidationError, match="OHLCV data must be an OHLCVData object or a list of OHLCVData objects"):
            MarketDataEvent(
                event_id="test-id",
                source="test",
                symbol="EURUSD",
                timeframe=TimeFrame.M1,
                data_type="ohlcv",
                data=tick_data  # This passes Union validation but should fail custom validation
            )

    def test_market_data_event_with_valid_tick(self):
        """Test creating a MarketDataEvent with valid tick data (covers line 79 - return v)."""
        event = MarketDataEvent(
            event_id="test-id",
            source="test",
            symbol="EURUSD",
            timeframe=TimeFrame.M1,
            data_type="tick",
            data={"bid": 1.1234, "ask": 1.1235}  # Valid dict for tick data
        )

        assert event.event_id == "test-id"
        assert event.event_type == EventType.MARKET_DATA
        assert event.source == "test"
        assert event.symbol == "EURUSD"
        assert event.timeframe == TimeFrame.M1
        assert event.data_type == "tick"
        assert event.data["bid"] == 1.1234
        assert event.data["ask"] == 1.1235

    def test_market_data_event_with_valid_ohlcv(self):
        """Test creating a MarketDataEvent with valid OHLCV data."""
        ohlcv_data = OHLCVData(
            open=1.1234,
            high=1.1240,
            low=1.1230,
            close=1.1235,
            volume=100.0,
            time=datetime(2023, 1, 1, 12, 0, 0, tzinfo=timezone.utc)
        )

        event = MarketDataEvent(
            event_id="test-id",
            source="test",
            symbol="EURUSD",
            timeframe=TimeFrame.M1,
            data_type="ohlcv",
            data=ohlcv_data
        )

        assert event.event_id == "test-id"
        assert event.event_type == EventType.MARKET_DATA
        assert event.source == "test"
        assert event.symbol == "EURUSD"
        assert event.timeframe == TimeFrame.M1
        assert event.data_type == "ohlcv"
        assert event.data.open == 1.1234
        assert event.data.high == 1.1240
        assert event.data.low == 1.1230
        assert event.data.close == 1.1235
        assert event.data.volume == 100.0

    def test_market_data_event_with_valid_ohlcv_list(self):
        """Test creating a MarketDataEvent with a list of OHLCV data."""
        ohlcv_data_list = [
            OHLCVData(
                open=1.1234,
                high=1.1240,
                low=1.1230,
                close=1.1235,
                volume=100.0,
                time=datetime(2023, 1, 1, 12, 0, 0, tzinfo=timezone.utc)
            ),
            OHLCVData(
                open=1.1235,
                high=1.1245,
                low=1.1233,
                close=1.1240,
                volume=120.0,
                time=datetime(2023, 1, 1, 12, 5, 0, tzinfo=timezone.utc)
            )
        ]

        event = MarketDataEvent(
            event_id="test-id",
            source="test",
            symbol="EURUSD",
            timeframe=TimeFrame.M5,
            data_type="ohlcv",
            data=ohlcv_data_list
        )

        assert event.event_id == "test-id"
        assert event.event_type == EventType.MARKET_DATA
        assert event.source == "test"
        assert event.symbol == "EURUSD"
        assert event.timeframe == TimeFrame.M5
        assert event.data_type == "ohlcv"
        assert len(event.data) == 2
        assert event.data[0].open == 1.1234
        assert event.data[1].close == 1.1240


class TestBaseEvent:
    """Tests for the BaseEvent class."""

    def test_base_event_creation(self):
        """Test creating a BaseEvent."""
        event = BaseEvent(
            event_id="test-id",
            event_type=EventType.SYSTEM,
            source="test"
        )

        assert event.event_id == "test-id"
        assert event.event_type == EventType.SYSTEM
        assert event.source == "test"
        assert event.version == "1.0"
        assert isinstance(event.timestamp, datetime)


class TestOHLCVData:
    """Tests for the OHLCVData class."""

    def test_ohlcv_data_creation(self):
        """Test creating OHLCVData."""
        ohlcv = OHLCVData(
            open=1.1234,
            high=1.1240,
            low=1.1230,
            close=1.1235,
            volume=100.0,
            time=datetime(2023, 1, 1, 12, 0, 0, tzinfo=timezone.utc)
        )

        assert ohlcv.open == 1.1234
        assert ohlcv.high == 1.1240
        assert ohlcv.low == 1.1230
        assert ohlcv.close == 1.1235
        assert ohlcv.volume == 100.0
        assert ohlcv.time == datetime(2023, 1, 1, 12, 0, 0, tzinfo=timezone.utc)
