"""
Phase 5FF: Simple test for market_depth_visualizer/models.py to push from 71% to 90%+ coverage.
"""

import pytest
from datetime import datetime, timezone
from unittest.mock import patch
from src.forex_bot.market_depth_visualizer.models import MarketDepthSnapshot


class TestMarketDepthPhase5FFSimple:
    """Simple test to push market_depth_visualizer/models.py to 90%+ coverage"""

    def test_numpy_unavailable_fallback(self):
        """Test numpy unavailable fallback for cumulative volumes"""
        
        # Test with numpy unavailable to trigger fallback implementation
        with patch('src.forex_bot.market_depth_visualizer.models.NUMPY_AVAILABLE', False):
            # Force module reload to use fallback implementation
            import importlib
            import src.forex_bot.market_depth_visualizer.models as models_module
            importlib.reload(models_module)
            
            now = datetime.now(timezone.utc)
            snapshot = models_module.MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340, 1.2339, 1.2338],
                bid_volumes=[1000.0, 1500.0, 2000.0],
                ask_prices=[1.2341, 1.2342, 1.2343],
                ask_volumes=[800.0, 1200.0, 1600.0]
            )
            
            # Test cumulative volumes without numpy (should use manual calculation)
            cumulative_bids = snapshot.cumulative_bid_volumes
            assert len(cumulative_bids) == 3
            assert cumulative_bids[0] == 1000.0
            assert cumulative_bids[1] == 2500.0  # 1000 + 1500
            assert cumulative_bids[2] == 4500.0  # 1000 + 1500 + 2000
            
            cumulative_asks = snapshot.cumulative_ask_volumes
            assert len(cumulative_asks) == 3
            assert cumulative_asks[0] == 800.0
            assert cumulative_asks[1] == 2000.0  # 800 + 1200
            assert cumulative_asks[2] == 3600.0  # 800 + 1200 + 1600

    def test_zero_volume_edge_case(self):
        """Test zero volume edge case for imbalance ratio"""
        
        now = datetime.now(timezone.utc)
        
        # Test zero total volume edge case (line 225)
        snapshot = MarketDepthSnapshot(
            symbol="GBPUSD",
            timestamp=now,
            bid_prices=[1.3000],
            bid_volumes=[0.0],  # Zero volume
            ask_prices=[1.3001],
            ask_volumes=[0.0]   # Zero volume
        )
        
        # This should trigger the zero volume check in imbalance_ratio calculation
        assert snapshot.imbalance_ratio is None