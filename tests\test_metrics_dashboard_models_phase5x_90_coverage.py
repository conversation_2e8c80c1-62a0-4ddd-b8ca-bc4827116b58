"""
Phase 5X: Comprehensive test for metrics_dashboard/models.py to achieve 90%+ coverage.
Targeting specific missing lines: 14-15, 20-21, 26-35, 82, 96-103, 107-110, 192, 249, 251, 253, 273, 281
"""

import pytest
import sys
from unittest.mock import patch
from datetime import datetime, timedelta
import numpy as np
import pandas as pd

# Import the models module
from src.forex_bot.metrics_dashboard.models import (
    TimeFrame, MetricCategory, ChartType, AlertSeverity,
    MetricValue, MetricTimeSeries, PerformanceMetrics, TradeMetrics,
    MarketMetrics, SystemMetrics, ChartData, DashboardLayout, Dashboard
)


class TestMetricsDashboardModelsPhase5X90Coverage:
    """Comprehensive test class to achieve 90%+ coverage for metrics_dashboard/models.py"""

    def test_import_error_handling_numpy_pandas_pydantic(self):
        """Test import error handling for numpy, pandas, and pydantic (lines 14-15, 20-21, 26-35)"""
        
        # Test numpy import error handling
        with patch.dict('sys.modules', {'numpy': None}):
            import importlib
            import src.forex_bot.metrics_dashboard.models as models_module
            importlib.reload(models_module)
            assert not models_module.NUMPY_AVAILABLE
        
        # Test pandas import error handling
        with patch.dict('sys.modules', {'pandas': None}):
            import importlib
            import src.forex_bot.metrics_dashboard.models as models_module
            importlib.reload(models_module)
            assert not models_module.PANDAS_AVAILABLE
        
        # Test pydantic import error handling
        with patch.dict('sys.modules', {'pydantic': None}):
            import importlib
            import src.forex_bot.metrics_dashboard.models as models_module
            importlib.reload(models_module)
            assert not models_module.PYDANTIC_AVAILABLE
            
            # Test fallback BaseModel functionality
            fallback_model = models_module.BaseModel(test_attr="test_value")
            assert fallback_model.test_attr == "test_value"
            assert fallback_model.dict() == {"test_attr": "test_value"}

    def test_metric_value_numpy_validation(self):
        """Test MetricValue validation with numpy NaN and Inf values (line 82)"""
        
        # Test with NaN value
        with pytest.raises(ValueError, match="value cannot be NaN or Inf"):
            MetricValue(
                name="test_metric",
                value=float('nan'),
                unit="units",
                timestamp=datetime.now(),
                category=MetricCategory.PERFORMANCE
            )