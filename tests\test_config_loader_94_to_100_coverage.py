"""
Test file to push config_loader.py from 94% to 100% coverage.
Focuses on the remaining 13 missing lines: 73, 100-102, 113, 318-320, 329-331, 334-335.
"""

import pytest
import os
import sys
from unittest.mock import patch, MagicMock
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from forex_bot.config_loader import Config


class TestConfigLoaderMissingLines:
    """Tests to cover the remaining missing lines in config_loader.py."""

    def test_config_class_name_error_fallback_lines_318_320(self):
        """Test NameError fallback in Config class (covers lines 318-320)."""
        config = Config()

        # Mock globals() to not contain '__file__'
        with patch('builtins.globals', return_value={}):
            with patch('os.getcwd', return_value='/test/cwd'):
                # Call _calculate_paths to trigger the NameError handling
                config._calculate_paths()

                # Should not raise an error and should use fallback
                assert config.knowledge_base_strategy_file_path is not None

    def test_config_class_project_marker_not_found_lines_329_331(self, capsys):
        """Test project marker not found in Config class (covers lines 329-331)."""
        config = Config()

        # Mock path traversal to never find markers and hit parent == current
        with patch('os.path.exists', return_value=False):
            with patch('os.path.dirname', side_effect=lambda x: x):  # Same directory
                with patch('os.getcwd', return_value='/test/cwd'):
                    config._calculate_paths()

                    # Check that the warning was printed
                    captured = capsys.readouterr()
                    assert "[WARN] KB Path: Could not find project marker" in captured.out

    def test_config_class_for_loop_else_lines_334_335(self, capsys):
        """Test for loop else clause in Config class (covers lines 334-335)."""
        config = Config()

        # Mock path traversal to exhaust the range without finding markers
        def mock_dirname(path):
            # Return different paths to avoid the parent == current condition
            if path == '/test/level4':
                return '/test/level3'
            elif path == '/test/level3':
                return '/test/level2'
            elif path == '/test/level2':
                return '/test/level1'
            elif path == '/test/level1':
                return '/test/level0'
            else:
                return '/test/root'

        with patch('os.path.exists', return_value=False):
            with patch('os.path.dirname', side_effect=mock_dirname):
                with patch('os.getcwd', return_value='/test/cwd'):
                    # Mock the script_dir to start the traversal
                    with patch('os.path.abspath', return_value='/test/level4'):
                        config._calculate_paths()

                        # Check that the else clause warning was printed
                        captured = capsys.readouterr()
                        assert "[WARN] KB Path: Project marker not found" in captured.out

    def test_comprehensive_edge_cases(self):
        """Test multiple edge cases together to ensure robustness."""
        config = Config()

        # Test with various edge conditions
        with patch('os.path.exists', return_value=False):
            with patch('os.getcwd', return_value='/fallback/path'):
                config._calculate_paths()

                # Should complete without errors
                assert config.knowledge_base_strategy_file_path is not None
                assert config.knowledge_base_metrics_file_path is not None

    def test_env_int_invalid_value_line_281_282(self, capsys):
        """Test invalid integer environment variable (covers lines 281-282)."""
        config = Config()

        with patch.dict(os.environ, {'TEST_INT': 'invalid_int'}):
            result = config._get_env_int('TEST_INT', 42)

            # Should return default and print warning
            assert result == 42
            captured = capsys.readouterr()
            assert "[WARNING] Invalid integer value for TEST_INT: invalid_int" in captured.out

    def test_env_float_invalid_value_line_296_298(self, capsys):
        """Test invalid float environment variable (covers lines 296-298)."""
        config = Config()

        with patch.dict(os.environ, {'TEST_FLOAT': 'invalid_float'}):
            result = config._get_env_float('TEST_FLOAT', 3.14)

            # Should return default and print warning
            assert result == 3.14
            captured = capsys.readouterr()
            assert "[WARNING] Invalid float value for TEST_FLOAT: invalid_float" in captured.out

    def test_symbols_parsing_error_line_239(self, capsys):
        """Test symbols parsing error (covers line 239)."""
        config = Config()

        # Create a mock string that will raise an exception when split is called
        class MockString(str):
            def split(self, *args, **kwargs):  # noqa: ARG002
                raise Exception("Mock parsing error")

        # Mock the environment variable to return our mock string
        mock_symbols = MockString('EURUSD,USDJPY')
        with patch.dict(os.environ, {'SYMBOLS': mock_symbols}):
            with patch('os.getenv', return_value=mock_symbols):
                config._load_from_env()

                # Should print warning and use defaults
                captured = capsys.readouterr()
                assert "[WARNING] Invalid SYMBOLS format" in captured.out
