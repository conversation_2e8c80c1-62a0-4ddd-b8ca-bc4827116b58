#!/usr/bin/env python3
"""
QA Wolf Enhancement Suite - Installation Verification Script

This script verifies that all QA Wolf components are properly installed
and ready for use with your Forex Trading Bot.

SAFETY LEVEL: MAXIMUM - Verification only, no trading impact
"""

import sys
import importlib
from pathlib import Path

def check_python_version():
    """Check Python version compatibility."""
    version = sys.version_info
    if version.major >= 3 and version.minor >= 8:
        print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
        return True
    else:
        print(f"❌ Python {version.major}.{version.minor}.{version.micro} - Requires Python 3.8+")
        return False

def check_dependencies():
    """Check required Python dependencies."""
    dependencies = [
        ('psutil', 'System monitoring'),
        ('flask', 'Web dashboard framework'),
        ('flask_socketio', 'Real-time updates'),
        ('requests', 'HTTP testing'),
        ('sqlite3', 'Database storage'),
        ('json', 'Data processing'),
        ('threading', 'Parallel execution'),
        ('datetime', 'Time handling'),
        ('pathlib', 'File operations')
    ]
    
    all_good = True
    
    for module, description in dependencies:
        try:
            importlib.import_module(module)
            print(f"✅ {module} - {description}")
        except ImportError:
            print(f"❌ {module} - {description} (MISSING)")
            all_good = False
    
    return all_good

def check_file_structure():
    """Check QA Wolf file structure."""
    base_path = Path(__file__).parent.parent
    
    required_files = [
        'README.md',
        'requirements.txt',
        'CLIENT_DELIVERY_GUIDE.md',
        'scripts/start_monitoring.py',
        'scripts/start_dashboard.py',
        'scripts/run_api_tests.py',
        'core/monitoring_overlay.py',
        'core/web_dashboard.py',
        'core/api_integration_tests.py',
        'templates/dashboard.html'
    ]
    
    all_good = True
    
    for file_path in required_files:
        full_path = base_path / file_path
        if full_path.exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} (MISSING)")
            all_good = False
    
    return all_good

def check_core_modules():
    """Check if core QA Wolf modules can be imported."""
    sys.path.append(str(Path(__file__).parent.parent / "core"))
    
    modules = [
        ('monitoring_overlay', 'Real-time monitoring'),
        ('api_integration_tests', 'API testing suite'),
        ('coverage_analysis', 'Coverage analysis'),
        ('documentation_generator', 'Documentation generator')
    ]
    
    all_good = True
    
    for module, description in modules:
        try:
            importlib.import_module(module)
            print(f"✅ {module} - {description}")
        except ImportError as e:
            print(f"❌ {module} - {description} (ERROR: {e})")
            all_good = False
    
    return all_good

def check_directories():
    """Check required directories exist."""
    base_path = Path(__file__).parent.parent
    
    directories = [
        'scripts',
        'core', 
        'docs',
        'reports',
        'config',
        'data',
        'templates'
    ]
    
    all_good = True
    
    for directory in directories:
        dir_path = base_path / directory
        if dir_path.exists() and dir_path.is_dir():
            print(f"✅ {directory}/ directory")
        else:
            print(f"❌ {directory}/ directory (MISSING)")
            all_good = False
    
    return all_good

def main():
    """Run complete installation verification."""
    
    print("🔍 QA Wolf Enhancement Suite - Installation Verification")
    print("🛡️ SAFETY: Verification only, no trading impact")
    print("=" * 60)
    
    all_checks_passed = True
    
    # Check Python version
    print("\n📋 Checking Python Version...")
    if not check_python_version():
        all_checks_passed = False
    
    # Check dependencies
    print("\n📦 Checking Dependencies...")
    if not check_dependencies():
        all_checks_passed = False
        print("\n💡 Install missing dependencies with:")
        print("   pip install -r requirements.txt")
    
    # Check file structure
    print("\n📁 Checking File Structure...")
    if not check_file_structure():
        all_checks_passed = False
    
    # Check directories
    print("\n📂 Checking Directories...")
    if not check_directories():
        all_checks_passed = False
    
    # Check core modules
    print("\n🔧 Checking Core Modules...")
    if not check_core_modules():
        all_checks_passed = False
    
    # Final result
    print("\n" + "=" * 60)
    if all_checks_passed:
        print("✅ INSTALLATION VERIFICATION SUCCESSFUL!")
        print("")
        print("🚀 Your QA Wolf Enhancement Suite is ready to use!")
        print("")
        print("Next steps:")
        print("1. Start monitoring: python scripts/start_monitoring.py")
        print("2. Launch dashboard: python scripts/start_dashboard.py")
        print("3. Run API tests: python scripts/run_api_tests.py")
        print("")
        print("🌐 Dashboard will be available at: http://localhost:5000")
        print("🛡️ Safety guaranteed: Zero trading impact")
    else:
        print("❌ INSTALLATION VERIFICATION FAILED!")
        print("")
        print("Please fix the issues above before proceeding.")
        print("Check the installation guide: docs/installation_guide.md")
    
    print("=" * 60)
    
    return all_checks_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)