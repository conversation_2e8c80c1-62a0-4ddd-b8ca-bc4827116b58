"""
Enhanced tests to push market_depth_visualizer/models.py to 90%+ coverage.

This module targets all remaining uncovered lines to achieve comprehensive coverage.
"""

import pytest
from unittest.mock import patch
from datetime import datetime, timezone

class TestMarketDepthVisualizerModelsEnhanced:
    """Enhanced tests to achieve 90%+ coverage for market_depth_visualizer/models.py."""

    def test_import_error_handling_numpy(self):
        """Test numpy import error handling."""
        with patch('src.forex_bot.market_depth_visualizer.models.NUMPY_AVAILABLE', False):
            import importlib
            from src.forex_bot.market_depth_visualizer import models
            importlib.reload(models)
            
            # Test that NUMPY_AVAILABLE is False
            assert not models.NUMPY_AVAILABLE
            
            # Test cumulative calculations without numpy
            snapshot = models.MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=datetime.now(timezone.utc),
                bid_prices=[1.1000, 1.0999],
                bid_volumes=[100.0, 200.0],
                ask_prices=[1.1001, 1.1002],
                ask_volumes=[150.0, 250.0]
            )
            
            # Test cumulative bid volumes without numpy
            cumulative_bid = snapshot.cumulative_bid_volumes
            assert cumulative_bid == [100.0, 300.0]
            
            # Test cumulative ask volumes without numpy
            cumulative_ask = snapshot.cumulative_ask_volumes
            assert cumulative_ask == [150.0, 400.0]

    def test_market_depth_snapshot_properties(self):
        """Test MarketDepthSnapshot property calculations."""
        from src.forex_bot.market_depth_visualizer import models
        
        # Test with valid data
        snapshot = models.MarketDepthSnapshot(
            symbol="EURUSD",
            timestamp=datetime.now(timezone.utc),
            bid_prices=[1.1000, 1.0999, 1.0998],
            bid_volumes=[100.0, 200.0, 150.0],
            ask_prices=[1.1001, 1.1002, 1.1003],
            ask_volumes=[150.0, 250.0, 100.0]
        )
        
        # Test best bid/ask
        assert snapshot.best_bid == 1.1000
        assert snapshot.best_ask == 1.1001        
        # Test spread
        assert snapshot.spread == 0.0001
        
        # Test mid price
        assert snapshot.mid_price == 1.10005
        
        # Test total volumes
        assert snapshot.total_bid_volume == 450.0
        assert snapshot.total_ask_volume == 500.0
        
        # Test imbalance ratio
        expected_imbalance = (450.0 - 500.0) / (450.0 + 500.0)
        assert abs(snapshot.imbalance_ratio - expected_imbalance) < 1e-10
        
        # Test with empty data
        empty_snapshot = models.MarketDepthSnapshot(
            symbol="EURUSD",
            timestamp=datetime.now(timezone.utc),
            bid_prices=[],
            bid_volumes=[],
            ask_prices=[],
            ask_volumes=[]
        )
        
        assert empty_snapshot.best_bid is None
        assert empty_snapshot.best_ask is None
        assert empty_snapshot.spread is None
        assert empty_snapshot.mid_price is None
        assert empty_snapshot.imbalance_ratio is None

    def test_market_depth_snapshot_validation_errors(self):
        """Test MarketDepthSnapshot validation errors."""
        from src.forex_bot.market_depth_visualizer import models
        from pydantic import ValidationError
        
        timestamp = datetime.now(timezone.utc)
        
        # Test empty bid prices validation
        with pytest.raises(ValidationError) as exc_info:
            models.MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=timestamp,
                bid_prices=[],
                bid_volumes=[],
                ask_prices=[1.1001],
                ask_volumes=[100.0]
            )
        assert "Bid prices cannot be empty" in str(exc_info.value)
        
        # Test empty ask prices validation
        with pytest.raises(ValidationError) as exc_info:
            models.MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=timestamp,
                bid_prices=[1.1000],
                bid_volumes=[100.0],
                ask_prices=[],
                ask_volumes=[]
            )
        assert "Ask prices cannot be empty" in str(exc_info.value)