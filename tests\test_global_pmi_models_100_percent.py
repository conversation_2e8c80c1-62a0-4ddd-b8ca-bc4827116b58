"""
Targeted tests to push global_pmi/models.py to 100% coverage.
"""

from datetime import date
from src.forex_bot.global_pmi.models import PMIData, PMITrend, PMIDivergence


def test_pmi_data_post_init_change_calculation():
    """Test PMIData __post_init__ change calculation - covers lines 35-40."""
    # Test with previous value provided but no change
    pmi_data = PMIData(
        country='United States',
        date=date(2023, 1, 1),
        pmi_value=52.5,
        pmi_type='Manufacturing',
        previous_value=50.0,
        change=None  # Should be calculated automatically
    )

    # Check that change was calculated (covers lines 35-40)
    assert pmi_data.change == 2.5  # 52.5 - 50.0
    assert pmi_data.is_expansion is True  # PMI > 50
    assert pmi_data.is_contraction is False  # PMI > 50


def test_pmi_trend_economic_state_strongly_expanding():
    """Test economic_state property for strongly expanding - covers lines 87-88."""
    # Create PMITrend with expansion and up trend
    pmi_trend = PMITrend(
        country='United States',
        pmi_type='Manufacturing',
        latest_date=date(2023, 1, 1),
        latest_value=52.5,  # Above 50 = expansion
        trend_direction='up',  # Upward trend
        trend_strength=0.8,
        values_3m=[52.5, 51.8, 51.2],
        values_6m=[52.5, 51.8, 51.2, 50.8, 50.5, 50.2],
        values_12m=[52.5, 51.8, 51.2, 50.8, 50.5, 50.2, 49.8, 49.5, 49.2, 48.8, 48.5, 48.2],
        avg_3m=51.83,
        avg_6m=51.17,
        avg_12m=50.25,
        is_improving=True,
        is_deteriorating=False,
        is_above_3m_avg=True,
        is_above_6m_avg=True,
        is_above_12m_avg=True
    )

    # Check economic_state property - should return "Strongly Expanding"
    assert pmi_trend.economic_state == "Strongly Expanding"


def test_pmi_trend_economic_state_moderately_expanding():
    """Test economic_state property for moderately expanding - covers lines 89-90."""
    # Create PMITrend with expansion and down trend
    pmi_trend = PMITrend(
        country='United States',
        pmi_type='Manufacturing',
        latest_date=date(2023, 1, 1),
        latest_value=51.5,  # Above 50 = expansion
        trend_direction='down',  # Downward trend
        trend_strength=0.3,
        values_3m=[51.5, 52.2, 52.8],
        values_6m=[51.5, 52.2, 52.8, 53.2, 53.5, 53.8],
        values_12m=[51.5, 52.2, 52.8, 53.2, 53.5, 53.8, 54.2, 54.5, 54.8, 55.2, 55.5, 55.8],
        avg_3m=52.17,
        avg_6m=52.83,
        avg_12m=53.75,
        is_improving=False,
        is_deteriorating=True,
        is_above_3m_avg=False,
        is_above_6m_avg=False,
        is_above_12m_avg=False
    )

    # Check economic_state property - should return "Moderately Expanding"
    assert pmi_trend.economic_state == "Moderately Expanding"


def test_pmi_trend_economic_state_stable_expansion():
    """Test economic_state property for stable expansion - covers lines 91-92."""
    # Create PMITrend with expansion and flat trend
    pmi_trend = PMITrend(
        country='United States',
        pmi_type='Manufacturing',
        latest_date=date(2023, 1, 1),
        latest_value=52.0,  # Above 50 = expansion
        trend_direction='flat',  # Flat trend (neither up nor down)
        trend_strength=0.1,
        values_3m=[52.0, 51.9, 52.1],
        values_6m=[52.0, 51.9, 52.1, 51.8, 52.2, 52.0],
        values_12m=[52.0, 51.9, 52.1, 51.8, 52.2, 52.0, 51.9, 52.1, 51.8, 52.2, 52.0, 51.9],
        avg_3m=52.0,
        avg_6m=52.0,
        avg_12m=52.0,
        is_improving=False,
        is_deteriorating=False,
        is_above_3m_avg=True,
        is_above_6m_avg=True,
        is_above_12m_avg=True
    )

    # Check economic_state property - should return "Stable Expansion"
    assert pmi_trend.economic_state == "Stable Expansion"


def test_pmi_trend_economic_state_improving_contraction():
    """Test economic_state property for improving contraction - covers line 95."""
    # Create PMITrend with contraction and up trend
    pmi_trend = PMITrend(
        country='United States',
        pmi_type='Manufacturing',
        latest_date=date(2023, 1, 1),
        latest_value=48.5,  # Below 50 = contraction
        trend_direction='up',  # Upward trend (improving)
        trend_strength=0.6,
        values_3m=[48.5, 47.8, 47.2],
        values_6m=[48.5, 47.8, 47.2, 46.8, 46.5, 46.2],
        values_12m=[48.5, 47.8, 47.2, 46.8, 46.5, 46.2, 45.8, 45.5, 45.2, 44.8, 44.5, 44.2],
        avg_3m=47.83,
        avg_6m=47.17,
        avg_12m=46.25,
        is_improving=True,
        is_deteriorating=False,
        is_above_3m_avg=True,
        is_above_6m_avg=True,
        is_above_12m_avg=True
    )

    # Check economic_state property - should return "Improving Contraction"
    assert pmi_trend.economic_state == "Improving Contraction"


def test_pmi_trend_economic_state_deepening_contraction():
    """Test economic_state property for deepening contraction - covers line 96."""
    # Create PMITrend with contraction and down trend
    pmi_trend = PMITrend(
        country='United States',
        pmi_type='Manufacturing',
        latest_date=date(2023, 1, 1),
        latest_value=48.5,  # Below 50 = contraction
        trend_direction='down',  # Downward trend
        trend_strength=0.7,
        values_3m=[48.5, 49.2, 49.8],
        values_6m=[48.5, 49.2, 49.8, 50.2, 50.5, 50.8],
        values_12m=[48.5, 49.2, 49.8, 50.2, 50.5, 50.8, 51.2, 51.5, 51.8, 52.2, 52.5, 52.8],
        avg_3m=49.17,
        avg_6m=49.83,
        avg_12m=50.75,
        is_improving=False,
        is_deteriorating=True,
        is_above_3m_avg=False,
        is_above_6m_avg=False,
        is_above_12m_avg=False
    )

    # Check economic_state property - should return "Deepening Contraction"
    assert pmi_trend.economic_state == "Deepening Contraction"


def test_pmi_trend_economic_state_stable_contraction():
    """Test economic_state property for stable contraction - covers line 98."""
    # Create PMITrend with contraction and flat trend
    pmi_trend = PMITrend(
        country='United States',
        pmi_type='Manufacturing',
        latest_date=date(2023, 1, 1),
        latest_value=48.5,  # Below 50 = contraction
        trend_direction='flat',  # Flat trend (neither up nor down)
        trend_strength=0.0,
        values_3m=[48.5, 48.4, 48.6],
        values_6m=[48.5, 48.4, 48.6, 48.3, 48.7, 48.5],
        values_12m=[48.5, 48.4, 48.6, 48.3, 48.7, 48.5, 48.4, 48.6, 48.3, 48.7, 48.5, 48.4],
        avg_3m=48.5,
        avg_6m=48.5,
        avg_12m=48.5,
        is_improving=False,
        is_deteriorating=False,
        is_above_3m_avg=True,
        is_above_6m_avg=True,
        is_above_12m_avg=True
    )

    # Check economic_state property - should return "Stable Contraction"
    assert pmi_trend.economic_state == "Stable Contraction"


def test_pmi_divergence_is_positive_divergence():
    """Test PMIDivergence is_positive_divergence property - covers line 136."""
    # Create positive divergence
    divergence = PMIDivergence(
        country='United States',
        currency='USD',
        start_date=date(2023, 1, 1),
        end_date=date(2023, 2, 1),
        start_pmi=48.0,
        end_pmi=52.0,
        pmi_change=4.0,
        start_currency_value=1.20,
        end_currency_value=1.15,
        currency_change=-0.05,
        divergence_type='positive',
        strength=0.8
    )

    # Check is_positive_divergence property
    assert divergence.is_positive_divergence is True


def test_pmi_divergence_is_negative_divergence():
    """Test PMIDivergence is_negative_divergence property - covers line 146."""
    # Create negative divergence
    divergence = PMIDivergence(
        country='United States',
        currency='USD',
        start_date=date(2023, 1, 1),
        end_date=date(2023, 2, 1),
        start_pmi=52.0,
        end_pmi=48.0,
        pmi_change=-4.0,
        start_currency_value=1.15,
        end_currency_value=1.20,
        currency_change=0.05,
        divergence_type='negative',
        strength=0.7
    )

    # Check is_negative_divergence property
    assert divergence.is_negative_divergence is True


def test_pmi_divergence_trading_signal_buy():
    """Test PMIDivergence trading_signal property for BUY - covers lines 156-157."""
    # Create positive divergence with high strength
    divergence = PMIDivergence(
        country='United States',
        currency='USD',
        start_date=date(2023, 1, 1),
        end_date=date(2023, 2, 1),
        start_pmi=48.0,
        end_pmi=52.0,
        pmi_change=4.0,
        start_currency_value=1.20,
        end_currency_value=1.15,
        currency_change=-0.05,
        divergence_type='positive',
        strength=0.8  # > 0.5
    )

    # Check trading_signal property - should return "BUY"
    assert divergence.trading_signal == "BUY"


def test_pmi_divergence_trading_signal_sell():
    """Test PMIDivergence trading_signal property for SELL - covers lines 158-159."""
    # Create negative divergence with high strength
    divergence = PMIDivergence(
        country='United States',
        currency='USD',
        start_date=date(2023, 1, 1),
        end_date=date(2023, 2, 1),
        start_pmi=52.0,
        end_pmi=48.0,
        pmi_change=-4.0,
        start_currency_value=1.15,
        end_currency_value=1.20,
        currency_change=0.05,
        divergence_type='negative',
        strength=0.7  # > 0.5
    )

    # Check trading_signal property - should return "SELL"
    assert divergence.trading_signal == "SELL"


def test_pmi_divergence_trading_signal_none():
    """Test PMIDivergence trading_signal property for None - covers lines 160-161."""
    # Create divergence with low strength
    divergence = PMIDivergence(
        country='United States',
        currency='USD',
        start_date=date(2023, 1, 1),
        end_date=date(2023, 2, 1),
        start_pmi=48.0,
        end_pmi=52.0,
        pmi_change=4.0,
        start_currency_value=1.20,
        end_currency_value=1.15,
        currency_change=-0.05,
        divergence_type='positive',
        strength=0.3  # <= 0.5
    )

    # Check trading_signal property - should return None
    assert divergence.trading_signal is None