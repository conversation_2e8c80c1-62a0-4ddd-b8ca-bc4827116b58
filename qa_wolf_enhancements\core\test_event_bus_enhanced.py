#!/usr/bin/env python3
"""
QA Wolf Enhancement: Enhanced Event Bus Testing

This module provides comprehensive edge case testing for the event bus components,
focusing on resilience, error handling, and cross-platform compatibility.

SAFETY LEVEL: MAXIMUM - Testing only, no modification of trading logic
"""

import pytest
import unittest
from unittest.mock import MagicMock, patch, Mock
import json
import threading
import time
import logging
from datetime import datetime, timezone
from typing import Dict, List, Any

# Import modules under test
try:
    from src.forex_bot.event_bus.consumer import BaseConsumer, KafkaConsumer
    from src.forex_bot.event_bus.producer import BaseProducer, KafkaProducer
    from src.forex_bot.event_bus.config import EventBusConfig, KafkaConfig
    from src.forex_bot.event_bus.event_schemas import BaseEvent, EventType, MarketDataEvent
    EVENT_BUS_AVAILABLE = True
except ImportError as e:
    EVENT_BUS_AVAILABLE = False
    print(f"Event bus modules not available: {e}")

class TestEventBusResilience(unittest.TestCase):
    """
    Enhanced resilience testing for event bus components.
    
    SAFETY GUARANTEE: These tests only validate behavior,
    never modify trading logic or core functionality.
    """
    
    def setUp(self):
        """Set up test fixtures."""
        if not EVENT_BUS_AVAILABLE:
            self.skipTest("Event bus modules not available")
        
        self.mock_logger = MagicMock(spec=logging.Logger)
        self.test_config = EventBusConfig(
            kafka=KafkaConfig(
                bootstrap_servers=['localhost:9092'],
                security_protocol='PLAINTEXT'
            )
        )
    
    def test_consumer_connection_failure_resilience(self):
        """Test consumer behavior during connection failures."""
        
        # Mock Kafka consumer that fails to connect
        with patch('src.forex_bot.event_bus.consumer.KafkaConsumerLib') as mock_kafka:
            mock_kafka.side_effect = Exception("Connection failed")
            
            # Consumer should handle connection failure gracefully
            consumer = KafkaConsumer(config=self.test_config, logger=self.mock_logger)
            
            # Should not crash on initialization failure
            self.assertIsNotNone(consumer)
            
            # Should log the error appropriately
            self.mock_logger.error.assert_called()
    
    def test_producer_message_delivery_failure(self):
        """Test producer behavior when message delivery fails."""
        
        with patch('src.forex_bot.event_bus.producer.KafkaProducerLib') as mock_kafka:
            # Mock producer that fails to deliver messages
            mock_producer_instance = MagicMock()
            mock_producer_instance.produce.side_effect = Exception("Delivery failed")
            mock_kafka.return_value = mock_producer_instance
            
            producer = KafkaProducer(config=self.test_config, logger=self.mock_logger)
            
            # Create test event
            test_event = MarketDataEvent(
                event_id="test-123",
                timestamp=datetime.now(timezone.utc),
                event_type=EventType.MARKET_DATA,
                symbol="EURUSD",
                timeframe="M5",
                ohlcv_data={}
            )
            
            # Should handle delivery failure gracefully
            result = producer.produce(test_event)
            self.assertFalse(result)  # Should return False on failure
            
            # Should log the error
            self.mock_logger.error.assert_called()
    
    def test_consumer_message_processing_errors(self):
        """Test consumer behavior when message processing fails."""
        
        # Mock consumer with message processing errors
        consumer = BaseConsumer(config=self.test_config, logger=self.mock_logger)
        
        # Mock event handler that raises exceptions
        def failing_handler(event):
            raise ValueError("Processing failed")
        
        consumer.subscribe("test_topic", failing_handler)
        
        # Create test event
        test_event = MarketDataEvent(
            event_id="test-456",
            timestamp=datetime.now(timezone.utc),
            event_type=EventType.MARKET_DATA,
            symbol="GBPUSD",
            timeframe="H1",
            ohlcv_data={}
        )
        
        # Should handle processing errors gracefully
        # (This would be tested with actual message consumption in integration tests)
        self.assertTrue(True)  # Placeholder for actual test implementation
    
    def test_concurrent_producer_operations(self):
        """Test producer thread safety under concurrent operations."""
        
        with patch('src.forex_bot.event_bus.producer.KafkaProducerLib') as mock_kafka:
            mock_producer_instance = MagicMock()
            mock_kafka.return_value = mock_producer_instance
            
            producer = KafkaProducer(config=self.test_config, logger=self.mock_logger)
            
            # Create multiple test events
            events = []
            for i in range(10):
                event = MarketDataEvent(
                    event_id=f"concurrent-test-{i}",
                    timestamp=datetime.now(timezone.utc),
                    event_type=EventType.MARKET_DATA,
                    symbol="USDJPY",
                    timeframe="M15",
                    ohlcv_data={}
                )
                events.append(event)
            
            # Test concurrent production
            threads = []
            results = []
            
            def produce_event(event):
                result = producer.produce(event)
                results.append(result)
            
            # Start multiple threads
            for event in events:
                thread = threading.Thread(target=produce_event, args=(event,))
                threads.append(thread)
                thread.start()
            
            # Wait for all threads to complete
            for thread in threads:
                thread.join(timeout=5)
            
            # All operations should complete
            self.assertEqual(len(results), len(events))
    
    def test_event_serialization_edge_cases(self):
        """Test event serialization with edge case data."""
        
        # Test with None values
        event_with_none = MarketDataEvent(
            event_id="edge-case-1",
            timestamp=datetime.now(timezone.utc),
            event_type=EventType.MARKET_DATA,
            symbol="EURGBP",
            timeframe="H4",
            ohlcv_data=None  # Edge case: None data
        )
        
        # Should handle None values gracefully
        try:
            serialized = event_with_none.model_dump_json()
            self.assertIsInstance(serialized, str)
        except Exception as e:
            self.fail(f"Serialization failed with None values: {e}")
        
        # Test with very large data
        large_data = {f"key_{i}": f"value_{i}" * 1000 for i in range(100)}
        event_with_large_data = MarketDataEvent(
            event_id="edge-case-2",
            timestamp=datetime.now(timezone.utc),
            event_type=EventType.MARKET_DATA,
            symbol="AUDUSD",
            timeframe="D1",
            ohlcv_data=large_data
        )
        
        # Should handle large data gracefully
        try:
            serialized = event_with_large_data.model_dump_json()
            self.assertIsInstance(serialized, str)
        except Exception as e:
            self.fail(f"Serialization failed with large data: {e}")
    
    def test_configuration_validation_edge_cases(self):
        """Test configuration validation with invalid inputs."""
        
        # Test with empty bootstrap servers
        with self.assertRaises((ValueError, Exception)):
            invalid_config = EventBusConfig(
                kafka=KafkaConfig(
                    bootstrap_servers=[],  # Empty list
                    security_protocol='PLAINTEXT'
                )
            )
        
        # Test with invalid security protocol
        with self.assertRaises((ValueError, Exception)):
            invalid_config = EventBusConfig(
                kafka=KafkaConfig(
                    bootstrap_servers=['localhost:9092'],
                    security_protocol='INVALID_PROTOCOL'  # Invalid protocol
                )
            )
    
    def test_memory_usage_under_load(self):
        """Test memory usage patterns under high load."""
        
        with patch('src.forex_bot.event_bus.producer.KafkaProducerLib') as mock_kafka:
            mock_producer_instance = MagicMock()
            mock_kafka.return_value = mock_producer_instance
            
            producer = KafkaProducer(config=self.test_config, logger=self.mock_logger)
            
            # Generate many events to test memory usage
            initial_memory = self._get_memory_usage()
            
            for i in range(1000):
                event = MarketDataEvent(
                    event_id=f"memory-test-{i}",
                    timestamp=datetime.now(timezone.utc),
                    event_type=EventType.MARKET_DATA,
                    symbol="NZDUSD",
                    timeframe="M1",
                    ohlcv_data={"test": f"data_{i}"}
                )
                producer.produce(event)
            
            final_memory = self._get_memory_usage()
            
            # Memory usage should not increase dramatically
            memory_increase = final_memory - initial_memory
            self.assertLess(memory_increase, 100)  # Less than 100MB increase
    
    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB."""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / (1024 * 1024)
        except ImportError:
            return 0.0  # Return 0 if psutil not available
    
    def test_network_timeout_handling(self):
        """Test handling of network timeouts."""
        
        with patch('src.forex_bot.event_bus.consumer.KafkaConsumerLib') as mock_kafka:
            # Mock consumer that times out
            mock_consumer_instance = MagicMock()
            mock_consumer_instance.poll.side_effect = TimeoutError("Network timeout")
            mock_kafka.return_value = mock_consumer_instance
            
            consumer = KafkaConsumer(config=self.test_config, logger=self.mock_logger)
            
            # Should handle timeout gracefully
            # (Actual timeout handling would be tested in integration tests)
            self.assertIsNotNone(consumer)
    
    def test_cross_platform_compatibility(self):
        """Test cross-platform compatibility aspects."""
        
        import os
        import platform
        
        # Test path handling across platforms
        config_paths = [
            "/tmp/kafka.conf",  # Unix-style
            "C:\\temp\\kafka.conf",  # Windows-style
            "kafka.conf"  # Relative path
        ]
        
        for path in config_paths:
            # Should handle different path formats gracefully
            try:
                # Test path normalization
                normalized_path = os.path.normpath(path)
                self.assertIsInstance(normalized_path, str)
            except Exception as e:
                self.fail(f"Path handling failed for {path}: {e}")
        
        # Test platform-specific configurations
        current_platform = platform.system()
        self.assertIn(current_platform, ['Windows', 'Linux', 'Darwin'])


class TestEventBusPerformance(unittest.TestCase):
    """
    Performance testing for event bus components.
    
    SAFETY GUARANTEE: Performance tests only measure and validate,
    never modify trading logic.
    """
    
    def setUp(self):
        """Set up performance test fixtures."""
        if not EVENT_BUS_AVAILABLE:
            self.skipTest("Event bus modules not available")
        
        self.mock_logger = MagicMock(spec=logging.Logger)
        self.test_config = EventBusConfig(
            kafka=KafkaConfig(
                bootstrap_servers=['localhost:9092'],
                security_protocol='PLAINTEXT'
            )
        )
    
    def test_event_creation_performance(self):
        """Test performance of event creation."""
        
        start_time = time.time()
        
        # Create many events
        events = []
        for i in range(1000):
            event = MarketDataEvent(
                event_id=f"perf-test-{i}",
                timestamp=datetime.now(timezone.utc),
                event_type=EventType.MARKET_DATA,
                symbol="USDCAD",
                timeframe="M5",
                ohlcv_data={"open": 1.25, "high": 1.26, "low": 1.24, "close": 1.255, "volume": 1000}
            )
            events.append(event)
        
        end_time = time.time()
        creation_time = end_time - start_time
        
        # Should create 1000 events in reasonable time (< 1 second)
        self.assertLess(creation_time, 1.0)
        self.assertEqual(len(events), 1000)
    
    def test_serialization_performance(self):
        """Test performance of event serialization."""
        
        # Create test event
        event = MarketDataEvent(
            event_id="serialization-perf-test",
            timestamp=datetime.now(timezone.utc),
            event_type=EventType.MARKET_DATA,
            symbol="CHFJPY",
            timeframe="H1",
            ohlcv_data={"open": 110.5, "high": 111.0, "low": 110.0, "close": 110.8, "volume": 5000}
        )
        
        start_time = time.time()
        
        # Serialize many times
        for i in range(1000):
            serialized = event.model_dump_json()
            self.assertIsInstance(serialized, str)
        
        end_time = time.time()
        serialization_time = end_time - start_time
        
        # Should serialize 1000 times in reasonable time (< 0.5 seconds)
        self.assertLess(serialization_time, 0.5)


def run_enhanced_tests():
    """Run all enhanced event bus tests."""
    
    print("🚀 Running QA Wolf Enhanced Event Bus Tests...")
    print("🛡️ SAFETY: Testing only, no trading logic modification")
    
    # Create test suite
    suite = unittest.TestSuite()
    
    # Add resilience tests
    suite.addTest(unittest.makeSuite(TestEventBusResilience))
    
    # Add performance tests
    suite.addTest(unittest.makeSuite(TestEventBusPerformance))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Report results
    print(f"\n📊 Test Results:")
    print(f"   Tests run: {result.testsRun}")
    print(f"   Failures: {len(result.failures)}")
    print(f"   Errors: {len(result.errors)}")
    print(f"   Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    
    if result.wasSuccessful():
        print("✅ All enhanced tests passed!")
    else:
        print("❌ Some tests failed - see details above")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    run_enhanced_tests()