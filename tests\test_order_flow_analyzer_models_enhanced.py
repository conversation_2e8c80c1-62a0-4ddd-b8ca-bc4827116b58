"""
Enhanced tests to push order_flow_analyzer/models.py to 90%+ coverage.

This module targets all remaining uncovered lines to achieve comprehensive coverage.
"""

import pytest
from unittest.mock import patch
from datetime import datetime, timezone

class TestOrderFlowAnalyzerModelsEnhanced:
    """Enhanced tests to achieve 90%+ coverage for order_flow_analyzer/models.py."""

    def test_non_pydantic_order_flow_imbalance_comprehensive(self):
        """Test non-pydantic OrderFlowImbalance comprehensive functionality."""
        with patch('src.forex_bot.order_flow_analyzer.models.PYDANTIC_AVAILABLE', False):
            import importlib
            from src.forex_bot.order_flow_analyzer import models
            importlib.reload(models)
            
            timestamp = datetime.now(timezone.utc)
            
            # Test successful creation
            imbalance = models.OrderFlowImbalance(
                symbol="EURUSD",
                timestamp=timestamp,
                price_level=1.1000,
                bid_volume=100.0,
                ask_volume=150.0,
                imbalance_ratio=0.2,
                imbalance_level=models.ImbalanceLevel.MODERATE,
                is_significant=True,
                distance=0.0001
            )
            
            assert imbalance.symbol == "EURUSD"
            assert imbalance.timestamp == timestamp
            assert imbalance.price_level == 1.1000
            assert imbalance.bid_volume == 100.0
            assert imbalance.ask_volume == 150.0
            assert imbalance.imbalance_ratio == 0.2
            assert imbalance.imbalance_level == models.ImbalanceLevel.MODERATE
            assert imbalance.is_significant == True
            assert imbalance.distance == 0.0001
            
            # Test imbalance ratio validation errors
            with pytest.raises(ValueError) as exc_info:
                models.OrderFlowImbalance(
                    symbol="EURUSD",
                    timestamp=timestamp,
                    price_level=1.1000,
                    bid_volume=100.0,
                    ask_volume=150.0,
                    imbalance_ratio=1.5,  # Invalid ratio > 1.0
                    imbalance_level=models.ImbalanceLevel.MODERATE,
                    is_significant=True
                )
            assert "Imbalance ratio must be between -1.0 and 1.0" in str(exc_info.value)            
            with pytest.raises(ValueError) as exc_info:
                models.OrderFlowImbalance(
                    symbol="EURUSD",
                    timestamp=timestamp,
                    price_level=1.1000,
                    bid_volume=100.0,
                    ask_volume=150.0,
                    imbalance_ratio=-1.5,  # Invalid ratio < -1.0
                    imbalance_level=models.ImbalanceLevel.MODERATE,
                    is_significant=True
                )
            assert "Imbalance ratio must be between -1.0 and 1.0" in str(exc_info.value)

    def test_non_pydantic_large_order_comprehensive(self):
        """Test non-pydantic LargeOrder comprehensive functionality."""
        with patch('src.forex_bot.order_flow_analyzer.models.PYDANTIC_AVAILABLE', False):
            import importlib
            from src.forex_bot.order_flow_analyzer import models
            importlib.reload(models)
            
            timestamp = datetime.now(timezone.utc)
            
            # Test successful creation
            large_order = models.LargeOrder(
                symbol="EURUSD",
                timestamp=timestamp,
                price_level=1.1000,
                volume=1000.0,
                type="bid",
                is_market_moving=True,
                standard_deviations=2.5
            )
            
            assert large_order.symbol == "EURUSD"
            assert large_order.timestamp == timestamp
            assert large_order.price_level == 1.1000
            assert large_order.volume == 1000.0
            assert large_order.type == "bid"
            assert large_order.is_market_moving == True
            assert large_order.standard_deviations == 2.5
            
            # Test volume validation error
            with pytest.raises(ValueError) as exc_info:
                models.LargeOrder(
                    symbol="EURUSD",
                    timestamp=timestamp,
                    price_level=1.1000,
                    volume=-100.0,  # Invalid negative volume
                    type="bid",
                    is_market_moving=True,
                    standard_deviations=2.5
                )
            assert "Volume must be positive" in str(exc_info.value)
            
            # Test standard deviations validation error
            with pytest.raises(ValueError) as exc_info:
                models.LargeOrder(
                    symbol="EURUSD",
                    timestamp=timestamp,
                    price_level=1.1000,
                    volume=1000.0,
                    type="bid",
                    is_market_moving=True,
                    standard_deviations=-1.0  # Invalid negative standard deviations
                )
            assert "Standard deviations must be positive" in str(exc_info.value)            
            # Test type validation error
            with pytest.raises(ValueError) as exc_info:
                models.LargeOrder(
                    symbol="EURUSD",
                    timestamp=timestamp,
                    price_level=1.1000,
                    volume=1000.0,
                    type="invalid",  # Invalid type
                    is_market_moving=True,
                    standard_deviations=2.5
                )
            assert 'Type must be "bid" or "ask"' in str(exc_info.value)

    def test_non_pydantic_support_resistance_level_comprehensive(self):
        """Test non-pydantic SupportResistanceLevel comprehensive functionality."""
        with patch('src.forex_bot.order_flow_analyzer.models.PYDANTIC_AVAILABLE', False):
            import importlib
            from src.forex_bot.order_flow_analyzer import models
            importlib.reload(models)
            
            timestamp = datetime.now(timezone.utc)
            
            # Test successful creation
            level = models.SupportResistanceLevel(
                symbol="EURUSD",
                timestamp=timestamp,
                price_level=1.1000,
                type="support",
                strength=0.8,
                volume_concentration=1500.0,
                is_active=True
            )
            
            assert level.symbol == "EURUSD"
            assert level.timestamp == timestamp
            assert level.price_level == 1.1000
            assert level.type == "support"
            assert level.strength == 0.8
            assert level.volume_concentration == 1500.0
            assert level.is_active == True
            
            # Test strength validation errors
            with pytest.raises(ValueError) as exc_info:
                models.SupportResistanceLevel(
                    symbol="EURUSD",
                    timestamp=timestamp,
                    price_level=1.1000,
                    type="support",
                    strength=1.5,  # Invalid strength > 1.0
                    volume_concentration=1500.0,
                    is_active=True
                )
            assert "Strength must be between 0.0 and 1.0" in str(exc_info.value)
            
            with pytest.raises(ValueError) as exc_info:
                models.SupportResistanceLevel(
                    symbol="EURUSD",
                    timestamp=timestamp,
                    price_level=1.1000,
                    type="support",
                    strength=-0.1,  # Invalid strength < 0.0
                    volume_concentration=1500.0,
                    is_active=True
                )
            assert "Strength must be between 0.0 and 1.0" in str(exc_info.value)