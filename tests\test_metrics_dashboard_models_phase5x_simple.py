"""
Phase 5X: Simple test for metrics_dashboard/models.py to achieve 90%+ coverage.
"""

import pytest
from datetime import datetime, timedelta
from src.forex_bot.metrics_dashboard.models import (
    TimeFrame, MetricCategory, MetricValue, MetricTimeSeries, 
    MarketMetrics, DashboardLayout, Dashboard
)


class TestMetricsDashboardModelsPhase5XSimple:
    """Simple test class to achieve 90%+ coverage for metrics_dashboard/models.py"""

    def test_metric_value_numpy_validation(self):
        """Test MetricValue validation with numpy NaN and Inf values"""
        
        # Test with NaN value
        with pytest.raises(ValueError, match="value cannot be NaN or Inf"):
            MetricValue(
                name="test_metric",
                value=float('nan'),
                unit="units",
                timestamp=datetime.now(),
                category=MetricCategory.PERFORMANCE
            )
        
        # Test with Inf value
        with pytest.raises(ValueError, match="value cannot be NaN or Inf"):
            MetricValue(
                name="test_metric",
                value=float('inf'),
                unit="units",
                timestamp=datetime.now(),
                category=MetricCategory.PERFORMANCE
            )

    def test_metric_time_series_validation(self):
        """Test MetricTimeSeries validation"""
        
        # Test values and timestamps length mismatch
        with pytest.raises(ValueError, match="values and timestamps must have the same length"):
            MetricTimeSeries(
                name="test_series",
                values=[1.0, 2.0, 3.0],
                timestamps=[datetime.now(), datetime.now()],
                category=MetricCategory.PERFORMANCE
            )        # Test values containing NaN
        with pytest.raises(ValueError, match="values cannot contain NaN or Inf"):
            MetricTimeSeries(
                name="test_series",
                values=[1.0, float('nan'), 3.0],
                timestamps=[datetime.now(), datetime.now(), datetime.now()],
                category=MetricCategory.PERFORMANCE
            )

    def test_market_metrics_sentiment_score_validation(self):
        """Test MarketMetrics sentiment_score validation"""
        
        # Test sentiment_score out of range (too high)
        with pytest.raises(ValueError, match="sentiment_score must be between -1.0 and 1.0"):
            MarketMetrics(
                spread=1.5,
                volume=1000.0,
                volatility=0.02,
                sentiment_score=1.5,  # Invalid: > 1.0
                timestamp=datetime.now()
            )

    def test_dashboard_layout_chart_validation(self):
        """Test DashboardLayout chart validation"""
        
        # Test row out of range
        with pytest.raises(ValueError, match="row must be between 0 and"):
            DashboardLayout(
                rows=2,
                columns=2,
                charts=[
                    {"id": "chart1", "row": 2, "column": 0}  # Invalid: row >= rows
                ]
            )

    def test_dashboard_custom_date_validation(self):
        """Test Dashboard custom date validation"""
        
        # Test missing custom_start_date when time_frame is CUSTOM
        with pytest.raises(ValueError, match="custom_start_date and custom_end_date are required when time_frame is CUSTOM"):
            Dashboard(
                name="test_dashboard",
                layout=DashboardLayout(rows=1, columns=1, charts=[]),
                charts={},
                time_frame=TimeFrame.CUSTOM,
                custom_start_date=None,  # Missing required date
                custom_end_date=datetime.now(),
                last_updated=datetime.now()
            )

    def test_to_dataframe_method(self):
        """Test MetricTimeSeries to_dataframe method"""
        
        timestamps = [datetime.now(), datetime.now() + timedelta(hours=1)]
        values = [1.0, 2.0]
        
        time_series = MetricTimeSeries(
            name="test_series",
            values=values,
            timestamps=timestamps,
            category=MetricCategory.PERFORMANCE
        )
        
        df = time_series.to_dataframe()
        assert len(df) == 2
        assert list(df.columns) == ['timestamp', 'value']