"""
Phase 5L comprehensive tests to push order_flow_analyzer/models.py to 90%+ coverage.
"""

import pytest
from datetime import datetime, timezone
from src.forex_bot.order_flow_analyzer import models

class TestOrderFlowAnalyzerModelsPhase5L:
    """Phase 5L tests to achieve 90%+ coverage for order_flow_analyzer/models.py."""

    def test_imbalance_level_enum(self):
        """Test ImbalanceLevel enum values."""
        assert models.ImbalanceLevel.EXTREME_BUY.value == "extreme_buy"
        assert models.ImbalanceLevel.STRONG_BUY.value == "strong_buy"
        assert models.ImbalanceLevel.MODERATE_BUY.value == "moderate_buy"
        assert models.ImbalanceLevel.NEUTRAL.value == "neutral"
        assert models.ImbalanceLevel.MODERATE_SELL.value == "moderate_sell"
        assert models.ImbalanceLevel.STRONG_SELL.value == "strong_sell"
        assert models.ImbalanceLevel.EXTREME_SELL.value == "extreme_sell"

    def test_order_flow_imbalance_basic_functionality(self):
        """Test OrderFlowImbalance class basic functionality."""
        
        # Create test imbalance
        timestamp = datetime(2023, 12, 15, 10, 0, 0, tzinfo=timezone.utc)
        imbalance = models.OrderFlowImbalance(
            symbol="EURUSD",
            timestamp=timestamp,
            price_level=1.1000,
            bid_volume=1000.0,
            ask_volume=500.0,
            imbalance_ratio=0.5,
            imbalance_level=models.ImbalanceLevel.STRONG_BUY,
            is_significant=True,
            distance=0.001
        )
        
        # Test basic attributes
        assert imbalance.symbol == "EURUSD"
        assert imbalance.timestamp == timestamp
        assert imbalance.price_level == 1.1000
        assert imbalance.bid_volume == 1000.0
        assert imbalance.ask_volume == 500.0
        assert imbalance.imbalance_ratio == 0.5
        assert imbalance.imbalance_level == models.ImbalanceLevel.STRONG_BUY
        assert imbalance.is_significant == True
        assert imbalance.distance == 0.001

    def test_order_flow_imbalance_validation(self):
        """Test OrderFlowImbalance validation."""
        
        timestamp = datetime(2023, 12, 15, 10, 0, 0, tzinfo=timezone.utc)
        
        # Test valid imbalance ratio boundaries
        valid_ratios = [-1.0, -0.5, 0.0, 0.5, 1.0]
        for ratio in valid_ratios:
            imbalance = models.OrderFlowImbalance(
                symbol="GBPUSD",
                timestamp=timestamp,
                price_level=1.2500,
                bid_volume=800.0,
                ask_volume=600.0,
                imbalance_ratio=ratio,
                imbalance_level=models.ImbalanceLevel.NEUTRAL,
                is_significant=False
            )
            assert imbalance.imbalance_ratio == ratio
        
        # Test invalid imbalance ratio
        with pytest.raises(ValueError, match="Imbalance ratio must be between -1.0 and 1.0"):
            models.OrderFlowImbalance(
                symbol="GBPUSD",
                timestamp=timestamp,
                price_level=1.2500,
                bid_volume=800.0,
                ask_volume=600.0,
                imbalance_ratio=1.5,  # Invalid: > 1.0
                imbalance_level=models.ImbalanceLevel.NEUTRAL,
                is_significant=False
            )
        
        with pytest.raises(ValueError, match="Imbalance ratio must be between -1.0 and 1.0"):
            models.OrderFlowImbalance(
                symbol="GBPUSD",
                timestamp=timestamp,
                price_level=1.2500,
                bid_volume=800.0,
                ask_volume=600.0,
                imbalance_ratio=-1.5,  # Invalid: < -1.0
                imbalance_level=models.ImbalanceLevel.NEUTRAL,
                is_significant=False
            )

    def test_large_order_basic_functionality(self):
        """Test LargeOrder class basic functionality."""
        
        # Create test large order
        timestamp = datetime(2023, 12, 15, 11, 0, 0, tzinfo=timezone.utc)
        large_order = models.LargeOrder(
            symbol="USDJPY",
            timestamp=timestamp,
            price_level=150.50,
            volume=5000.0,
            type="bid",
            is_market_moving=True,
            standard_deviations=3.5
        )
        
        # Test basic attributes
        assert large_order.symbol == "USDJPY"
        assert large_order.timestamp == timestamp
        assert large_order.price_level == 150.50
        assert large_order.volume == 5000.0
        assert large_order.type == "bid"
        assert large_order.is_market_moving == True
        assert large_order.standard_deviations == 3.5

    def test_large_order_validation(self):
        """Test LargeOrder validation."""
        
        timestamp = datetime(2023, 12, 15, 11, 0, 0, tzinfo=timezone.utc)
        
        # Test valid order types
        valid_types = ["bid", "ask"]
        for order_type in valid_types:
            large_order = models.LargeOrder(
                symbol="AUDUSD",
                timestamp=timestamp,
                price_level=0.6500,
                volume=2000.0,
                type=order_type,
                is_market_moving=False,
                standard_deviations=2.0
            )
            assert large_order.type == order_type
        
        # Test invalid volume
        with pytest.raises(ValueError, match="Volume must be positive"):
            models.LargeOrder(
                symbol="AUDUSD",
                timestamp=timestamp,
                price_level=0.6500,
                volume=0.0,  # Invalid: not positive
                type="bid",
                is_market_moving=False,
                standard_deviations=2.0
            )
        
        with pytest.raises(ValueError, match="Volume must be positive"):
            models.LargeOrder(
                symbol="AUDUSD",
                timestamp=timestamp,
                price_level=0.6500,
                volume=-100.0,  # Invalid: negative
                type="bid",
                is_market_moving=False,
                standard_deviations=2.0
            )
        
        # Test invalid standard deviations
        with pytest.raises(ValueError, match="Standard deviations must be positive"):
            models.LargeOrder(
                symbol="AUDUSD",
                timestamp=timestamp,
                price_level=0.6500,
                volume=2000.0,
                type="bid",
                is_market_moving=False,
                standard_deviations=0.0  # Invalid: not positive
            )
        
        with pytest.raises(ValueError, match="Standard deviations must be positive"):
            models.LargeOrder(
                symbol="AUDUSD",
                timestamp=timestamp,
                price_level=0.6500,
                volume=2000.0,
                type="bid",
                is_market_moving=False,
                standard_deviations=-1.0  # Invalid: negative
            )

    def test_support_resistance_level_basic_functionality(self):
        """Test SupportResistanceLevel class basic functionality."""
        
        # Create test support level
        timestamp = datetime(2023, 12, 15, 12, 0, 0, tzinfo=timezone.utc)
        support_level = models.SupportResistanceLevel(
            symbol="NZDUSD",
            timestamp=timestamp,
            price_level=0.6200,
            type="support",
            strength=0.85,
            volume_concentration=15000.0,
            is_active=True
        )
        
        # Test basic attributes
        assert support_level.symbol == "NZDUSD"
        assert support_level.timestamp == timestamp
        assert support_level.price_level == 0.6200
        assert support_level.type == "support"
        assert support_level.strength == 0.85
        assert support_level.volume_concentration == 15000.0
        assert support_level.is_active == True

    def test_support_resistance_level_validation(self):
        """Test SupportResistanceLevel validation."""
        
        timestamp = datetime(2023, 12, 15, 12, 0, 0, tzinfo=timezone.utc)
        
        # Test valid types
        valid_types = ["support", "resistance"]
        for level_type in valid_types:
            level = models.SupportResistanceLevel(
                symbol="USDCAD",
                timestamp=timestamp,
                price_level=1.3500,
                type=level_type,
                strength=0.7,
                volume_concentration=8000.0,
                is_active=False
            )
            assert level.type == level_type
        
        # Test valid strength boundaries
        valid_strengths = [0.0, 0.25, 0.5, 0.75, 1.0]
        for strength in valid_strengths:
            level = models.SupportResistanceLevel(
                symbol="USDCAD",
                timestamp=timestamp,
                price_level=1.3500,
                type="support",
                strength=strength,
                volume_concentration=8000.0,
                is_active=True
            )
            assert level.strength == strength
        
        # Test invalid strength
        with pytest.raises(ValueError, match="Strength must be between 0.0 and 1.0"):
            models.SupportResistanceLevel(
                symbol="USDCAD",
                timestamp=timestamp,
                price_level=1.3500,
                type="support",
                strength=1.5,  # Invalid: > 1.0
                volume_concentration=8000.0,
                is_active=True
            )
        
        with pytest.raises(ValueError, match="Strength must be between 0.0 and 1.0"):
            models.SupportResistanceLevel(
                symbol="USDCAD",
                timestamp=timestamp,
                price_level=1.3500,
                type="support",
                strength=-0.1,  # Invalid: < 0.0
                volume_concentration=8000.0,
                is_active=True
            )
        
        # Test invalid volume concentration
        with pytest.raises(ValueError, match="Volume concentration must be positive"):
            models.SupportResistanceLevel(
                symbol="USDCAD",
                timestamp=timestamp,
                price_level=1.3500,
                type="support",
                strength=0.7,
                volume_concentration=0.0,  # Invalid: not positive
                is_active=True
            )
        
        with pytest.raises(ValueError, match="Volume concentration must be positive"):
            models.SupportResistanceLevel(
                symbol="USDCAD",
                timestamp=timestamp,
                price_level=1.3500,
                type="support",
                strength=0.7,
                volume_concentration=-1000.0,  # Invalid: negative
                is_active=True
            )

    def test_order_flow_signal_basic_functionality(self):
        """Test OrderFlowSignal class basic functionality."""
        
        # Create test signal
        timestamp = datetime(2023, 12, 15, 13, 0, 0, tzinfo=timezone.utc)
        signal = models.OrderFlowSignal(
            symbol="EURJPY",
            timestamp=timestamp,
            signal_type="buy",
            confidence=0.9,
            price_level=160.50,
            reason="Strong bid imbalance detected"
        )
        
        # Test basic attributes
        assert signal.symbol == "EURJPY"
        assert signal.timestamp == timestamp
        assert signal.signal_type == "buy"
        assert signal.confidence == 0.9
        assert signal.price_level == 160.50
        assert signal.reason == "Strong bid imbalance detected"
        assert signal.imbalance_data is None
        assert signal.large_order_data is None
        assert signal.support_resistance_data is None

    def test_order_flow_signal_with_data(self):
        """Test OrderFlowSignal with related data objects."""
        
        timestamp = datetime(2023, 12, 15, 13, 0, 0, tzinfo=timezone.utc)
        
        # Create related data objects
        imbalance = models.OrderFlowImbalance(
            symbol="GBPJPY",
            timestamp=timestamp,
            price_level=185.00,
            bid_volume=3000.0,
            ask_volume=1000.0,
            imbalance_ratio=0.75,
            imbalance_level=models.ImbalanceLevel.STRONG_BUY,
            is_significant=True
        )
        
        large_order = models.LargeOrder(
            symbol="GBPJPY",
            timestamp=timestamp,
            price_level=185.00,
            volume=10000.0,
            type="bid",
            is_market_moving=True,
            standard_deviations=4.0
        )
        
        support_level = models.SupportResistanceLevel(
            symbol="GBPJPY",
            timestamp=timestamp,
            price_level=184.50,
            type="support",
            strength=0.95,
            volume_concentration=25000.0,
            is_active=True
        )
        
        # Create signal with all data
        signal = models.OrderFlowSignal(
            symbol="GBPJPY",
            timestamp=timestamp,
            signal_type="buy",
            confidence=0.95,
            price_level=185.00,
            reason="Multiple bullish signals converging",
            imbalance_data=imbalance,
            large_order_data=large_order,
            support_resistance_data=support_level
        )
        
        # Test that all data is properly attached
        assert signal.imbalance_data == imbalance
        assert signal.large_order_data == large_order
        assert signal.support_resistance_data == support_level

    def test_order_flow_signal_validation(self):
        """Test OrderFlowSignal validation."""
        
        timestamp = datetime(2023, 12, 15, 13, 0, 0, tzinfo=timezone.utc)
        
        # Test valid signal types
        valid_types = ["buy", "sell", "hold"]
        for signal_type in valid_types:
            signal = models.OrderFlowSignal(
                symbol="USDCHF",
                timestamp=timestamp,
                signal_type=signal_type,
                confidence=0.8,
                price_level=0.9000,
                reason=f"Test {signal_type} signal"
            )
            assert signal.signal_type == signal_type
        
        # Test valid confidence boundaries
        valid_confidences = [0.0, 0.25, 0.5, 0.75, 1.0]
        for confidence in valid_confidences:
            signal = models.OrderFlowSignal(
                symbol="USDCHF",
                timestamp=timestamp,
                signal_type="hold",
                confidence=confidence,
                price_level=0.9000,
                reason="Test confidence"
            )
            assert signal.confidence == confidence
        
        # Test invalid confidence
        with pytest.raises(ValueError, match="Confidence must be between 0.0 and 1.0"):
            models.OrderFlowSignal(
                symbol="USDCHF",
                timestamp=timestamp,
                signal_type="buy",
                confidence=1.5,  # Invalid: > 1.0
                price_level=0.9000,
                reason="Invalid confidence"
            )
        
        with pytest.raises(ValueError, match="Confidence must be between 0.0 and 1.0"):
            models.OrderFlowSignal(
                symbol="USDCHF",
                timestamp=timestamp,
                signal_type="buy",
                confidence=-0.1,  # Invalid: < 0.0
                price_level=0.9000,
                reason="Invalid confidence"
            )

    def test_order_flow_context_basic_functionality(self):
        """Test OrderFlowContext class basic functionality."""
        
        timestamp = datetime(2023, 12, 15, 14, 0, 0, tzinfo=timezone.utc)
        
        # Create test data lists
        imbalances = [
            models.OrderFlowImbalance(
                symbol="AUDCAD",
                timestamp=timestamp,
                price_level=0.9100,
                bid_volume=2000.0,
                ask_volume=1500.0,
                imbalance_ratio=0.25,
                imbalance_level=models.ImbalanceLevel.MODERATE_BUY,
                is_significant=True
            )
        ]
        
        large_orders = [
            models.LargeOrder(
                symbol="AUDCAD",
                timestamp=timestamp,
                price_level=0.9105,
                volume=8000.0,
                type="ask",
                is_market_moving=True,
                standard_deviations=3.2
            )
        ]
        
        support_resistance_levels = [
            models.SupportResistanceLevel(
                symbol="AUDCAD",
                timestamp=timestamp,
                price_level=0.9080,
                type="support",
                strength=0.8,
                volume_concentration=12000.0,
                is_active=True
            )
        ]
        
        signals = [
            models.OrderFlowSignal(
                symbol="AUDCAD",
                timestamp=timestamp,
                signal_type="sell",
                confidence=0.75,
                price_level=0.9105,
                reason="Large ask order at resistance"
            )
        ]
        
        # Create context
        context = models.OrderFlowContext(
            symbol="AUDCAD",
            timestamp=timestamp,
            imbalances=imbalances,
            large_orders=large_orders,
            support_resistance_levels=support_resistance_levels,
            signals=signals,
            overall_bias="bearish",
            confidence=0.8
        )
        
        # Test basic attributes
        assert context.symbol == "AUDCAD"
        assert context.timestamp == timestamp
        assert len(context.imbalances) == 1
        assert len(context.large_orders) == 1
        assert len(context.support_resistance_levels) == 1
        assert len(context.signals) == 1
        assert context.overall_bias == "bearish"
        assert context.confidence == 0.8

    def test_order_flow_context_validation(self):
        """Test OrderFlowContext validation."""
        
        timestamp = datetime(2023, 12, 15, 14, 0, 0, tzinfo=timezone.utc)
        
        # Test valid bias types
        valid_biases = ["bullish", "bearish", "neutral"]
        for bias in valid_biases:
            context = models.OrderFlowContext(
                symbol="CADCHF",
                timestamp=timestamp,
                imbalances=[],
                large_orders=[],
                support_resistance_levels=[],
                signals=[],
                overall_bias=bias,
                confidence=0.6
            )
            assert context.overall_bias == bias
        
        # Test valid confidence boundaries
        valid_confidences = [0.0, 0.25, 0.5, 0.75, 1.0]
        for confidence in valid_confidences:
            context = models.OrderFlowContext(
                symbol="CADCHF",
                timestamp=timestamp,
                imbalances=[],
                large_orders=[],
                support_resistance_levels=[],
                signals=[],
                overall_bias="neutral",
                confidence=confidence
            )
            assert context.confidence == confidence
        
        # Test invalid confidence
        with pytest.raises(ValueError, match="Confidence must be between 0.0 and 1.0"):
            models.OrderFlowContext(
                symbol="CADCHF",
                timestamp=timestamp,
                imbalances=[],
                large_orders=[],
                support_resistance_levels=[],
                signals=[],
                overall_bias="neutral",
                confidence=1.5  # Invalid: > 1.0
            )
        
        with pytest.raises(ValueError, match="Confidence must be between 0.0 and 1.0"):
            models.OrderFlowContext(
                symbol="CADCHF",
                timestamp=timestamp,
                imbalances=[],
                large_orders=[],
                support_resistance_levels=[],
                signals=[],
                overall_bias="neutral",
                confidence=-0.1  # Invalid: < 0.0
            )

    def test_edge_cases_and_optional_fields(self):
        """Test edge cases and optional fields."""
        
        timestamp = datetime(2023, 12, 15, 15, 0, 0, tzinfo=timezone.utc)
        
        # Test OrderFlowImbalance without distance
        imbalance_no_distance = models.OrderFlowImbalance(
            symbol="CHFJPY",
            timestamp=timestamp,
            price_level=165.00,
            bid_volume=1000.0,
            ask_volume=1000.0,
            imbalance_ratio=0.0,
            imbalance_level=models.ImbalanceLevel.NEUTRAL,
            is_significant=False
        )
        assert imbalance_no_distance.distance is None
        
        # Test OrderFlowSignal without optional data
        signal_minimal = models.OrderFlowSignal(
            symbol="CHFJPY",
            timestamp=timestamp,
            signal_type="hold",
            confidence=0.5,
            price_level=165.00,
            reason="Neutral market conditions"
        )
        assert signal_minimal.imbalance_data is None
        assert signal_minimal.large_order_data is None
        assert signal_minimal.support_resistance_data is None
        
        # Test OrderFlowContext with empty lists
        context_empty = models.OrderFlowContext(
            symbol="CHFJPY",
            timestamp=timestamp,
            imbalances=[],
            large_orders=[],
            support_resistance_levels=[],
            signals=[],
            overall_bias="neutral",
            confidence=0.5
        )
        assert len(context_empty.imbalances) == 0
        assert len(context_empty.large_orders) == 0
        assert len(context_empty.support_resistance_levels) == 0
        assert len(context_empty.signals) == 0