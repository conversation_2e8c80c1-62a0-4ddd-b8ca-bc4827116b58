"""
Targeted tests to push event_bus/config.py to 90% coverage.

This module specifically targets the remaining uncovered lines:
- Lines 63-66: validate_security_protocol validator
- Lines 71-81: validate_sasl_mechanism validator  
- Lines 110-117: get_topic_by_event_type method
- Lines 142-145: validate_provider validator
- Lines 164-192: get_event_bus_config function logic
"""

import pytest
import os
from unittest.mock import patch, MagicMock
from pydantic import ValidationError

from src.forex_bot.event_bus.config import (
    KafkaConfig, 
    TopicConfig, 
    EventBusConfig, 
    get_event_bus_config
)


class TestEventBusConfig90Percent:
    """Targeted tests to achieve 90% coverage for event_bus/config.py."""

    def test_kafka_config_validate_security_protocol_valid(self):
        """Test validate_security_protocol with valid protocols - covers lines 63-66."""
        valid_protocols = ["PLAINTEXT", "SSL", "SASL_PLAINTEXT", "SASL_SSL"]
        
        for protocol in valid_protocols:
            config = KafkaConfig(security_protocol=protocol)
            assert config.security_protocol == protocol

    def test_kafka_config_validate_security_protocol_invalid(self):
        """Test validate_security_protocol with invalid protocol - covers lines 63-66."""
        with pytest.raises(ValidationError) as exc_info:
            KafkaConfig(security_protocol="INVALID_PROTOCOL")
        
        assert "Security protocol must be one of" in str(exc_info.value)

    def test_kafka_config_validate_sasl_mechanism_valid(self):
        """Test validate_sasl_mechanism with valid mechanisms - covers lines 71-81."""
        valid_mechanisms = ["PLAIN", "GSSAPI", "SCRAM-SHA-256", "SCRAM-SHA-512"]
        
        for mechanism in valid_mechanisms:
            config = KafkaConfig(
                sasl_mechanism=mechanism,
                security_protocol="SASL_PLAINTEXT",
                sasl_username="test_user",
                sasl_password="test_pass"
            )
            assert config.sasl_mechanism == mechanism

    def test_kafka_config_validate_sasl_mechanism_invalid(self):
        """Test validate_sasl_mechanism with invalid mechanism - covers lines 71-81."""
        with pytest.raises(ValidationError) as exc_info:
            KafkaConfig(
                sasl_mechanism="INVALID_MECHANISM",
                security_protocol="SASL_PLAINTEXT",
                sasl_username="test_user",
                sasl_password="test_pass"
            )
        
        assert "SASL mechanism must be one of" in str(exc_info.value)

    def test_kafka_config_validate_sasl_mechanism_missing_credentials(self):
        """Test validate_sasl_mechanism with missing credentials - covers lines 71-81."""
        # Test missing username
        with pytest.raises(ValidationError) as exc_info:
            KafkaConfig(
                sasl_mechanism="PLAIN",
                security_protocol="SASL_PLAINTEXT",
                sasl_password="test_pass"
                # Missing sasl_username
            )
        
        assert "SASL username and password must be provided" in str(exc_info.value)
        
        # Test missing password
        with pytest.raises(ValidationError) as exc_info:
            KafkaConfig(
                sasl_mechanism="PLAIN",
                security_protocol="SASL_PLAINTEXT",
                sasl_username="test_user"
                # Missing sasl_password
            )
        
        assert "SASL username and password must be provided" in str(exc_info.value)

    def test_kafka_config_validate_sasl_mechanism_none_value(self):
        """Test validate_sasl_mechanism with None value - covers lines 71-81."""
        # Should work fine with None sasl_mechanism
        config = KafkaConfig(
            sasl_mechanism=None,
            security_protocol="PLAINTEXT"
        )
        assert config.sasl_mechanism is None

    def test_topic_config_get_topic_by_event_type(self):
        """Test get_topic_by_event_type method - covers lines 110-117."""
        topic_config = TopicConfig(
            market_data_topic="custom_market_data",
            order_topic="custom_orders",
            trade_topic="custom_trades",
            analysis_topic="custom_analysis",
            system_topic="custom_system"
        )
        
        # Test all mapped event types
        assert topic_config.get_topic_by_event_type("market_data") == "custom_market_data"
        assert topic_config.get_topic_by_event_type("order") == "custom_orders"
        assert topic_config.get_topic_by_event_type("trade") == "custom_trades"
        assert topic_config.get_topic_by_event_type("analysis") == "custom_analysis"
        assert topic_config.get_topic_by_event_type("system") == "custom_system"
        
        # Test unknown event type (should return system_topic)
        assert topic_config.get_topic_by_event_type("unknown_type") == "custom_system"
        assert topic_config.get_topic_by_event_type("") == "custom_system"
        assert topic_config.get_topic_by_event_type("invalid") == "custom_system"

    def test_event_bus_config_validate_provider_valid(self):
        """Test validate_provider with valid providers - covers lines 142-145."""
        valid_providers = ["kafka", "pulsar"]
        
        for provider in valid_providers:
            config = EventBusConfig(provider=provider)
            assert config.provider == provider

    def test_event_bus_config_validate_provider_invalid(self):
        """Test validate_provider with invalid provider - covers lines 142-145."""
        with pytest.raises(ValidationError) as exc_info:
            EventBusConfig(provider="invalid_provider")
        
        assert "Provider must be one of" in str(exc_info.value)

    @patch('src.forex_bot.event_bus.config.get_app_config')
    @patch.dict(os.environ, {}, clear=True)
    def test_get_event_bus_config_default_values(self, mock_get_app_config):
        """Test get_event_bus_config with default values - covers lines 164-192."""
        # Reset the global config
        import src.forex_bot.event_bus.config
        src.forex_bot.event_bus.config._event_bus_config = None
        
        # Mock app config
        mock_app_config = MagicMock()
        mock_app_config.instance_id = "test_instance"
        mock_get_app_config.return_value = mock_app_config
        
        config = get_event_bus_config()
        
        # Verify default values
        assert config.enabled is False
        assert config.provider == "kafka"
        assert config.kafka.bootstrap_servers == "localhost:9092"
        assert config.kafka.client_id == "forex_bot_test_instance"
        assert config.kafka.security_protocol == "PLAINTEXT"
        assert config.topics.market_data_topic == "forex_bot.market_data"
        assert config.topics.order_topic == "forex_bot.orders"
        assert config.topics.trade_topic == "forex_bot.trades"
        assert config.topics.analysis_topic == "forex_bot.analysis"
        assert config.topics.system_topic == "forex_bot.system"

    @patch('src.forex_bot.event_bus.config.get_app_config')
    @patch.dict(os.environ, {
        "EVENT_BUS_ENABLED": "true",
        "EVENT_BUS_PROVIDER": "pulsar",
        "KAFKA_BOOTSTRAP_SERVERS": "custom:9092",
        "KAFKA_CLIENT_ID": "custom_client",
        "KAFKA_SECURITY_PROTOCOL": "SSL",
        "KAFKA_SSL_CAFILE": "/path/to/ca.pem",
        "KAFKA_SSL_CERTFILE": "/path/to/cert.pem",
        "KAFKA_SSL_KEYFILE": "/path/to/key.pem",
        "KAFKA_SASL_MECHANISM": "PLAIN",
        "KAFKA_SASL_USERNAME": "test_user",
        "KAFKA_SASL_PASSWORD": "test_pass",
        "KAFKA_MARKET_DATA_TOPIC": "custom.market_data",
        "KAFKA_ORDER_TOPIC": "custom.orders",
        "KAFKA_TRADE_TOPIC": "custom.trades",
        "KAFKA_ANALYSIS_TOPIC": "custom.analysis",
        "KAFKA_SYSTEM_TOPIC": "custom.system"
    })
    def test_get_event_bus_config_custom_values(self, mock_get_app_config):
        """Test get_event_bus_config with custom environment values - covers lines 164-192."""
        # Reset the global config
        import src.forex_bot.event_bus.config
        src.forex_bot.event_bus.config._event_bus_config = None
        
        # Mock app config
        mock_app_config = MagicMock()
        mock_app_config.instance_id = "test_instance"
        mock_get_app_config.return_value = mock_app_config
        
        config = get_event_bus_config()
        
        # Verify custom values
        assert config.enabled is True
        assert config.provider == "pulsar"
        assert config.kafka.bootstrap_servers == "custom:9092"
        assert config.kafka.client_id == "custom_client"
        assert config.kafka.security_protocol == "SSL"
        assert config.kafka.ssl_cafile == "/path/to/ca.pem"
        assert config.kafka.ssl_certfile == "/path/to/cert.pem"
        assert config.kafka.ssl_keyfile == "/path/to/key.pem"
        assert config.kafka.sasl_mechanism == "PLAIN"
        assert config.kafka.sasl_username == "test_user"
        assert config.kafka.sasl_password == "test_pass"
        assert config.topics.market_data_topic == "custom.market_data"
        assert config.topics.order_topic == "custom.orders"
        assert config.topics.trade_topic == "custom.trades"
        assert config.topics.analysis_topic == "custom.analysis"
        assert config.topics.system_topic == "custom.system"

    @patch('src.forex_bot.event_bus.config.get_app_config')
    @patch.dict(os.environ, {
        "EVENT_BUS_ENABLED": "1",  # Test "1" as true
    })
    def test_get_event_bus_config_enabled_variations(self, mock_get_app_config):
        """Test get_event_bus_config with different enabled values - covers lines 164-192."""
        # Reset the global config
        import src.forex_bot.event_bus.config
        src.forex_bot.event_bus.config._event_bus_config = None
        
        # Mock app config
        mock_app_config = MagicMock()
        mock_app_config.instance_id = "test_instance"
        mock_get_app_config.return_value = mock_app_config
        
        config = get_event_bus_config()
        assert config.enabled is True

    @patch('src.forex_bot.event_bus.config.get_app_config')
    @patch.dict(os.environ, {
        "EVENT_BUS_ENABLED": "yes",  # Test "yes" as true
    })
    def test_get_event_bus_config_enabled_yes(self, mock_get_app_config):
        """Test get_event_bus_config with 'yes' as enabled value - covers lines 164-192."""
        # Reset the global config
        import src.forex_bot.event_bus.config
        src.forex_bot.event_bus.config._event_bus_config = None
        
        # Mock app config
        mock_app_config = MagicMock()
        mock_app_config.instance_id = "test_instance"
        mock_get_app_config.return_value = mock_app_config
        
        config = get_event_bus_config()
        assert config.enabled is True

    @patch('src.forex_bot.event_bus.config.get_app_config')
    def test_get_event_bus_config_singleton_behavior(self, mock_get_app_config):
        """Test that get_event_bus_config returns the same instance - covers lines 164-192."""
        # Reset the global config
        import src.forex_bot.event_bus.config
        src.forex_bot.event_bus.config._event_bus_config = None
        
        # Mock app config
        mock_app_config = MagicMock()
        mock_app_config.instance_id = "test_instance"
        mock_get_app_config.return_value = mock_app_config
        
        config1 = get_event_bus_config()
        config2 = get_event_bus_config()
        
        # Should be the same instance (singleton behavior)
        assert config1 is config2
        
        # get_app_config should only be called once
        mock_get_app_config.assert_called_once()

    def test_kafka_config_edge_cases(self):
        """Test KafkaConfig edge cases and combinations."""
        # Test with all SSL fields
        config = KafkaConfig(
            security_protocol="SSL",
            ssl_cafile="/path/to/ca.pem",
            ssl_certfile="/path/to/cert.pem",
            ssl_keyfile="/path/to/key.pem"
        )
        assert config.security_protocol == "SSL"
        assert config.ssl_cafile == "/path/to/ca.pem"
        
        # Test with SASL_SSL combination
        config = KafkaConfig(
            security_protocol="SASL_SSL",
            sasl_mechanism="SCRAM-SHA-256",
            sasl_username="user",
            sasl_password="pass",
            ssl_cafile="/path/to/ca.pem"
        )
        assert config.security_protocol == "SASL_SSL"
        assert config.sasl_mechanism == "SCRAM-SHA-256"

    def test_topic_config_edge_cases(self):
        """Test TopicConfig edge cases."""
        # Test with custom topic names
        config = TopicConfig(
            market_data_topic="custom.market.data",
            order_topic="custom.orders",
            trade_topic="custom.trades",
            analysis_topic="custom.analysis",
            system_topic="custom.system"
        )
        
        # Test edge cases for get_topic_by_event_type
        assert config.get_topic_by_event_type(None) == "custom.system"  # None should return system
        assert config.get_topic_by_event_type("") == "custom.system"    # Empty string
        assert config.get_topic_by_event_type("MARKET_DATA") == "custom.system"  # Case sensitive

    def test_event_bus_config_edge_cases(self):
        """Test EventBusConfig edge cases."""
        # Test with all fields
        config = EventBusConfig(
            enabled=True,
            provider="kafka",
            kafka=KafkaConfig(
                bootstrap_servers="test:9092",
                client_id="test_client"
            ),
            topics=TopicConfig()
        )
        
        assert config.enabled is True
        assert config.provider == "kafka"
        assert config.kafka.bootstrap_servers == "test:9092"
        assert isinstance(config.topics, TopicConfig)