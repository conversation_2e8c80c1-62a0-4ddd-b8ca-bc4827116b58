"""
Comprehensive tests to push correlation_matrix/models.py to 100% coverage.

This module targets all remaining uncovered lines to achieve complete coverage.
"""

import pytest
import sys
from unittest.mock import patch, MagicMock
from datetime import datetime, timezone
from pydantic import ValidationError

class TestCorrelationMatrixModels100Percent:
    """Comprehensive tests to achieve 100% coverage for correlation_matrix/models.py."""

    def test_import_error_handling_numpy(self):
        """Test numpy import error handling - covers lines 14-15."""
        # Test the actual import behavior by checking the module state
        from src.forex_bot.correlation_matrix import models
        
        # The module should have NUMPY_AVAILABLE set based on actual import
        assert hasattr(models, 'NUMPY_AVAILABLE')
        assert isinstance(models.NUMPY_AVAILABLE, bool)

    def test_import_error_handling_pandas(self):
        """Test pandas import error handling - covers lines 20-21."""
        # Test the actual import behavior by checking the module state
        from src.forex_bot.correlation_matrix import models
        
        # The module should have PANDAS_AVAILABLE set based on actual import
        assert hasattr(models, 'PANDAS_AVAILABLE')
        assert isinstance(models.PANDAS_AVAILABLE, bool)

    def test_import_error_handling_pydantic(self):
        """Test pydantic import error handling - covers lines 26-35."""
        # Test the actual import behavior by checking the module state
        from src.forex_bot.correlation_matrix import models
        
        # The module should have PYDANTIC_AVAILABLE set based on actual import
        assert hasattr(models, 'PYDANTIC_AVAILABLE')
        assert isinstance(models.PYDANTIC_AVAILABLE, bool)
        
        # If pydantic is not available, test the fallback BaseModel
        if not models.PYDANTIC_AVAILABLE:
            BaseModel = models.BaseModel
            
            # Test __init__ method
            model = BaseModel(name="test", value=123)
            assert model.name == "test"
            assert model.value == 123            
            # Test dict method
            result = model.dict()
            assert result == {"name": "test", "value": 123}
            
            # Test that private attributes are excluded
            model._private = "hidden"
            result = model.dict()
            assert "_private" not in result

    def test_correlation_matrix_to_dataframe_pandas_error(self):
        """Test CorrelationMatrix to_dataframe pandas ImportError - covers lines 135-141."""
        from src.forex_bot.correlation_matrix.models import CorrelationMatrix, TimeWindow, CorrelationMethod
        
        # Create a valid CorrelationMatrix
        timestamp = datetime.now(timezone.utc)
        symbols = ["EURUSD", "GBPUSD"]
        matrix = {
            "EURUSD": {"EURUSD": 1.0, "GBPUSD": 0.8},
            "GBPUSD": {"EURUSD": 0.8, "GBPUSD": 1.0}
        }
        
        corr_matrix = CorrelationMatrix(
            timestamp=timestamp,
            time_window=TimeWindow.DAY_1,
            method=CorrelationMethod.PEARSON,
            symbols=symbols,
            matrix=matrix
        )
        
        # Mock PANDAS_AVAILABLE to be False
        with patch('src.forex_bot.correlation_matrix.models.PANDAS_AVAILABLE', False):
            with pytest.raises(ImportError) as exc_info:
                corr_matrix.to_dataframe()
            
            assert "pandas is required for to_dataframe()" in str(exc_info.value)

    def test_correlation_trend_to_dataframe_pandas_error(self):
        """Test CorrelationTrend to_dataframe pandas ImportError - covers lines 164-167."""
        from src.forex_bot.correlation_matrix.models import CorrelationTrend, TimeWindow, CorrelationMethod
        
        # Create a valid CorrelationTrend
        timestamps = [datetime.now(timezone.utc), datetime.now(timezone.utc)]
        correlations = [0.8, 0.9]
        
        trend = CorrelationTrend(
            symbol1="EURUSD",
            symbol2="GBPUSD",
            timestamps=timestamps,
            correlations=correlations,
            time_window=TimeWindow.DAY_1,
            method=CorrelationMethod.PEARSON
        )
        
        # Mock PANDAS_AVAILABLE to be False
        with patch('src.forex_bot.correlation_matrix.models.PANDAS_AVAILABLE', False):
            with pytest.raises(ImportError) as exc_info:
                trend.to_dataframe()
            
            assert "pandas is required for to_dataframe()" in str(exc_info.value)
    def test_correlation_alert_change_validation(self):
        """Test CorrelationAlert change validation - covers lines 186-191."""
        from src.forex_bot.correlation_matrix.models import CorrelationAlert, TimeWindow, CorrelationMethod
        
        with pytest.raises(ValidationError) as exc_info:
            CorrelationAlert(
                symbol1="EURUSD",
                symbol2="GBPUSD",
                old_correlation=0.5,
                new_correlation=0.8,
                change=0.2,  # Should be 0.3 (0.8 - 0.5)
                timestamp=datetime.now(timezone.utc),
                time_window=TimeWindow.DAY_1,
                method=CorrelationMethod.PEARSON,
                is_significant=True
            )
        
        assert "change should be" in str(exc_info.value)

    def test_correlation_visualization_image_data_validation(self):
        """Test CorrelationVisualization image_data validation - covers lines 203-316."""
        from src.forex_bot.correlation_matrix.models import CorrelationVisualization, TimeWindow, CorrelationMethod
        
        with pytest.raises(ValidationError) as exc_info:
            CorrelationVisualization(
                timestamp=datetime.now(timezone.utc),
                time_window=TimeWindow.DAY_1,
                method=CorrelationMethod.PEARSON,
                symbols=["EURUSD", "GBPUSD"],
                image_data=""  # Empty image data should fail
            )
        
        assert "image_data cannot be empty" in str(exc_info.value)

    def test_non_pydantic_correlation_settings_fallback(self):
        """Test non-pydantic CorrelationSettings fallback - covers lines 203-316."""
        # Mock PYDANTIC_AVAILABLE to be False to test fallback classes
        with patch('src.forex_bot.correlation_matrix.models.PYDANTIC_AVAILABLE', False):
            # Re-import the module to get the fallback classes
            import importlib
            from src.forex_bot.correlation_matrix import models
            importlib.reload(models)
            
            # Test CorrelationSettings fallback
            settings = models.CorrelationSettings(
                time_window=models.TimeWindow.HOUR_1,
                method=models.CorrelationMethod.SPEARMAN,
                min_periods=10,
                symbols=["EURUSD", "GBPUSD"],
                include_base_pairs=False
            )            
            assert settings.time_window == models.TimeWindow.HOUR_1
            assert settings.method == models.CorrelationMethod.SPEARMAN
            assert settings.min_periods == 10
            assert settings.symbols == ["EURUSD", "GBPUSD"]
            assert settings.include_base_pairs == False
            
            # Test dict method
            settings_dict = settings.dict()
            assert "time_window" in settings_dict
            assert "method" in settings_dict
            
            # Test validation error
            with pytest.raises(ValueError) as exc_info:
                models.CorrelationSettings(min_periods=1)  # Should be at least 2
            
            assert "min_periods must be at least 2" in str(exc_info.value)

    def test_successful_model_creation(self):
        """Test successful creation of all models to ensure basic functionality."""
        from src.forex_bot.correlation_matrix.models import (
            CorrelationSettings, CorrelationPair, CorrelationMatrix, CorrelationTrend,
            CorrelationAlert, CorrelationVisualization, TimeWindow, CorrelationMethod,
            CorrelationStrength
        )
        
        timestamp = datetime.now(timezone.utc)
        
        # Test CorrelationSettings
        settings = CorrelationSettings(
            time_window=TimeWindow.DAY_1,
            method=CorrelationMethod.PEARSON,
            min_periods=30,
            symbols=["EURUSD", "GBPUSD"],
            include_base_pairs=True
        )
        assert settings.time_window == TimeWindow.DAY_1
        
        # Test CorrelationPair
        pair = CorrelationPair(
            symbol1="EURUSD",
            symbol2="GBPUSD",
            correlation=0.8,
            strength=CorrelationStrength.STRONG_POSITIVE,
            timestamp=timestamp,
            time_window=TimeWindow.DAY_1,
            method=CorrelationMethod.PEARSON
        )
        assert pair.correlation == 0.8
        
        # Test CorrelationMatrix
        symbols = ["EURUSD", "GBPUSD"]
        matrix = {
            "EURUSD": {"EURUSD": 1.0, "GBPUSD": 0.8},
            "GBPUSD": {"EURUSD": 0.8, "GBPUSD": 1.0}
        }
        
        corr_matrix = CorrelationMatrix(
            timestamp=timestamp,
            time_window=TimeWindow.DAY_1,
            method=CorrelationMethod.PEARSON,
            symbols=symbols,
            matrix=matrix
        )
        assert corr_matrix.symbols == symbols        
        # Test CorrelationTrend
        timestamps = [timestamp, timestamp]
        correlations = [0.8, 0.9]
        
        trend = CorrelationTrend(
            symbol1="EURUSD",
            symbol2="GBPUSD",
            timestamps=timestamps,
            correlations=correlations,
            time_window=TimeWindow.DAY_1,
            method=CorrelationMethod.PEARSON
        )
        assert trend.correlations == correlations
        
        # Test CorrelationAlert
        alert = CorrelationAlert(
            symbol1="EURUSD",
            symbol2="GBPUSD",
            old_correlation=0.5,
            new_correlation=0.8,
            change=0.3,
            timestamp=timestamp,
            time_window=TimeWindow.DAY_1,
            method=CorrelationMethod.PEARSON,
            is_significant=True
        )
        assert alert.change == 0.3
        
        # Test CorrelationVisualization
        visualization = CorrelationVisualization(
            timestamp=timestamp,
            time_window=TimeWindow.DAY_1,
            method=CorrelationMethod.PEARSON,
            symbols=symbols,
            image_data="base64encodeddata"
        )
        assert visualization.image_data == "base64encodeddata"

    def test_correlation_matrix_to_dataframe_success(self):
        """Test CorrelationMatrix to_dataframe success case."""
        from src.forex_bot.correlation_matrix.models import CorrelationMatrix, TimeWindow, CorrelationMethod
        
        # Mock pandas to be available
        with patch('src.forex_bot.correlation_matrix.models.PANDAS_AVAILABLE', True):
            with patch('src.forex_bot.correlation_matrix.models.pd') as mock_pd:
                mock_dataframe = MagicMock()
                mock_pd.DataFrame.return_value = mock_dataframe
                mock_dataframe.reindex.return_value = mock_dataframe
                
                timestamp = datetime.now(timezone.utc)
                symbols = ["EURUSD", "GBPUSD"]
                matrix = {
                    "EURUSD": {"EURUSD": 1.0, "GBPUSD": 0.8},
                    "GBPUSD": {"EURUSD": 0.8, "GBPUSD": 1.0}
                }
                
                corr_matrix = CorrelationMatrix(
                    timestamp=timestamp,
                    time_window=TimeWindow.DAY_1,
                    method=CorrelationMethod.PEARSON,
                    symbols=symbols,
                    matrix=matrix
                )
                
                result = corr_matrix.to_dataframe()
                
                # Verify pandas DataFrame was called with correct data
                mock_pd.DataFrame.assert_called_once_with(matrix)
                mock_dataframe.reindex.assert_called_once_with(index=symbols, columns=symbols)
                assert result == mock_dataframe