"""
Phase 5BB: Test for market_depth_visualizer/models.py to push from 61% to 90%+ coverage.
"""

import pytest
from datetime import datetime, timezone
from unittest.mock import patch
from src.forex_bot.market_depth_visualizer.models import (
    MarketDepthSnapshot, VisualizationSettings
)


class TestMarketDepthPhase5BB:
    """Test class to push market_depth_visualizer/models.py to 90%+ coverage"""

    def test_validation_errors(self):
        """Test validation error scenarios"""
        
        now = datetime.now(timezone.utc)
        
        # Test empty bid prices validation
        with pytest.raises(ValueError, match="Bid prices cannot be empty"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[],
                bid_volumes=[],
                ask_prices=[1.2341],
                ask_volumes=[800.0]
            )
        
        # Test empty ask prices validation
        with pytest.raises(ValueError, match="Ask prices cannot be empty"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340],
                bid_volumes=[1000.0],
                ask_prices=[],
                ask_volumes=[]
            )

    def test_properties(self):
        """Test property calculations"""
        
        now = datetime.now(timezone.utc)
        
        snapshot = MarketDepthSnapshot(
            symbol="GBPUSD",
            timestamp=now,
            bid_prices=[1.3000, 1.2999],
            bid_volumes=[1000.0, 1500.0],
            ask_prices=[1.3001, 1.3002],
            ask_volumes=[800.0, 1200.0]
        )
        
        # Test properties
        assert snapshot.best_bid == 1.3000
        assert snapshot.best_ask == 1.3001
        assert abs(snapshot.spread - 0.0001) < 1e-10  # Use tolerance for floating point
        assert abs(snapshot.mid_price - 1.30005) < 1e-10  # Use tolerance for floating point
        assert snapshot.total_bid_volume == 2500.0
        assert snapshot.total_ask_volume == 2000.0
        
        # Test cumulative volumes
        assert snapshot.cumulative_bid_volumes == [1000.0, 2500.0]
        assert snapshot.cumulative_ask_volumes == [800.0, 2000.0]

    def test_edge_cases(self):
        """Test edge cases"""
        
        now = datetime.now(timezone.utc)
        
        # Test zero volume imbalance ratio
        zero_snapshot = MarketDepthSnapshot(
            symbol="EURUSD",
            timestamp=now,
            bid_prices=[1.2340],
            bid_volumes=[0.0],
            ask_prices=[1.2341],
            ask_volumes=[0.0]
        )
        assert zero_snapshot.imbalance_ratio is None
        
        # Test without numpy
        with patch('src.forex_bot.market_depth_visualizer.models.NUMPY_AVAILABLE', False):
            manual_snapshot = MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340, 1.2339],
                bid_volumes=[100.0, 200.0],
                ask_prices=[1.2341, 1.2342],
                ask_volumes=[150.0, 250.0]
            )
            
            assert manual_snapshot.cumulative_bid_volumes == [100.0, 300.0]
            assert manual_snapshot.cumulative_ask_volumes == [150.0, 400.0]

    def test_visualization_settings(self):
        """Test VisualizationSettings"""
        
        settings = VisualizationSettings()
        assert settings.depth_chart is not None
        assert settings.heatmap is not None
        assert settings.time_and_sales is not None
        assert settings.liquidity_map is not None
        assert settings.order_flow_footprint is not None
        assert settings.dashboard is not None