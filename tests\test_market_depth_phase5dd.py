"""
Phase 5DD: Test for market_depth_visualizer/models.py to push from 70% to 90%+ coverage.
Targeting all remaining fallback classes and edge cases.
"""

import pytest
from datetime import datetime, timezone
from unittest.mock import patch
from src.forex_bot.market_depth_visualizer.models import (
    MarketDepthSnapshot, MarketDepthVisualization, MarketDepthDashboard, 
    DashboardSettings, VisualizationSettings, VisualizationType
)


class TestMarketDepthPhase5DD:
    """Test class to push market_depth_visualizer/models.py to 90%+ coverage"""

    def test_comprehensive_fallback_classes(self):
        """Test all fallback classes when pydantic is unavailable"""
        
        # Test with pydantic unavailable to trigger all fallback classes
        with patch('src.forex_bot.market_depth_visualizer.models.PYDANTIC_AVAILABLE', False):
            # Force module reload to use fallback classes
            import importlib
            import src.forex_bot.market_depth_visualizer.models as models_module
            importlib.reload(models_module)
            
            # Test all fallback TimeAndSalesSettings parameters
            time_sales = models_module.TimeAndSalesSettings(
                max_entries=150,
                show_direction=False,
                show_time=False,
                show_price=False,
                show_volume=False,
                color_scheme=models_module.ColorScheme.LIGHT,
                custom_buy_color="#AAFFAA",
                custom_sell_color="#FFAAAA",
                highlight_large_trades=False,
                large_trade_threshold=3.5
            )
            assert time_sales.max_entries == 150
            assert time_sales.show_direction is False
            assert time_sales.show_time is False
            assert time_sales.show_price is False
            assert time_sales.show_volume is False
            assert time_sales.custom_buy_color == "#AAFFAA"
            assert time_sales.custom_sell_color == "#FFAAAA"
            assert time_sales.highlight_large_trades is False
            assert time_sales.large_trade_threshold == 3.5
            
            # Test dict method
            time_sales_dict = time_sales.dict()
            assert time_sales_dict["max_entries"] == 150
            assert time_sales_dict["show_direction"] is False            # Test all fallback LiquidityMapSettings parameters
            liquidity = models_module.LiquidityMapSettings(
                price_levels=35,
                show_bid_liquidity=False,
                show_ask_liquidity=False,
                color_scheme=models_module.ColorScheme.CUSTOM,
                custom_bid_color="#CCCCFF",
                custom_ask_color="#FFCCCC",
                show_tooltips=False,
                normalization="log"
            )
            assert liquidity.price_levels == 35
            assert liquidity.show_bid_liquidity is False
            assert liquidity.show_ask_liquidity is False
            assert liquidity.custom_bid_color == "#CCCCFF"
            assert liquidity.custom_ask_color == "#FFCCCC"
            assert liquidity.show_tooltips is False
            assert liquidity.normalization == "log"
            
            # Test dict method
            liquidity_dict = liquidity.dict()
            assert liquidity_dict["price_levels"] == 35
            assert liquidity_dict["normalization"] == "log"
            
            # Test all fallback OrderFlowFootprintSettings parameters
            footprint = models_module.OrderFlowFootprintSettings(
                price_levels=40,
                time_window=180,
                show_imbalances=False,
                color_scheme=models_module.ColorScheme.COLORBLIND,
                custom_buy_color="#DDFFDD",
                custom_sell_color="#FFDDDD",
                show_tooltips=False,
                show_delta=False,
                delta_type="trades"
            )
            assert footprint.price_levels == 40
            assert footprint.time_window == 180
            assert footprint.show_imbalances is False
            assert footprint.custom_buy_color == "#DDFFDD"
            assert footprint.custom_sell_color == "#FFDDDD"
            assert footprint.show_tooltips is False
            assert footprint.show_delta is False
            assert footprint.delta_type == "trades"
            
            # Test dict method
            footprint_dict = footprint.dict()
            assert footprint_dict["price_levels"] == 40
            assert footprint_dict["delta_type"] == "trades"

    def test_remaining_edge_cases(self):
        """Test remaining edge cases and validators"""
        
        now = datetime.now(timezone.utc)
        
        # Test specific validator edge cases (lines 165, 177)
        with pytest.raises(ValueError, match="Bid volumes must have the same length as bid prices"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340, 1.2339, 1.2338, 1.2337, 1.2336],
                bid_volumes=[1000.0, 1500.0, 2000.0],  # Different length
                ask_prices=[1.2341],
                ask_volumes=[800.0]
            )
        
        with pytest.raises(ValueError, match="Ask volumes must have the same length as ask prices"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340],
                bid_volumes=[1000.0],
                ask_prices=[1.2341, 1.2342, 1.2343, 1.2344, 1.2345],
                ask_volumes=[800.0, 1200.0, 1600.0]  # Different length
            )