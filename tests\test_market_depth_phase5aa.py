"""
Phase 5AA: Test for market_depth_visualizer/models.py to push from 60% to 90%+ coverage.
"""

import pytest
from datetime import datetime, timezone
from src.forex_bot.market_depth_visualizer.models import (
    VisualizationType, ColorScheme, HeatmapSettings, TimeAndSalesSettings,
    LiquidityMapSettings, OrderFlowFootprintSettings, DashboardSettings,
    VisualizationSettings, MarketDepthSnapshot, MarketDepthDashboard
)


class TestMarketDepthPhase5AA:
    """Test class to push market_depth_visualizer/models.py to 90%+ coverage"""

    def test_heatmap_settings(self):
        """Test HeatmapSettings"""
        heatmap = HeatmapSettings(
            price_levels=25,
            time_window=120,
            color_scheme=ColorScheme.DARK,
            custom_colormap="plasma",
            show_current_price=False,
            show_tooltips=False,
            interpolation="bilinear",
            normalization="log"
        )
        assert heatmap.price_levels == 25
        assert heatmap.normalization == "log"

    def test_time_and_sales_settings(self):
        """Test TimeAndSalesSettings"""
        time_sales = TimeAndSalesSettings(
            max_entries=200,
            show_direction=False,
            color_scheme=ColorScheme.COLORBLIND,
            custom_buy_color="#00FF00",
            custom_sell_color="#FF0000",
            highlight_large_trades=False,
            large_trade_threshold=3.0
        )
        assert time_sales.max_entries == 200
        assert time_sales.large_trade_threshold == 3.0

    def test_liquidity_map_settings(self):
        """Test LiquidityMapSettings"""
        liquidity = LiquidityMapSettings(
            price_levels=15,
            show_bid_liquidity=False,
            show_ask_liquidity=False,
            color_scheme=ColorScheme.LIGHT,
            custom_bid_color="#0000FF",
            custom_ask_color="#FF00FF",
            show_tooltips=False,
            normalization="log"
        )
        assert liquidity.price_levels == 15
        assert liquidity.normalization == "log"

    def test_order_flow_footprint_settings(self):
        """Test OrderFlowFootprintSettings"""
        footprint = OrderFlowFootprintSettings(
            price_levels=30,
            time_window=90,
            show_imbalances=False,
            color_scheme=ColorScheme.CUSTOM,
            custom_buy_color="#AAFFAA",
            custom_sell_color="#FFAAAA",
            show_tooltips=False,
            show_delta=False,
            delta_type="trades"
        )
        assert footprint.price_levels == 30
        assert footprint.delta_type == "trades"

    def test_dashboard_settings(self):
        """Test DashboardSettings"""
        custom_layout = [
            [VisualizationType.DEPTH_CHART, VisualizationType.HEATMAP],
            [VisualizationType.TIME_AND_SALES]
        ]
        
        dashboard = DashboardSettings(
            layout=custom_layout,
            refresh_interval=500,
            show_title=False,
            show_timestamp=False,
            color_scheme=ColorScheme.DARK,
            custom_background_color="#000000",
            custom_text_color="#FFFFFF"
        )
        assert dashboard.refresh_interval == 500
        assert dashboard.color_scheme == ColorScheme.DARK

    def test_market_depth_snapshot_zero_volume(self):
        """Test MarketDepthSnapshot with zero volume edge case"""
        now = datetime.now(timezone.utc)
        
        zero_snapshot = MarketDepthSnapshot(
            symbol="EURUSD",
            timestamp=now,
            bid_prices=[1.2340],
            bid_volumes=[0.0],
            ask_prices=[1.2341],
            ask_volumes=[0.0]
        )
        assert zero_snapshot.imbalance_ratio is None