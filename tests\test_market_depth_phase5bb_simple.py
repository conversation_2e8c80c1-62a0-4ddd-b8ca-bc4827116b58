"""
Phase 5BB: Simple test for market_depth_visualizer/models.py to push from 61% to 90%+ coverage.
Targeting validation methods, properties, and edge cases.
"""

import pytest
from datetime import datetime, timezone
from unittest.mock import patch
from src.forex_bot.market_depth_visualizer.models import (
    VisualizationType, ColorScheme, MarketDepthSnapshot, MarketDepthVisualization,
    MarketDepthDashboard, DashboardSettings, VisualizationSettings
)


class TestMarketDepthPhase5BBSimple:
    """Simple test to push market_depth_visualizer/models.py to 90%+ coverage"""

    def test_validation_errors(self):
        """Test validation error scenarios"""
        
        now = datetime.now(timezone.utc)
        
        # Test empty bid prices validation (line 159)
        with pytest.raises(ValueError, match="Bid prices cannot be empty"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[],
                bid_volumes=[],
                ask_prices=[1.2341],
                ask_volumes=[800.0]
            )
        
        # Test empty ask prices validation (line 171)
        with pytest.raises(ValueError, match="Ask prices cannot be empty"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340],
                bid_volumes=[1000.0],
                ask_prices=[],
                ask_volumes=[]
            )
        
        # Test bid volumes length validation (line 165)
        with pytest.raises(ValueError, match="Bid volumes must have the same length as bid prices"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340, 1.2339],
                bid_volumes=[1000.0],  # Length mismatch
                ask_prices=[1.2341],
                ask_volumes=[800.0]
            )
        
        # Test ask volumes length validation (line 177)
        with pytest.raises(ValueError, match="Ask volumes must have the same length as ask prices"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340],
                bid_volumes=[1000.0],
                ask_prices=[1.2341, 1.2342],
                ask_volumes=[800.0]  # Length mismatch
            )    def test_properties_comprehensive(self):
        """Test comprehensive property calculations"""
        
        now = datetime.now(timezone.utc)
        
        # Test all properties with valid data
        snapshot = MarketDepthSnapshot(
            symbol="GBPUSD",
            timestamp=now,
            bid_prices=[1.3000, 1.2999, 1.2998],
            bid_volumes=[1000.0, 1500.0, 2000.0],
            ask_prices=[1.3001, 1.3002, 1.3003],
            ask_volumes=[800.0, 1200.0, 1600.0]
        )
        
        # Test best_bid property (lines 193-195)
        assert snapshot.best_bid == 1.3000
        
        # Test best_ask property (lines 200-202)
        assert snapshot.best_ask == 1.3001
        
        # Test spread property
        assert snapshot.spread == 0.0001
        
        # Test mid_price property
        assert snapshot.mid_price == 1.30005
        
        # Test total volumes
        assert snapshot.total_bid_volume == 4500.0
        assert snapshot.total_ask_volume == 3600.0
        
        # Test imbalance_ratio property (line 225)
        expected_imbalance = (4500.0 - 3600.0) / (4500.0 + 3600.0)
        assert abs(snapshot.imbalance_ratio - expected_imbalance) < 1e-10
        
        # Test cumulative volumes (lines 230-238)
        expected_cum_bid = [1000.0, 2500.0, 4500.0]
        expected_cum_ask = [800.0, 2000.0, 3600.0]
        assert snapshot.cumulative_bid_volumes == expected_cum_bid
        assert snapshot.cumulative_ask_volumes == expected_cum_ask

    def test_edge_cases(self):
        """Test edge cases and fallback behavior"""
        
        now = datetime.now(timezone.utc)
        
        # Test zero volume imbalance ratio (returns None)
        zero_snapshot = MarketDepthSnapshot(
            symbol="EURUSD",
            timestamp=now,
            bid_prices=[1.2340],
            bid_volumes=[0.0],
            ask_prices=[1.2341],
            ask_volumes=[0.0]
        )
        assert zero_snapshot.imbalance_ratio is None
        
        # Test cumulative volumes without numpy (lines 243-251)
        with patch('src.forex_bot.market_depth_visualizer.models.NUMPY_AVAILABLE', False):
            manual_snapshot = MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340, 1.2339, 1.2338],
                bid_volumes=[100.0, 200.0, 300.0],
                ask_prices=[1.2341, 1.2342, 1.2343],
                ask_volumes=[150.0, 250.0, 350.0]
            )
            
            # Test manual cumulative calculation
            expected_cum_bid = [100.0, 300.0, 600.0]
            expected_cum_ask = [150.0, 400.0, 750.0]
            assert manual_snapshot.cumulative_bid_volumes == expected_cum_bid
            assert manual_snapshot.cumulative_ask_volumes == expected_cum_ask

    def test_comprehensive_models(self):
        """Test comprehensive model functionality"""
        
        now = datetime.now(timezone.utc)
        
        # Test VisualizationSettings with defaults
        default_settings = VisualizationSettings()
        assert default_settings.depth_chart is not None
        assert default_settings.heatmap is not None
        assert default_settings.time_and_sales is not None
        assert default_settings.liquidity_map is not None
        assert default_settings.order_flow_footprint is not None
        assert default_settings.dashboard is not None
        
        # Test MarketDepthVisualization
        viz = MarketDepthVisualization(
            symbol="EURUSD",
            timestamp=now,
            visualization_type=VisualizationType.DEPTH_CHART,
            image_data="base64_encoded_data",
            settings={"price_levels": 20, "color_scheme": "default"}
        )
        assert viz.symbol == "EURUSD"
        assert viz.visualization_type == VisualizationType.DEPTH_CHART
        assert viz.settings["price_levels"] == 20
        
        # Test MarketDepthDashboard
        dashboard = MarketDepthDashboard(
            symbol="EURUSD",
            timestamp=now,
            visualizations={VisualizationType.DEPTH_CHART: viz},
            settings=DashboardSettings()
        )
        assert dashboard.symbol == "EURUSD"
        assert len(dashboard.visualizations) == 1