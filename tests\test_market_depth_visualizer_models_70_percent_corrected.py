"""
Targeted tests to push market_depth_visualizer/models.py to 70%+ coverage.
"""

import pytest
from datetime import datetime, timezone
from src.forex_bot.market_depth_visualizer.models import (
    VisualizationType, ColorScheme, DepthChartSettings, HeatmapSettings,
    TimeAndSalesSettings, LiquidityMapSettings, OrderFlowFootprintSettings,
    DashboardSettings, VisualizationSettings, TradeEntry, MarketDepthSnapshot
)


class TestTradeEntry:
    """Test TradeEntry model."""
    
    def test_trade_entry_creation_valid(self):
        """Test valid TradeEntry creation."""
        trade = TradeEntry(
            timestamp=datetime.now(timezone.utc),
            price=1.1000,
            volume=100.0,
            direction="buy",
            is_large=True
        )
        
        assert trade.price == 1.1000
        assert trade.volume == 100.0
        assert trade.direction == "buy"
        assert trade.is_large is True
    
    def test_trade_entry_invalid_volume(self):
        """Test TradeEntry with invalid volume."""
        with pytest.raises(ValueError, match="Volume must be positive"):
            TradeEntry(
                timestamp=datetime.now(timezone.utc),
                price=1.1000,
                volume=-100.0,  # Invalid: negative volume
                direction="buy"
            )class TestMarketDepthSnapshot:
    """Test MarketDepthSnapshot model."""
    
    def test_market_depth_snapshot_creation_valid(self):
        """Test valid MarketDepthSnapshot creation."""
        snapshot = MarketDepthSnapshot(
            symbol="EURUSD",
            timestamp=datetime.now(timezone.utc),
            bid_prices=[1.0990, 1.0989, 1.0988],
            bid_volumes=[100.0, 200.0, 150.0],
            ask_prices=[1.1000, 1.1001, 1.1002],
            ask_volumes=[120.0, 180.0, 160.0]
        )
        
        assert snapshot.symbol == "EURUSD"
        assert len(snapshot.bid_prices) == 3
        assert len(snapshot.bid_volumes) == 3
        assert len(snapshot.ask_prices) == 3
        assert len(snapshot.ask_volumes) == 3
    
    def test_market_depth_snapshot_invalid_empty_bid_prices(self):
        """Test MarketDepthSnapshot with empty bid prices."""
        with pytest.raises(ValueError, match="Bid prices cannot be empty"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=datetime.now(timezone.utc),
                bid_prices=[],  # Invalid: empty
                bid_volumes=[],
                ask_prices=[1.1000],
                ask_volumes=[100.0]
            )
    
    def test_market_depth_snapshot_invalid_empty_ask_prices(self):
        """Test MarketDepthSnapshot with empty ask prices."""
        with pytest.raises(ValueError, match="Ask prices cannot be empty"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=datetime.now(timezone.utc),
                bid_prices=[1.0990],
                bid_volumes=[100.0],
                ask_prices=[],  # Invalid: empty
                ask_volumes=[]
            )    
    def test_market_depth_snapshot_invalid_bid_volumes_length(self):
        """Test MarketDepthSnapshot with mismatched bid volumes length."""
        with pytest.raises(ValueError, match="Bid volumes must have the same length as bid prices"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=datetime.now(timezone.utc),
                bid_prices=[1.0990, 1.0989],
                bid_volumes=[100.0],  # Invalid: different length
                ask_prices=[1.1000],
                ask_volumes=[100.0]
            )
    
    def test_market_depth_snapshot_invalid_ask_volumes_length(self):
        """Test MarketDepthSnapshot with mismatched ask volumes length."""
        with pytest.raises(ValueError, match="Ask volumes must have the same length as ask prices"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=datetime.now(timezone.utc),
                bid_prices=[1.0990],
                bid_volumes=[100.0],
                ask_prices=[1.1000, 1.1001],
                ask_volumes=[100.0]  # Invalid: different length
            )
    
    def test_market_depth_snapshot_properties(self):
        """Test MarketDepthSnapshot property methods."""
        snapshot = MarketDepthSnapshot(
            symbol="EURUSD",
            timestamp=datetime.now(timezone.utc),
            bid_prices=[1.0990, 1.0989],
            bid_volumes=[100.0, 200.0],
            ask_prices=[1.1000, 1.1001],
            ask_volumes=[120.0, 180.0]
        )
        
        # Test best_bid property
        assert snapshot.best_bid == 1.0990
        
        # Test best_ask property
        assert snapshot.best_ask == 1.1000
        
        # Test spread property
        assert snapshot.spread == 0.0010
        
        # Test mid_price property
        assert snapshot.mid_price == 1.0995
        
        # Test total_bid_volume property
        assert snapshot.total_bid_volume == 300.0
        
        # Test total_ask_volume property
        assert snapshot.total_ask_volume == 300.0
        
        # Test imbalance_ratio property
        assert snapshot.imbalance_ratio == 0.0  # Equal volumes    
    def test_market_depth_snapshot_empty_volumes(self):
        """Test MarketDepthSnapshot with zero volumes."""
        snapshot = MarketDepthSnapshot(
            symbol="EURUSD",
            timestamp=datetime.now(timezone.utc),
            bid_prices=[1.0990],
            bid_volumes=[0.0],  # Zero volume
            ask_prices=[1.1000],
            ask_volumes=[0.0]   # Zero volume
        )
        
        # Test imbalance_ratio with zero total volume
        assert snapshot.imbalance_ratio is None


class TestDepthChartSettings:
    """Test DepthChartSettings model."""
    
    def test_depth_chart_settings_creation_valid(self):
        """Test valid DepthChartSettings creation."""
        settings = DepthChartSettings(
            price_levels=10,
            show_imbalances=True,
            show_current_price=True,
            color_scheme=ColorScheme.DARK,
            log_scale=False,
            show_tooltips=True,
            highlight_large_orders=True,
            large_order_threshold=2.0
        )
        
        assert settings.price_levels == 10
        assert settings.show_imbalances is True
        assert settings.show_current_price is True
        assert settings.color_scheme == ColorScheme.DARK
        assert settings.log_scale is False
        assert settings.show_tooltips is True
        assert settings.highlight_large_orders is True
        assert settings.large_order_threshold == 2.0


class TestHeatmapSettings:
    """Test HeatmapSettings model."""
    
    def test_heatmap_settings_creation_valid(self):
        """Test valid HeatmapSettings creation."""
        settings = HeatmapSettings(
            price_levels=20,
            time_window=60,
            color_scheme=ColorScheme.LIGHT,
            show_current_price=True,
            show_tooltips=True,
            interpolation="nearest",
            normalization="linear"
        )
        
        assert settings.price_levels == 20
        assert settings.time_window == 60
        assert settings.color_scheme == ColorScheme.LIGHT
        assert settings.show_current_price is True
        assert settings.show_tooltips is True
        assert settings.interpolation == "nearest"
        assert settings.normalization == "linear"