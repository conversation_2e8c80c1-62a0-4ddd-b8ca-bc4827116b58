"""
Phase 2 enhanced tests to push market_depth_visualizer/models.py to 90%+ coverage.

This module targets all remaining uncovered lines to achieve comprehensive coverage.
"""

import pytest
from unittest.mock import patch
from datetime import datetime, timezone

class TestMarketDepthVisualizerModelsPhase2:
    """Phase 2 tests to achieve 90%+ coverage for market_depth_visualizer/models.py."""

    def test_non_pydantic_depth_chart_settings_comprehensive(self):
        """Test non-pydantic DepthChartSettings comprehensive functionality."""
        with patch('src.forex_bot.market_depth_visualizer.models.PYDANTIC_AVAILABLE', False):
            import importlib
            from src.forex_bot.market_depth_visualizer import models
            importlib.reload(models)
            
            # Test successful creation with all parameters
            settings = models.DepthChartSettings(
                price_levels=15,
                show_imbalances=False,
                show_current_price=False,
                color_scheme=models.ColorScheme.DARK,
                custom_bid_color="#FF0000",
                custom_ask_color="#00FF00",
                log_scale=True,
                show_tooltips=False,
                highlight_large_orders=False,
                large_order_threshold=3.0
            )
            
            assert settings.price_levels == 15
            assert settings.show_imbalances == False
            assert settings.show_current_price == False
            assert settings.color_scheme == models.ColorScheme.DARK
            assert settings.custom_bid_color == "#FF0000"
            assert settings.custom_ask_color == "#00FF00"
            assert settings.log_scale == True
            assert settings.show_tooltips == False
            assert settings.highlight_large_orders == False
            assert settings.large_order_threshold == 3.0
            
            # Test dict method
            settings_dict = settings.dict()
            assert isinstance(settings_dict, dict)
            assert settings_dict['price_levels'] == 15
            assert settings_dict['show_imbalances'] == False
            assert settings_dict['color_scheme'] == models.ColorScheme.DARK

    def test_non_pydantic_default_values(self):
        """Test non-pydantic classes with default values."""
        with patch('src.forex_bot.market_depth_visualizer.models.PYDANTIC_AVAILABLE', False):
            import importlib
            from src.forex_bot.market_depth_visualizer import models
            importlib.reload(models)
            
            # Test default values for all classes
            depth_settings = models.DepthChartSettings()
            assert depth_settings.price_levels == 10
            assert depth_settings.show_imbalances == True
            assert depth_settings.color_scheme == models.ColorScheme.DEFAULT