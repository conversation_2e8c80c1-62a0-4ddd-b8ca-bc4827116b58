"""
Phase 6B: Simple test for market_depth_visualizer/models.py to push from 54% to 90%+ coverage.
"""

import pytest
from datetime import datetime, timezone
from src.forex_bot.market_depth_visualizer.models import (
    MarketDepthSnapshot, MarketDepthVisualization, MarketDepthDashboard,
    DashboardSettings, VisualizationType
)


class TestMarketDepthPhase6BSimple:
    """Simple test to push market_depth_visualizer/models.py to 90%+ coverage"""

    def test_validator_edge_cases(self):
        """Test validator edge cases and error conditions"""
        
        now = datetime.now(timezone.utc)
        
        # Test MarketDepthSnapshot bid_prices validator
        with pytest.raises(ValueError, match="Bid prices cannot be empty"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[],  # Empty bid prices
                bid_volumes=[],
                ask_prices=[1.2341],
                ask_volumes=[800.0]
            )
        
        # Test MarketDepthSnapshot ask_prices validator
        with pytest.raises(ValueError, match="Ask prices cannot be empty"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340],
                bid_volumes=[1000.0],
                ask_prices=[],  # Empty ask prices
                ask_volumes=[]
            )
        
        # Test MarketDepthVisualization image_data validator
        with pytest.raises(ValueError, match="image_data cannot be empty"):
            MarketDepthVisualization(
                type=VisualizationType.DEPTH_CHART,
                image_data="",  # Empty image data
                metadata={}
            )
        
        # Test MarketDepthDashboard visualizations validator
        with pytest.raises(ValueError, match="Visualizations cannot be empty"):
            MarketDepthDashboard(
                symbol="EURUSD",
                timestamp=now,
                visualizations={},  # Empty visualizations
                settings=DashboardSettings()
            )    def test_property_calculations(self):
        """Test property calculations and edge cases"""
        
        now = datetime.now(timezone.utc)
        
        # Test normal snapshot with property calculations
        snapshot = MarketDepthSnapshot(
            symbol="EURUSD",
            timestamp=now,
            bid_prices=[1.2340, 1.2339, 1.2338],
            bid_volumes=[1000.0, 1500.0, 2000.0],
            ask_prices=[1.2341, 1.2342, 1.2343],
            ask_volumes=[800.0, 1200.0, 1600.0]
        )
        
        # Test best_bid property
        assert snapshot.best_bid == 1.2340  # Highest bid price
        
        # Test best_ask property
        assert snapshot.best_ask == 1.2341  # Lowest ask price
        
        # Test spread property calculation
        expected_spread = 1.2341 - 1.2340
        assert abs(snapshot.spread - expected_spread) < 1e-10
        
        # Test mid_price property calculation
        expected_mid = (1.2340 + 1.2341) / 2
        assert abs(snapshot.mid_price - expected_mid) < 1e-10
        
        # Test total_bid_volume property
        expected_bid_volume = 1000.0 + 1500.0 + 2000.0
        assert snapshot.total_bid_volume == expected_bid_volume
        
        # Test total_ask_volume property
        expected_ask_volume = 800.0 + 1200.0 + 1600.0
        assert snapshot.total_ask_volume == expected_ask_volume
        
        # Test imbalance_ratio calculation
        total_bid = 4500.0
        total_ask = 3600.0
        expected_imbalance = (total_bid - total_ask) / (total_bid + total_ask)
        assert abs(snapshot.imbalance_ratio - expected_imbalance) < 1e-10

    def test_zero_volume_edge_case(self):
        """Test zero volume edge case for imbalance ratio"""
        
        now = datetime.now(timezone.utc)
        
        # Test zero total volume edge case
        snapshot_zero = MarketDepthSnapshot(
            symbol="GBPUSD",
            timestamp=now,
            bid_prices=[1.3000],
            bid_volumes=[0.0],  # Zero volume
            ask_prices=[1.3001],
            ask_volumes=[0.0]   # Zero volume
        )
        # Should return None for zero total volume
        assert snapshot_zero.imbalance_ratio is None