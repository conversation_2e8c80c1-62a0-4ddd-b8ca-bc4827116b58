"""
Final tests for signal_generator module to reach 90% coverage.

This module targets the remaining uncovered lines including:
- Import error handling paths
- Specific feature flag conditionals
- Backward compatibility function edge cases
- Error handling in analysis modules
"""

import pytest
import pandas as pd
import logging
from datetime import datetime, timezone
from unittest.mock import Mock, MagicMock, patch, PropertyMock
from typing import Dict, Any, List

from src.forex_bot.signal_generator import SignalGenerator


class TestSignalGeneratorFinalCoverage:
    """Final test cases to achieve 90% coverage of SignalGenerator."""

    @pytest.fixture
    def mock_logger(self):
        """Create a mock logger adapter."""
        return Mock(spec=logging.LoggerAdapter)

    @pytest.fixture
    def signal_generator(self, mock_logger):
        """Create a SignalGenerator instance with mocked logger."""
        return SignalGenerator(mock_logger)

    @pytest.fixture
    def sample_dataframes(self):
        """Create sample DataFrames for testing."""
        data = {
            'time': [datetime.now(timezone.utc)],
            'open': [1.1000],
            'high': [1.1010],
            'low': [1.0990],
            'close': [1.1005],
            'volume': [1000]
        }
        df = pd.DataFrame(data)
        return df.copy(), df.copy(), df.copy()

    def test_import_error_handling_coverage(self):
        """Test that import error handling paths are covered."""
        # This test ensures the import error handling blocks are executed
        # by checking that the availability flags exist and are boolean
        from src.forex_bot import signal_generator
        
        # Test all availability flags
        flags = [
            'ORDER_FLOW_ANALYZER_AVAILABLE',
            'MARKET_DEPTH_VISUALIZER_AVAILABLE', 
            'CORRELATION_MATRIX_AVAILABLE',
            'METRICS_DASHBOARD_AVAILABLE',
            'MULTILINGUAL_NEWS_AVAILABLE'
        ]
        
        for flag in flags:
            assert hasattr(signal_generator, flag)
            flag_value = getattr(signal_generator, flag)
            assert isinstance(flag_value, bool)
            # The flag can be either True or False depending on actual imports

    @patch('src.forex_bot.signal_generator.config')
    def test_specific_feature_flag_combinations(self, mock_config, signal_generator, sample_dataframes):
        """Test specific feature flag combinations that weren't covered."""
        df_m5, df_h1, df_h4 = sample_dataframes
        symbol = "EURUSD"
        now_utc = datetime.now(timezone.utc)
        
        # Test combination where only some features are enabled
        mock_config.enable_garch_forecast = True
        mock_config.enable_hmm_regime = True
        mock_config.enable_heikin_ashi = False
        mock_config.enable_sentiment_analysis = True
        mock_config.enable_volume_profile = False
        mock_config.enable_vwap_analysis = True
        mock_config.enable_cvd_analysis = False
        mock_config.enable_cot_analysis = True
        mock_config.enable_pmi_analysis = False
        mock_config.enable_volatility_analysis = True
        
        with patch.multiple(
            'src.forex_bot.signal_generator',
            garch_model=Mock(),
            hmm_model=Mock(),
            sentiment_analyzer=Mock(),
            vwap_calculator=Mock(),
            cot_analyzer=Mock(),
            volatility_analyzer=Mock()
        ) as mocks:
            
            # Configure mocks to return data
            mocks['garch_model'].get_volatility_context.return_value = {'volatility': 0.01}
            mocks['hmm_model'].get_hmm_context.return_value = {'regime': 'trending'}
            mocks['sentiment_analyzer'].get_sentiment_context.return_value = {'sentiment': 'positive'}
            mocks['vwap_calculator'].get_vwap_context.return_value = {'vwap': 1.1002}
            mocks['cot_analyzer'].get_cot_context.return_value = {'cot_sentiment': 'bullish'}
            mocks['volatility_analyzer'].get_volatility_context.return_value = {'volatility_regime': 'high'}
            
            result = signal_generator.run_analysis_modules(
                symbol, df_m5, df_h1, df_h4, now_utc
            )
            
            assert isinstance(result, dict)

    def test_error_handling_in_specific_analysis_paths(self, signal_generator, sample_dataframes):
        """Test error handling in specific analysis paths that weren't covered."""
        df_m5, df_h1, df_h4 = sample_dataframes
        symbol = "EURUSD"
        now_utc = datetime.now(timezone.utc)
        
        # Test with modules that raise specific exceptions during context retrieval
        with patch.multiple(
            'src.forex_bot.signal_generator',
            garch_model=Mock(),
            hmm_model=Mock(),
            ha_calculator=Mock(),
            sentiment_analyzer=Mock(),
            volume_profile_calculator=Mock(),
            vwap_calculator=Mock(),
            cvd_calculator=Mock(),
            cot_analyzer=Mock(),
            pmi_analyzer=Mock(),
            volatility_analyzer=Mock()
        ) as mocks:
            
            # Make context retrieval methods raise exceptions
            mocks['garch_model'].get_volatility_context.side_effect = Exception("GARCH context error")
            mocks['hmm_model'].get_hmm_context.side_effect = ValueError("HMM context error")
            mocks['ha_calculator'].get_ha_context.side_effect = RuntimeError("HA context error")
            mocks['sentiment_analyzer'].get_sentiment_context.side_effect = ConnectionError("Sentiment context error")
            mocks['volume_profile_calculator'].get_volume_profile_context.side_effect = Exception("VP context error")
            mocks['vwap_calculator'].get_vwap_context.side_effect = Exception("VWAP context error")
            mocks['cvd_calculator'].get_cvd_context.side_effect = Exception("CVD context error")
            mocks['cot_analyzer'].get_cot_context.side_effect = Exception("COT context error")
            mocks['pmi_analyzer'].get_pmi_context.side_effect = Exception("PMI context error")
            mocks['volatility_analyzer'].get_volatility_context.side_effect = Exception("Volatility context error")
            
            # Enable all features to trigger all error paths
            with patch('src.forex_bot.signal_generator.config') as mock_config:
                mock_config.enable_garch_forecast = True
                mock_config.enable_hmm_regime = True
                mock_config.enable_heikin_ashi = True
                mock_config.enable_sentiment_analysis = True
                mock_config.enable_volume_profile = True
                mock_config.enable_vwap_analysis = True
                mock_config.enable_cvd_analysis = True
                mock_config.enable_cot_analysis = True
                mock_config.enable_pmi_analysis = True
                mock_config.enable_volatility_analysis = True
                
                result = signal_generator.run_analysis_modules(
                    symbol, df_m5, df_h1, df_h4, now_utc
                )
                
                # Should handle all errors gracefully
                assert isinstance(result, dict)
                # Should have logged multiple errors
                assert signal_generator.adapter.error.call_count >= 5

    def test_backward_compatibility_functions_edge_cases(self):
        """Test backward compatibility functions with specific edge cases."""
        from src.forex_bot.signal_generator import (
            run_analysis_modules,
            get_knowledge_base_context,
            prepare_analysis_context,
            generate_signal
        )
        
        mock_logger = Mock(spec=logging.LoggerAdapter)
        
        # Test run_analysis_modules with None values
        df = pd.DataFrame({'close': [1.1]})
        symbol = "EURUSD"
        now_utc = datetime.now(timezone.utc)
        
        # Test with None macro_info explicitly
        result = run_analysis_modules(symbol, df, df, df, now_utc, None, mock_logger)
        assert isinstance(result, dict)
        
        # Test with empty macro_info
        result = run_analysis_modules(symbol, df, df, df, now_utc, {}, mock_logger)
        assert isinstance(result, dict)
        
        # Test get_knowledge_base_context with different top_k values
        for top_k in [0, 1, 5, 10, 100]:
            result = get_knowledge_base_context(symbol, "test query", mock_logger, top_k)
            assert isinstance(result, str)
        
        # Test prepare_analysis_context with None values
        result = prepare_analysis_context(symbol, df, df, 5, None, None, mock_logger)
        assert isinstance(result, dict)
        
        # Test generate_signal with None model parameters explicitly
        result = generate_signal(symbol, {}, mock_logger, None, None)
        assert isinstance(result, str)
        assert result in ["BUY", "SELL", "HOLD"]

    def test_advanced_feature_error_handling(self, signal_generator, sample_dataframes):
        """Test error handling in advanced features."""
        df_m5, df_h1, df_h4 = sample_dataframes
        symbol = "EURUSD"
        now_utc = datetime.now(timezone.utc)
        
        # Test with advanced features enabled but modules raising errors
        with patch.multiple(
            'src.forex_bot.signal_generator',
            ENABLE_MULTILINGUAL_NEWS=True,
            ENABLE_ORDER_FLOW_ANALYSIS=True,
            ENABLE_MARKET_DEPTH_VISUALIZATION=True,
            ENABLE_CORRELATION_ANALYSIS=True,
            ENABLE_METRICS_DASHBOARD=True
        ):
            with patch.multiple(
                'src.forex_bot.signal_generator',
                multilingual_news_analyzer=Mock(),
                get_order_flow_client=Mock(),
                get_market_depth_client=Mock(),
                get_correlation_client=Mock(),
                get_metrics_dashboard_client=Mock()
            ) as mocks:
                
                # Make advanced features raise errors
                mocks['multilingual_news_analyzer'].analyze.side_effect = Exception("News analysis error")
                mocks['get_order_flow_client'].side_effect = Exception("Order flow error")
                mocks['get_market_depth_client'].side_effect = Exception("Market depth error")
                mocks['get_correlation_client'].side_effect = Exception("Correlation error")
                mocks['get_metrics_dashboard_client'].side_effect = Exception("Metrics error")
                
                result = signal_generator.run_analysis_modules(
                    symbol, df_m5, df_h1, df_h4, now_utc
                )
                
                assert isinstance(result, dict)

    def test_gemini_client_error_scenarios(self, signal_generator):
        """Test various Gemini client error scenarios."""
        symbol = "EURUSD"
        
        # Test different types of context that might cause errors
        error_contexts = [
            {'malformed': float('inf')},  # Invalid float
            {'nested': {'very': {'deep': {'data': 'value'}}}},  # Very nested
            {'large_list': list(range(1000))},  # Large data
            {'mixed_types': [1, 'string', {'dict': 'value'}, [1, 2, 3]]},  # Mixed types
        ]
        
        for context in error_contexts:
            with patch('src.forex_bot.signal_generator.gemini_client') as mock_gemini:
                mock_gemini.generate_signal.side_effect = Exception("Gemini processing error")
                
                result = signal_generator.generate_signal(symbol, context)
                
                # Should handle error and return valid signal
                assert isinstance(result, str)
                assert result in ["BUY", "SELL", "HOLD"]

    def test_knowledge_base_context_edge_cases(self, signal_generator):
        """Test knowledge base context with edge cases."""
        symbol = "EURUSD"
        
        # Test with various query types
        edge_case_queries = [
            "",  # Empty string
            " ",  # Whitespace only
            "a" * 1000,  # Very long query
            "special!@#$%^&*()characters",  # Special characters
            "unicode: 你好世界",  # Unicode characters
        ]
        
        for query in edge_case_queries:
            with patch('src.forex_bot.signal_generator.qdrant_service') as mock_qdrant:
                mock_qdrant.QDRANT_FULLY_INITIALIZED = True
                mock_qdrant.get_qdrant_context.side_effect = Exception("KB processing error")
                
                result = signal_generator.get_knowledge_base_context(symbol, query)
                
                assert result == "KB Error"

    def test_prepare_analysis_context_with_complex_data(self, signal_generator):
        """Test prepare_analysis_context with complex data structures."""
        symbol = "EURUSD"
        digits = 5
        
        # Create complex DataFrames with various data types
        complex_data = {
            'time': [datetime.now(timezone.utc)],
            'open': [1.1000],
            'high': [1.1010],
            'low': [1.0990],
            'close': [1.1005],
            'volume': [1000],
            'complex_indicator': [{'nested': 'value'}],
            'nan_values': [float('nan')],
            'inf_values': [float('inf')]
        }
        
        df_h4 = pd.DataFrame(complex_data)
        df_m5 = pd.DataFrame(complex_data)
        
        # Complex news data
        complex_news = [
            {
                'title': 'Complex News',
                'content': 'A' * 10000,  # Very long content
                'metadata': {
                    'nested': {
                        'deep': {
                            'structure': 'value'
                        }
                    }
                }
            }
        ]
        
        # Complex analysis results
        complex_analysis = {
            'trend_analysis': {
                'nested_data': {
                    'values': list(range(100)),
                    'metadata': {'complex': True}
                }
            },
            'large_array': list(range(1000)),
            'special_values': {
                'nan': float('nan'),
                'inf': float('inf'),
                'neg_inf': float('-inf')
            }
        }
        
        result = signal_generator.prepare_analysis_context(
            symbol, df_h4, df_m5, digits, complex_news, complex_analysis
        )
        
        assert isinstance(result, dict)

    def test_signal_generation_with_model_instances(self, signal_generator):
        """Test signal generation with various model instance types."""
        symbol = "EURUSD"
        context = {'test': 'data'}
        
        # Test with different model instance types
        model_instances = [
            None,
            Mock(),
            "string_model",
            123,
            {'dict_model': 'value'},
            [1, 2, 3]
        ]
        
        for model_instance in model_instances:
            result = signal_generator.generate_signal(
                symbol, context, "test-model", model_instance
            )
            
            assert isinstance(result, str)
            assert result in ["BUY", "SELL", "HOLD"]

    def test_run_analysis_modules_with_macro_info_variations(self, signal_generator, sample_dataframes):
        """Test run_analysis_modules with various macro_info structures."""
        df_m5, df_h1, df_h4 = sample_dataframes
        symbol = "EURUSD"
        now_utc = datetime.now(timezone.utc)
        
        # Test with different macro_info structures
        macro_variations = [
            None,
            {},
            {'simple': 'value'},
            {'numeric': 123.45},
            {'boolean': True},
            {'list': [1, 2, 3]},
            {'nested': {'deep': {'value': 'test'}}},
            {'mixed': {'str': 'value', 'num': 123, 'bool': True, 'list': [1, 2]}},
            {'large_data': {'values': list(range(100))}},
            {'special_chars': {'key!@#': 'value$%^'}},
        ]
        
        for macro_info in macro_variations:
            result = signal_generator.run_analysis_modules(
                symbol, df_m5, df_h1, df_h4, now_utc, macro_info
            )
            
            assert isinstance(result, dict)

    def test_error_logging_coverage(self, signal_generator, sample_dataframes):
        """Test that error logging paths are covered."""
        df_m5, df_h1, df_h4 = sample_dataframes
        symbol = "EURUSD"
        now_utc = datetime.now(timezone.utc)
        
        # Test with modules that raise errors to ensure error logging is covered
        with patch.multiple(
            'src.forex_bot.signal_generator',
            trend_analyzer=Mock(),
            pattern_recognizer=Mock()
        ) as mocks:
            
            # Make modules raise different types of errors
            mocks['trend_analyzer'].analyze_trend.side_effect = [
                Exception("First error"),
                ValueError("Second error"),
                RuntimeError("Third error")
            ]
            mocks['pattern_recognizer'].recognize_patterns.side_effect = [
                ConnectionError("Network error"),
                TimeoutError("Timeout error"),
                KeyError("Key error")
            ]
            
            # Run multiple times to trigger different error paths
            for _ in range(3):
                result = signal_generator.run_analysis_modules(
                    symbol, df_m5, df_h1, df_h4, now_utc
                )
                assert isinstance(result, dict)
            
            # Verify error logging was called
            assert signal_generator.adapter.error.call_count >= 3