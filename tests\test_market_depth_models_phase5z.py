"""
Phase 5Z: Test for market_depth_visualizer/models.py to achieve 90%+ coverage.
"""

import pytest
from datetime import datetime, timezone
from src.forex_bot.market_depth_visualizer.models import (
    VisualizationType, ColorScheme, DepthChartSettings, TradeEntry, 
    MarketDepthSnapshot, MarketDepthVisualization
)


class TestMarketDepthModelsPhase5Z:
    """Test class to push market_depth_visualizer/models.py to 90%+ coverage"""

    def test_enums(self):
        """Test enums"""
        assert VisualizationType.DEPTH_CHART == "depth_chart"
        assert ColorScheme.DEFAULT == "default"

    def test_depth_chart_settings(self):
        """Test DepthChartSettings"""
        settings = DepthChartSettings()
        assert settings.price_levels == 10
        assert settings.show_imbalances is True

    def test_trade_entry(self):
        """Test TradeEntry with validation"""
        now = datetime.now(timezone.utc)
        
        trade = TradeEntry(
            timestamp=now,
            price=1.2345,
            volume=1000.0,
            direction="buy"
        )
        assert trade.volume == 1000.0
        
        with pytest.raises(ValueError):
            TradeEntry(
                timestamp=now,
                price=1.2345,
                volume=-100.0,
                direction="buy"
            )

    def test_market_depth_snapshot(self):
        """Test MarketDepthSnapshot"""
        now = datetime.now(timezone.utc)
        
        snapshot = MarketDepthSnapshot(
            symbol="EURUSD",
            timestamp=now,
            bid_prices=[1.2340],
            bid_volumes=[1000.0],
            ask_prices=[1.2341],
            ask_volumes=[800.0]
        )
        
        assert snapshot.best_bid == 1.2340
        assert snapshot.best_ask == 1.2341
        assert snapshot.total_bid_volume == 1000.0

    def test_market_depth_visualization(self):
        """Test MarketDepthVisualization"""
        now = datetime.now(timezone.utc)
        
        viz = MarketDepthVisualization(
            symbol="EURUSD",
            timestamp=now,
            visualization_type=VisualizationType.DEPTH_CHART,
            image_data="base64_data",
            settings={}
        )
        
        assert viz.symbol == "EURUSD"
        
        with pytest.raises(ValueError):
            MarketDepthVisualization(
                symbol="EURUSD",
                timestamp=now,
                visualization_type=VisualizationType.HEATMAP,
                image_data="",
                settings={}
            )