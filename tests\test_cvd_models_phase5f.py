"""
Phase 5F comprehensive tests to push cvd/models.py to 90%+ coverage.
"""

import pytest
import numpy as np
import pandas as pd
from datetime import datetime
from src.forex_bot.cvd import models

class TestCVDModelsPhase5F:
    """Phase 5F tests to achieve 90%+ coverage for cvd/models.py."""

    def test_cvd_result_basic_functionality(self):
        """Test CVDResult class basic functionality."""
        
        # Create test data
        timestamps = np.array([
            pd.Timestamp('2023-12-15 10:00:00'),
            pd.Timestamp('2023-12-15 10:01:00'),
            pd.Timestamp('2023-12-15 10:02:00'),
            pd.Timestamp('2023-12-15 10:03:00'),
            pd.Timestamp('2023-12-15 10:04:00')
        ])
        cvd_values = np.array([100.0, 105.0, 102.0, 108.0, 110.0])
        
        # Test successful creation
        cvd_result = models.CVDResult(
            timestamps=timestamps,
            cvd_values=cvd_values,
            symbol='EURUSD',
            timeframe=1,
            start_time=pd.Timestamp('2023-12-15 10:00:00'),
            end_time=pd.Timestamp('2023-12-15 10:04:00')
        )
        
        # Test basic attributes
        assert cvd_result.symbol == 'EURUSD'
        assert cvd_result.timeframe == 1
        assert cvd_result.start_time == pd.Timestamp('2023-12-15 10:00:00')
        assert cvd_result.end_time == pd.Timestamp('2023-12-15 10:04:00')
        assert len(cvd_result.timestamps) == 5
        assert len(cvd_result.cvd_values) == 5
        np.testing.assert_array_equal(cvd_result.timestamps, timestamps)
        np.testing.assert_array_equal(cvd_result.cvd_values, cvd_values)

    def test_cvd_result_to_dataframe_basic(self):
        """Test CVDResult to_dataframe method with basic data."""
        
        # Create test data
        timestamps = np.array([
            pd.Timestamp('2023-12-15 10:00:00'),
            pd.Timestamp('2023-12-15 10:01:00'),
            pd.Timestamp('2023-12-15 10:02:00')
        ])
        cvd_values = np.array([100.0, 105.0, 102.0])
        
        cvd_result = models.CVDResult(
            timestamps=timestamps,
            cvd_values=cvd_values,
            symbol='GBPUSD',
            timeframe=5
        )
        
        # Test DataFrame conversion
        df = cvd_result.to_dataframe()
        
        # Verify DataFrame structure
        assert isinstance(df, pd.DataFrame)
        assert len(df) == 3
        assert 'timestamp' in df.columns
        assert 'cvd' in df.columns
        assert len(df.columns) == 2  # Only basic columns
        
        # Verify data
        pd.testing.assert_series_equal(df['timestamp'], pd.Series(timestamps, name='timestamp'))
        pd.testing.assert_series_equal(df['cvd'], pd.Series(cvd_values, name='cvd'))

    def test_cvd_result_to_dataframe_with_additional_data(self):
        """Test CVDResult to_dataframe method with additional volume data."""
        
        # Create test data with additional volume arrays
        timestamps = np.array([
            pd.Timestamp('2023-12-15 10:00:00'),
            pd.Timestamp('2023-12-15 10:01:00'),
            pd.Timestamp('2023-12-15 10:02:00')
        ])
        cvd_values = np.array([100.0, 105.0, 102.0])
        buying_volume = np.array([1000.0, 1200.0, 800.0])
        selling_volume = np.array([800.0, 900.0, 1100.0])
        delta_volume = np.array([200.0, 300.0, -300.0])
        
        cvd_result = models.CVDResult(
            timestamps=timestamps,
            cvd_values=cvd_values,
            symbol='USDJPY',
            timeframe=15,
            buying_volume=buying_volume,
            selling_volume=selling_volume,
            delta_volume=delta_volume
        )
        
        # Test DataFrame conversion with additional data
        df = cvd_result.to_dataframe()
        
        # Verify DataFrame structure with additional columns
        assert isinstance(df, pd.DataFrame)
        assert len(df) == 3
        assert 'timestamp' in df.columns
        assert 'cvd' in df.columns
        assert 'buying_volume' in df.columns
        assert 'selling_volume' in df.columns
        assert 'delta_volume' in df.columns
        assert len(df.columns) == 5  # All columns present
        
        # Verify additional data
        pd.testing.assert_series_equal(df['buying_volume'], pd.Series(buying_volume, name='buying_volume'))
        pd.testing.assert_series_equal(df['selling_volume'], pd.Series(selling_volume, name='selling_volume'))
        pd.testing.assert_series_equal(df['delta_volume'], pd.Series(delta_volume, name='delta_volume'))

    def test_cvd_result_to_dataframe_mismatched_lengths(self):
        """Test CVDResult to_dataframe method with mismatched array lengths."""
        
        # Create test data with mismatched lengths
        timestamps = np.array([
            pd.Timestamp('2023-12-15 10:00:00'),
            pd.Timestamp('2023-12-15 10:01:00'),
            pd.Timestamp('2023-12-15 10:02:00')
        ])
        cvd_values = np.array([100.0, 105.0, 102.0])
        buying_volume = np.array([1000.0, 1200.0])  # Shorter array
        selling_volume = np.array([800.0, 900.0, 1100.0, 950.0])  # Longer array
        
        cvd_result = models.CVDResult(
            timestamps=timestamps,
            cvd_values=cvd_values,
            symbol='AUDUSD',
            timeframe=30,
            buying_volume=buying_volume,
            selling_volume=selling_volume
        )
        
        # Test DataFrame conversion - should only include matching lengths
        df = cvd_result.to_dataframe()
        
        # Verify DataFrame structure - mismatched arrays should be excluded
        assert isinstance(df, pd.DataFrame)
        assert len(df) == 3
        assert 'timestamp' in df.columns
        assert 'cvd' in df.columns
        assert 'buying_volume' not in df.columns  # Excluded due to length mismatch
        assert 'selling_volume' not in df.columns  # Excluded due to length mismatch
        assert len(df.columns) == 2  # Only basic columns

    def test_cvd_divergence_bullish_scenario(self):
        """Test CVDDivergence class with bullish divergence."""
        
        # Create bullish divergence (price down, CVD up)
        divergence = models.CVDDivergence(
            start_time=pd.Timestamp('2023-12-15 10:00:00'),
            end_time=pd.Timestamp('2023-12-15 11:00:00'),
            start_price=1.2500,
            end_price=1.2450,  # Price decreased
            start_cvd=1000.0,
            end_cvd=1200.0,    # CVD increased
            divergence_type='bullish',
            strength=0.75
        )
        
        # Test basic attributes
        assert divergence.start_time == pd.Timestamp('2023-12-15 10:00:00')
        assert divergence.end_time == pd.Timestamp('2023-12-15 11:00:00')
        assert divergence.start_price == 1.2500
        assert divergence.end_price == 1.2450
        assert divergence.start_cvd == 1000.0
        assert divergence.end_cvd == 1200.0
        assert divergence.divergence_type == 'bullish'
        assert divergence.strength == 0.75
        
        # Test properties
        assert divergence.is_bullish == True
        assert divergence.is_bearish == False
        assert abs(divergence.price_change - (-0.0050)) < 1e-10  # Price decreased
        assert divergence.cvd_change == 200.0      # CVD increased

    def test_cvd_divergence_bearish_scenario(self):
        """Test CVDDivergence class with bearish divergence."""
        
        # Create bearish divergence (price up, CVD down)
        divergence = models.CVDDivergence(
            start_time=pd.Timestamp('2023-12-15 12:00:00'),
            end_time=pd.Timestamp('2023-12-15 13:00:00'),
            start_price=1.2400,
            end_price=1.2480,   # Price increased
            start_cvd=1500.0,
            end_cvd=1300.0,     # CVD decreased
            divergence_type='bearish',
            strength=0.85
        )
        
        # Test basic attributes
        assert divergence.start_time == pd.Timestamp('2023-12-15 12:00:00')
        assert divergence.end_time == pd.Timestamp('2023-12-15 13:00:00')
        assert divergence.start_price == 1.2400
        assert divergence.end_price == 1.2480
        assert divergence.start_cvd == 1500.0
        assert divergence.end_cvd == 1300.0
        assert divergence.divergence_type == 'bearish'
        assert divergence.strength == 0.85
        
        # Test properties
        assert divergence.is_bullish == False
        assert divergence.is_bearish == True
        assert abs(divergence.price_change - 0.0080) < 1e-10   # Price increased
        assert divergence.cvd_change == -200.0     # CVD decreased

    def test_cvd_divergence_edge_cases(self):
        """Test CVDDivergence class with edge cases."""
        
        # Test zero price change
        divergence_zero_price = models.CVDDivergence(
            start_time=pd.Timestamp('2023-12-15 14:00:00'),
            end_time=pd.Timestamp('2023-12-15 15:00:00'),
            start_price=1.2500,
            end_price=1.2500,   # No price change
            start_cvd=1000.0,
            end_cvd=1100.0,
            divergence_type='bullish',
            strength=0.50
        )
        
        assert divergence_zero_price.price_change == 0.0
        assert divergence_zero_price.cvd_change == 100.0
        assert divergence_zero_price.is_bullish == True
        
        # Test zero CVD change
        divergence_zero_cvd = models.CVDDivergence(
            start_time=pd.Timestamp('2023-12-15 16:00:00'),
            end_time=pd.Timestamp('2023-12-15 17:00:00'),
            start_price=1.2400,
            end_price=1.2450,
            start_cvd=1200.0,
            end_cvd=1200.0,     # No CVD change
            divergence_type='bearish',
            strength=0.30
        )
        
        assert abs(divergence_zero_cvd.price_change - 0.0050) < 1e-10
        assert divergence_zero_cvd.cvd_change == 0.0
        assert divergence_zero_cvd.is_bearish == True
        
        # Test negative values
        divergence_negative = models.CVDDivergence(
            start_time=pd.Timestamp('2023-12-15 18:00:00'),
            end_time=pd.Timestamp('2023-12-15 19:00:00'),
            start_price=1.2300,
            end_price=1.2250,   # Negative price change
            start_cvd=-500.0,   # Negative CVD values
            end_cvd=-300.0,
            divergence_type='bullish',
            strength=0.90
        )
        
        assert abs(divergence_negative.price_change - (-0.0050)) < 1e-10
        assert divergence_negative.cvd_change == 200.0  # Still positive change
        assert divergence_negative.is_bullish == True

    def test_cvd_result_optional_fields(self):
        """Test CVDResult with optional fields set to None."""
        
        # Create minimal CVDResult
        timestamps = np.array([pd.Timestamp('2023-12-15 10:00:00')])
        cvd_values = np.array([100.0])
        
        cvd_result = models.CVDResult(
            timestamps=timestamps,
            cvd_values=cvd_values,
            symbol='NZDUSD',
            timeframe=60,
            start_time=None,
            end_time=None,
            buying_volume=None,
            selling_volume=None,
            delta_volume=None
        )
        
        # Test that optional fields are None
        assert cvd_result.start_time is None
        assert cvd_result.end_time is None
        assert cvd_result.buying_volume is None
        assert cvd_result.selling_volume is None
        assert cvd_result.delta_volume is None
        
        # Test DataFrame conversion with None values
        df = cvd_result.to_dataframe()
        assert len(df.columns) == 2  # Only basic columns
        assert 'timestamp' in df.columns
        assert 'cvd' in df.columns