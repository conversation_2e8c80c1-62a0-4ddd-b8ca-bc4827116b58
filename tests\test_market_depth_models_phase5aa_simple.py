"""
Phase 5AA: Simple test for market_depth_visualizer/models.py to push from 60% to 90%+ coverage.
"""

import pytest
from datetime import datetime, timezone
from unittest.mock import patch
from src.forex_bot.market_depth_visualizer.models import (
    VisualizationType, ColorScheme, DepthChartSettings, HeatmapSettings,
    TimeAndSalesSettings, LiquidityMapSettings, OrderFlowFootprintSettings,
    DashboardSettings, VisualizationSettings, TradeEntry, MarketDepthSnapshot,
    MarketDepthVisualization, MarketDepthDashboard
)


class TestMarketDepthModelsPhase5AASimple:
    """Simple test to push market_depth_visualizer/models.py to 90%+ coverage"""

    def test_all_settings_models(self):
        """Test all settings models with comprehensive parameters"""
        
        # Test HeatmapSettings
        heatmap = HeatmapSettings(
            price_levels=25,
            time_window=120,
            color_scheme=ColorScheme.DARK,
            custom_colormap="plasma",
            show_current_price=False,
            show_tooltips=False,
            interpolation="bilinear",
            normalization="log"
        )
        assert heatmap.price_levels == 25
        assert heatmap.normalization == "log"
        
        # Test TimeAndSalesSettings
        time_sales = TimeAndSalesSettings(
            max_entries=200,
            show_direction=False,
            color_scheme=ColorScheme.COLORBLIND,
            custom_buy_color="#00FF00",
            custom_sell_color="#FF0000",
            highlight_large_trades=False,
            large_trade_threshold=3.0
        )
        assert time_sales.max_entries == 200
        assert time_sales.large_trade_threshold == 3.0
        
        # Test LiquidityMapSettings
        liquidity = LiquidityMapSettings(
            price_levels=15,
            show_bid_liquidity=False,
            show_ask_liquidity=False,
            color_scheme=ColorScheme.LIGHT,
            custom_bid_color="#0000FF",
            custom_ask_color="#FF00FF",
            show_tooltips=False,
            normalization="log"
        )
        assert liquidity.price_levels == 15
        assert liquidity.normalization == "log"
        
        # Test OrderFlowFootprintSettings
        footprint = OrderFlowFootprintSettings(
            price_levels=30,
            time_window=90,
            show_imbalances=False,
            color_scheme=ColorScheme.CUSTOM,
            custom_buy_color="#AAFFAA",
            custom_sell_color="#FFAAAA",
            show_tooltips=False,
            show_delta=False,
            delta_type="trades"
        )
        assert footprint.price_levels == 30
        assert footprint.delta_type == "trades"    def test_dashboard_and_visualization_settings(self):
        """Test DashboardSettings and VisualizationSettings"""
        
        # Test custom dashboard layout
        custom_layout = [
            [VisualizationType.DEPTH_CHART, VisualizationType.HEATMAP],
            [VisualizationType.TIME_AND_SALES]
        ]
        
        dashboard = DashboardSettings(
            layout=custom_layout,
            refresh_interval=500,
            show_title=False,
            show_timestamp=False,
            color_scheme=ColorScheme.DARK,
            custom_background_color="#000000",
            custom_text_color="#FFFFFF"
        )
        assert dashboard.refresh_interval == 500
        assert dashboard.color_scheme == ColorScheme.DARK
        
        # Test VisualizationSettings
        viz_settings = VisualizationSettings(
            depth_chart=DepthChartSettings(price_levels=15),
            heatmap=HeatmapSettings(time_window=30),
            time_and_sales=TimeAndSalesSettings(max_entries=50),
            liquidity_map=LiquidityMapSettings(price_levels=25),
            order_flow_footprint=OrderFlowFootprintSettings(time_window=45),
            dashboard=dashboard
        )
        assert viz_settings.depth_chart.price_levels == 15
        assert viz_settings.dashboard.refresh_interval == 500

    def test_market_depth_snapshot_comprehensive(self):
        """Test MarketDepthSnapshot comprehensive functionality"""
        
        now = datetime.now(timezone.utc)
        
        # Test with multiple price levels
        snapshot = MarketDepthSnapshot(
            symbol="GBPUSD",
            timestamp=now,
            bid_prices=[1.3000, 1.2999, 1.2998],
            bid_volumes=[1000.0, 1500.0, 2000.0],
            ask_prices=[1.3001, 1.3002, 1.3003],
            ask_volumes=[800.0, 1200.0, 1600.0],
            trades=[]
        )
        
        # Test properties
        assert snapshot.total_bid_volume == 4500.0
        assert snapshot.total_ask_volume == 3600.0
        assert snapshot.cumulative_bid_volumes == [1000.0, 2500.0, 4500.0]
        assert snapshot.cumulative_ask_volumes == [800.0, 2000.0, 3600.0]
        
        # Test zero volume edge case
        zero_snapshot = MarketDepthSnapshot(
            symbol="EURUSD",
            timestamp=now,
            bid_prices=[1.2340],
            bid_volumes=[0.0],
            ask_prices=[1.2341],
            ask_volumes=[0.0]
        )
        assert zero_snapshot.imbalance_ratio is None

    def test_market_depth_dashboard_comprehensive(self):
        """Test MarketDepthDashboard comprehensive functionality"""
        
        now = datetime.now(timezone.utc)
        
        # Create multiple visualizations
        viz1 = MarketDepthVisualization(
            symbol="EURUSD",
            timestamp=now,
            visualization_type=VisualizationType.DEPTH_CHART,
            image_data="base64_depth_chart_data",
            settings={"price_levels": 10}
        )
        
        viz2 = MarketDepthVisualization(
            symbol="EURUSD",
            timestamp=now,
            visualization_type=VisualizationType.HEATMAP,
            image_data="base64_heatmap_data",
            settings={"time_window": 60}
        )
        
        dashboard = MarketDepthDashboard(
            symbol="EURUSD",
            timestamp=now,
            visualizations={
                VisualizationType.DEPTH_CHART: viz1,
                VisualizationType.HEATMAP: viz2
            },
            settings=DashboardSettings()
        )
        
        assert len(dashboard.visualizations) == 2
        assert VisualizationType.DEPTH_CHART in dashboard.visualizations