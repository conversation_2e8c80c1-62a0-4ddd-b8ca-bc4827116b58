"""
Complete coverage tests for signal_generator module to reach 90%.

This module focuses on the remaining uncovered lines including:
- Import error handling
- Feature flag conditionals
- Specific analysis paths
- Error handling scenarios
"""

import pytest
import pandas as pd
import logging
from datetime import datetime, timezone
from unittest.mock import Mock, MagicMock, patch, PropertyMock
from typing import Dict, Any, List

from src.forex_bot.signal_generator import SignalGenerator


class TestSignalGeneratorCompleteCoverage:
    """Test cases to achieve complete coverage of SignalGenerator."""

    @pytest.fixture
    def mock_logger(self):
        """Create a mock logger adapter."""
        return Mock(spec=logging.LoggerAdapter)

    @pytest.fixture
    def signal_generator(self, mock_logger):
        """Create a SignalGenerator instance with mocked logger."""
        return SignalGenerator(mock_logger)

    @pytest.fixture
    def sample_dataframes(self):
        """Create sample DataFrames for testing."""
        data = {
            'time': [datetime.now(timezone.utc)],
            'open': [1.1000],
            'high': [1.1010],
            'low': [1.0990],
            'close': [1.1005],
            'volume': [1000]
        }
        df = pd.DataFrame(data)
        return df.copy(), df.copy(), df.copy()

    @patch('src.forex_bot.signal_generator.config')
    def test_feature_flags_garch_enabled(self, mock_config, signal_generator, sample_dataframes):
        """Test analysis with GARCH forecast enabled."""
        mock_config.enable_garch_forecast = True
        mock_config.enable_hmm_regime = False
        mock_config.enable_heikin_ashi = False
        mock_config.enable_sentiment_analysis = False
        mock_config.enable_volume_profile = False
        mock_config.enable_vwap_analysis = False
        mock_config.enable_cvd_analysis = False
        mock_config.enable_cot_analysis = False
        mock_config.enable_pmi_analysis = False
        mock_config.enable_volatility_analysis = False
        
        df_m5, df_h1, df_h4 = sample_dataframes
        symbol = "EURUSD"
        now_utc = datetime.now(timezone.utc)
        
        with patch('src.forex_bot.signal_generator.garch_model') as mock_garch:
            mock_garch.get_volatility_context.return_value = {'volatility': 0.01}
            
            result = signal_generator.run_analysis_modules(
                symbol, df_m5, df_h1, df_h4, now_utc
            )
            
            assert isinstance(result, dict)

    @patch('src.forex_bot.signal_generator.config')
    def test_feature_flags_hmm_enabled(self, mock_config, signal_generator, sample_dataframes):
        """Test analysis with HMM regime detection enabled."""
        mock_config.enable_garch_forecast = False
        mock_config.enable_hmm_regime = True
        mock_config.enable_heikin_ashi = False
        mock_config.enable_sentiment_analysis = False
        mock_config.enable_volume_profile = False
        mock_config.enable_vwap_analysis = False
        mock_config.enable_cvd_analysis = False
        mock_config.enable_cot_analysis = False
        mock_config.enable_pmi_analysis = False
        mock_config.enable_volatility_analysis = False
        
        df_m5, df_h1, df_h4 = sample_dataframes
        symbol = "EURUSD"
        now_utc = datetime.now(timezone.utc)
        
        with patch('src.forex_bot.signal_generator.hmm_model') as mock_hmm:
            mock_hmm.get_hmm_context.return_value = {'regime': 'trending'}
            
            result = signal_generator.run_analysis_modules(
                symbol, df_m5, df_h1, df_h4, now_utc
            )
            
            assert isinstance(result, dict)

    @patch('src.forex_bot.signal_generator.config')
    def test_feature_flags_heikin_ashi_enabled(self, mock_config, signal_generator, sample_dataframes):
        """Test analysis with Heikin Ashi enabled."""
        mock_config.enable_garch_forecast = False
        mock_config.enable_hmm_regime = False
        mock_config.enable_heikin_ashi = True
        mock_config.enable_sentiment_analysis = False
        mock_config.enable_volume_profile = False
        mock_config.enable_vwap_analysis = False
        mock_config.enable_cvd_analysis = False
        mock_config.enable_cot_analysis = False
        mock_config.enable_pmi_analysis = False
        mock_config.enable_volatility_analysis = False
        
        df_m5, df_h1, df_h4 = sample_dataframes
        symbol = "EURUSD"
        now_utc = datetime.now(timezone.utc)
        
        with patch('src.forex_bot.signal_generator.ha_calculator') as mock_ha:
            mock_ha.get_ha_context.return_value = {'ha_trend': 'bullish'}
            
            result = signal_generator.run_analysis_modules(
                symbol, df_m5, df_h1, df_h4, now_utc
            )
            
            assert isinstance(result, dict)

    @patch('src.forex_bot.signal_generator.config')
    def test_feature_flags_sentiment_enabled(self, mock_config, signal_generator, sample_dataframes):
        """Test analysis with sentiment analysis enabled."""
        mock_config.enable_garch_forecast = False
        mock_config.enable_hmm_regime = False
        mock_config.enable_heikin_ashi = False
        mock_config.enable_sentiment_analysis = True
        mock_config.enable_volume_profile = False
        mock_config.enable_vwap_analysis = False
        mock_config.enable_cvd_analysis = False
        mock_config.enable_cot_analysis = False
        mock_config.enable_pmi_analysis = False
        mock_config.enable_volatility_analysis = False
        
        df_m5, df_h1, df_h4 = sample_dataframes
        symbol = "EURUSD"
        now_utc = datetime.now(timezone.utc)
        
        with patch('src.forex_bot.signal_generator.sentiment_analyzer') as mock_sentiment:
            mock_sentiment.get_sentiment_context.return_value = {'sentiment': 'positive'}
            
            result = signal_generator.run_analysis_modules(
                symbol, df_m5, df_h1, df_h4, now_utc
            )
            
            assert isinstance(result, dict)

    @patch('src.forex_bot.signal_generator.config')
    def test_feature_flags_volume_profile_enabled(self, mock_config, signal_generator, sample_dataframes):
        """Test analysis with volume profile enabled."""
        mock_config.enable_garch_forecast = False
        mock_config.enable_hmm_regime = False
        mock_config.enable_heikin_ashi = False
        mock_config.enable_sentiment_analysis = False
        mock_config.enable_volume_profile = True
        mock_config.enable_vwap_analysis = False
        mock_config.enable_cvd_analysis = False
        mock_config.enable_cot_analysis = False
        mock_config.enable_pmi_analysis = False
        mock_config.enable_volatility_analysis = False
        
        df_m5, df_h1, df_h4 = sample_dataframes
        symbol = "EURUSD"
        now_utc = datetime.now(timezone.utc)
        
        with patch('src.forex_bot.signal_generator.volume_profile_calculator') as mock_vp:
            mock_vp.get_volume_profile_context.return_value = {'poc': 1.1000}
            
            result = signal_generator.run_analysis_modules(
                symbol, df_m5, df_h1, df_h4, now_utc
            )
            
            assert isinstance(result, dict)

    @patch('src.forex_bot.signal_generator.config')
    def test_feature_flags_vwap_enabled(self, mock_config, signal_generator, sample_dataframes):
        """Test analysis with VWAP enabled."""
        mock_config.enable_garch_forecast = False
        mock_config.enable_hmm_regime = False
        mock_config.enable_heikin_ashi = False
        mock_config.enable_sentiment_analysis = False
        mock_config.enable_volume_profile = False
        mock_config.enable_vwap_analysis = True
        mock_config.enable_cvd_analysis = False
        mock_config.enable_cot_analysis = False
        mock_config.enable_pmi_analysis = False
        mock_config.enable_volatility_analysis = False
        
        df_m5, df_h1, df_h4 = sample_dataframes
        symbol = "EURUSD"
        now_utc = datetime.now(timezone.utc)
        
        with patch('src.forex_bot.signal_generator.vwap_calculator') as mock_vwap:
            mock_vwap.get_vwap_context.return_value = {'vwap': 1.1002}
            
            result = signal_generator.run_analysis_modules(
                symbol, df_m5, df_h1, df_h4, now_utc
            )
            
            assert isinstance(result, dict)

    @patch('src.forex_bot.signal_generator.config')
    def test_feature_flags_cvd_enabled(self, mock_config, signal_generator, sample_dataframes):
        """Test analysis with CVD enabled."""
        mock_config.enable_garch_forecast = False
        mock_config.enable_hmm_regime = False
        mock_config.enable_heikin_ashi = False
        mock_config.enable_sentiment_analysis = False
        mock_config.enable_volume_profile = False
        mock_config.enable_vwap_analysis = False
        mock_config.enable_cvd_analysis = True
        mock_config.enable_cot_analysis = False
        mock_config.enable_pmi_analysis = False
        mock_config.enable_volatility_analysis = False
        
        df_m5, df_h1, df_h4 = sample_dataframes
        symbol = "EURUSD"
        now_utc = datetime.now(timezone.utc)
        
        with patch('src.forex_bot.signal_generator.cvd_calculator') as mock_cvd:
            mock_cvd.get_cvd_context.return_value = {'cvd_trend': 'bullish'}
            
            result = signal_generator.run_analysis_modules(
                symbol, df_m5, df_h1, df_h4, now_utc
            )
            
            assert isinstance(result, dict)

    @patch('src.forex_bot.signal_generator.config')
    def test_feature_flags_cot_enabled(self, mock_config, signal_generator, sample_dataframes):
        """Test analysis with COT enabled."""
        mock_config.enable_garch_forecast = False
        mock_config.enable_hmm_regime = False
        mock_config.enable_heikin_ashi = False
        mock_config.enable_sentiment_analysis = False
        mock_config.enable_volume_profile = False
        mock_config.enable_vwap_analysis = False
        mock_config.enable_cvd_analysis = False
        mock_config.enable_cot_analysis = True
        mock_config.enable_pmi_analysis = False
        mock_config.enable_volatility_analysis = False
        
        df_m5, df_h1, df_h4 = sample_dataframes
        symbol = "EURUSD"
        now_utc = datetime.now(timezone.utc)
        
        with patch('src.forex_bot.signal_generator.cot_analyzer') as mock_cot:
            mock_cot.get_cot_context.return_value = {'cot_sentiment': 'bullish'}
            
            result = signal_generator.run_analysis_modules(
                symbol, df_m5, df_h1, df_h4, now_utc
            )
            
            assert isinstance(result, dict)

    @patch('src.forex_bot.signal_generator.config')
    def test_feature_flags_pmi_enabled(self, mock_config, signal_generator, sample_dataframes):
        """Test analysis with PMI enabled."""
        mock_config.enable_garch_forecast = False
        mock_config.enable_hmm_regime = False
        mock_config.enable_heikin_ashi = False
        mock_config.enable_sentiment_analysis = False
        mock_config.enable_volume_profile = False
        mock_config.enable_vwap_analysis = False
        mock_config.enable_cvd_analysis = False
        mock_config.enable_cot_analysis = False
        mock_config.enable_pmi_analysis = True
        mock_config.enable_volatility_analysis = False
        
        df_m5, df_h1, df_h4 = sample_dataframes
        symbol = "EURUSD"
        now_utc = datetime.now(timezone.utc)
        
        with patch('src.forex_bot.signal_generator.pmi_analyzer') as mock_pmi:
            mock_pmi.get_pmi_context.return_value = {'pmi_trend': 'expanding'}
            
            result = signal_generator.run_analysis_modules(
                symbol, df_m5, df_h1, df_h4, now_utc
            )
            
            assert isinstance(result, dict)

    @patch('src.forex_bot.signal_generator.config')
    def test_feature_flags_volatility_enabled(self, mock_config, signal_generator, sample_dataframes):
        """Test analysis with volatility analysis enabled."""
        mock_config.enable_garch_forecast = False
        mock_config.enable_hmm_regime = False
        mock_config.enable_heikin_ashi = False
        mock_config.enable_sentiment_analysis = False
        mock_config.enable_volume_profile = False
        mock_config.enable_vwap_analysis = False
        mock_config.enable_cvd_analysis = False
        mock_config.enable_cot_analysis = False
        mock_config.enable_pmi_analysis = False
        mock_config.enable_volatility_analysis = True
        
        df_m5, df_h1, df_h4 = sample_dataframes
        symbol = "EURUSD"
        now_utc = datetime.now(timezone.utc)
        
        with patch('src.forex_bot.signal_generator.volatility_analyzer') as mock_vol:
            mock_vol.get_volatility_context.return_value = {'volatility_regime': 'high'}
            
            result = signal_generator.run_analysis_modules(
                symbol, df_m5, df_h1, df_h4, now_utc
            )
            
            assert isinstance(result, dict)

    def test_error_handling_in_analysis_modules(self, signal_generator, sample_dataframes):
        """Test error handling when analysis modules fail."""
        df_m5, df_h1, df_h4 = sample_dataframes
        symbol = "EURUSD"
        now_utc = datetime.now(timezone.utc)
        
        # Mock modules to raise different types of exceptions
        with patch.multiple(
            'src.forex_bot.signal_generator',
            trend_analyzer=Mock(),
            pattern_recognizer=Mock(),
            garch_model=Mock(),
            sentiment_analyzer=Mock()
        ) as mocks:
            
            # Make different modules raise different exceptions
            mocks['trend_analyzer'].analyze_trend.side_effect = ValueError("Trend analysis error")
            mocks['pattern_recognizer'].recognize_patterns.side_effect = RuntimeError("Pattern error")
            mocks['garch_model'].forecast_volatility.side_effect = Exception("GARCH error")
            mocks['sentiment_analyzer'].analyze_sentiment.side_effect = ConnectionError("Sentiment error")
            
            result = signal_generator.run_analysis_modules(
                symbol, df_m5, df_h1, df_h4, now_utc
            )
            
            # Should handle all errors gracefully
            assert isinstance(result, dict)
            # Should have logged multiple errors
            assert signal_generator.adapter.error.call_count >= 4

    def test_prepare_analysis_context_error_handling(self, signal_generator):
        """Test error handling in prepare_analysis_context."""
        symbol = "EURUSD"
        # Create invalid DataFrames that will cause formatting errors
        df_h4 = pd.DataFrame({'invalid_column': [None, None]})
        df_m5 = pd.DataFrame({'another_invalid': ['bad_data']})
        digits = 5
        news_data = [{'malformed': 'news'}]
        analysis_results = {'invalid': None}
        
        # This should handle formatting errors gracefully
        result = signal_generator.prepare_analysis_context(
            symbol, df_h4, df_m5, digits, news_data, analysis_results
        )
        
        # Should return a dict even with errors
        assert isinstance(result, dict)

    def test_generate_signal_with_various_errors(self, signal_generator):
        """Test signal generation with various error scenarios."""
        symbol = "EURUSD"
        
        # Test with different types of malformed context
        test_contexts = [
            None,  # None context
            "invalid_string",  # String instead of dict
            123,  # Number instead of dict
            [],  # List instead of dict
            {'nested': {'very': {'deep': {'structure': 'value'}}}},  # Very nested
        ]
        
        for context in test_contexts:
            result = signal_generator.generate_signal(symbol, context)
            # Should always return a valid signal string
            assert isinstance(result, str)
            assert result in ["BUY", "SELL", "HOLD"]

    @patch('src.forex_bot.signal_generator.ENABLE_MULTILINGUAL_NEWS', True)
    @patch('src.forex_bot.signal_generator.ENABLE_ORDER_FLOW_ANALYSIS', True)
    @patch('src.forex_bot.signal_generator.ENABLE_MARKET_DEPTH_VISUALIZATION', True)
    @patch('src.forex_bot.signal_generator.ENABLE_CORRELATION_ANALYSIS', True)
    @patch('src.forex_bot.signal_generator.ENABLE_METRICS_DASHBOARD', True)
    def test_all_advanced_features_enabled(self, signal_generator, sample_dataframes):
        """Test with all advanced features enabled."""
        df_m5, df_h1, df_h4 = sample_dataframes
        symbol = "EURUSD"
        now_utc = datetime.now(timezone.utc)
        
        # Mock all advanced feature modules
        with patch.multiple(
            'src.forex_bot.signal_generator',
            multilingual_news_analyzer=Mock(),
            get_order_flow_client=Mock(),
            get_market_depth_client=Mock(),
            get_correlation_client=Mock(),
            get_metrics_dashboard_client=Mock()
        ) as mocks:
            
            # Configure advanced feature mocks
            mocks['multilingual_news_analyzer'].analyze.return_value = {'sentiment': 'positive'}
            mocks['get_order_flow_client'].return_value.analyze.return_value = {'flow': 'bullish'}
            mocks['get_market_depth_client'].return_value.analyze.return_value = {'depth': 'strong'}
            mocks['get_correlation_client'].return_value.analyze.return_value = {'correlation': 0.8}
            mocks['get_metrics_dashboard_client'].return_value.get_metrics.return_value = {'performance': 'good'}
            
            result = signal_generator.run_analysis_modules(
                symbol, df_m5, df_h1, df_h4, now_utc
            )
            
            assert isinstance(result, dict)

    def test_import_availability_flags(self):
        """Test that import availability flags are properly set."""
        # These should be accessible from the module
        from src.forex_bot import signal_generator
        
        # Test that the availability flags exist (they may be True or False depending on imports)
        assert hasattr(signal_generator, 'ORDER_FLOW_ANALYZER_AVAILABLE')
        assert hasattr(signal_generator, 'MARKET_DEPTH_VISUALIZER_AVAILABLE')
        assert hasattr(signal_generator, 'CORRELATION_MATRIX_AVAILABLE')
        assert hasattr(signal_generator, 'METRICS_DASHBOARD_AVAILABLE')
        assert hasattr(signal_generator, 'MULTILINGUAL_NEWS_AVAILABLE')
        
        # These should be boolean values
        assert isinstance(signal_generator.ORDER_FLOW_ANALYZER_AVAILABLE, bool)
        assert isinstance(signal_generator.MARKET_DEPTH_VISUALIZER_AVAILABLE, bool)
        assert isinstance(signal_generator.CORRELATION_MATRIX_AVAILABLE, bool)
        assert isinstance(signal_generator.METRICS_DASHBOARD_AVAILABLE, bool)
        assert isinstance(signal_generator.MULTILINGUAL_NEWS_AVAILABLE, bool)

    def test_backward_compatibility_functions_with_edge_cases(self):
        """Test backward compatibility functions with edge cases."""
        from src.forex_bot.signal_generator import (
            run_analysis_modules,
            get_knowledge_base_context,
            prepare_analysis_context,
            generate_signal
        )
        
        mock_logger = Mock(spec=logging.LoggerAdapter)
        
        # Test with minimal data
        df = pd.DataFrame({'close': [1.1]})
        symbol = "EURUSD"
        now_utc = datetime.now(timezone.utc)
        
        # Test run_analysis_modules with edge cases
        result = run_analysis_modules(symbol, df, df, df, now_utc, {}, mock_logger)
        assert isinstance(result, dict)
        
        # Test get_knowledge_base_context with edge cases
        result = get_knowledge_base_context(symbol, "", mock_logger, 1)
        assert isinstance(result, str)
        
        # Test prepare_analysis_context with edge cases
        result = prepare_analysis_context(symbol, df, df, 5, [], {}, mock_logger)
        assert isinstance(result, dict)
        
        # Test generate_signal with edge cases
        result = generate_signal(symbol, {}, mock_logger, None, None)
        assert isinstance(result, str)
        assert result in ["BUY", "SELL", "HOLD"]

    def test_complex_macro_info_processing(self, signal_generator, sample_dataframes):
        """Test processing of complex macro information."""
        df_m5, df_h1, df_h4 = sample_dataframes
        symbol = "EURUSD"
        now_utc = datetime.now(timezone.utc)
        
        # Create complex macro info with various data types
        complex_macro_info = {
            'economic_indicators': {
                'gdp': {'value': 2.5, 'trend': 'increasing'},
                'inflation': {'value': 3.2, 'trend': 'stable'},
                'unemployment': {'value': 4.1, 'trend': 'decreasing'}
            },
            'central_bank': {
                'interest_rate': 1.75,
                'policy_stance': 'hawkish',
                'next_meeting': '2024-03-15'
            },
            'market_sentiment': {
                'risk_on': True,
                'vix_level': 18.5,
                'yield_curve': 'normal'
            },
            'technical_levels': [1.0950, 1.1000, 1.1050],
            'news_sentiment': 0.65,
            'correlation_data': {
                'EURUSD_GBPUSD': 0.82,
                'EURUSD_USDJPY': -0.45
            }
        }
        
        result = signal_generator.run_analysis_modules(
            symbol, df_m5, df_h1, df_h4, now_utc, complex_macro_info
        )
        
        assert isinstance(result, dict)