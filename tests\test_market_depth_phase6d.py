"""
Phase 6D: Test for market_depth_visualizer/models.py to push from 59% to 90%+ coverage.
Targeting all remaining missing areas including comprehensive fallback implementations.
"""

import pytest
from datetime import datetime, timezone
from unittest.mock import patch, MagicMock
from src.forex_bot.market_depth_visualizer.models import (
    MarketDepthSnapshot, MarketDepthVisualization, MarketDepthDashboard,
    DashboardSettings, VisualizationType, ColorScheme
)


class TestMarketDepthPhase6D:
    """Test class to push market_depth_visualizer/models.py to 90%+ coverage"""

    def test_import_fallbacks_comprehensive(self):
        """Test import fallbacks for numpy and pandas (lines 14-15, 20-29)"""
        
        # Test numpy unavailable fallback (lines 14-15)
        with patch.dict('sys.modules', {'numpy': None}):
            with patch('src.forex_bot.market_depth_visualizer.models.NUMPY_AVAILABLE', False):
                import importlib
                import src.forex_bot.market_depth_visualizer.models as models_module
                importlib.reload(models_module)
                assert not models_module.NUMPY_AVAILABLE

        # Test pandas unavailable fallback (lines 20-29)
        with patch.dict('sys.modules', {'pandas': None}):
            with patch('src.forex_bot.market_depth_visualizer.models.PANDAS_AVAILABLE', False):
                import importlib
                import src.forex_bot.market_depth_visualizer.models as models_module
                importlib.reload(models_module)
                assert not models_module.PANDAS_AVAILABLE

    def test_remaining_validator_edge_cases(self):
        """Test remaining validator edge cases (lines 142-144, 165, 177, 230-238, 243-251, 263-265)"""
        
        now = datetime.now(timezone.utc)
        
        # Test TradeEntry volume validator (lines 142-144)
        from src.forex_bot.market_depth_visualizer.models import TradeEntry
        with pytest.raises(ValueError, match="Volume must be positive"):
            TradeEntry(
                timestamp=now,
                price=1.2340,
                volume=0.0,  # Zero volume
                direction="buy"
            )
        
        # Test bid_volumes length validator (line 165)
        with pytest.raises(ValueError, match="Bid volumes must have the same length"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340, 1.2339, 1.2338],
                bid_volumes=[1000.0, 1500.0],  # Different length
                ask_prices=[1.2341],
                ask_volumes=[800.0]
            )
        
        # Test ask_volumes length validator (line 177)
        with pytest.raises(ValueError, match="Ask volumes must have the same length"):
            MarketDepthSnapshot(
                symbol="EURUSD",
                timestamp=now,
                bid_prices=[1.2340],
                bid_volumes=[1000.0],
                ask_prices=[1.2341, 1.2342, 1.2343],
                ask_volumes=[800.0, 1200.0]  # Different length
            )    def test_property_edge_cases_comprehensive(self):
        """Test property edge cases (lines 193-195, 200-202, 222-225)"""
        
        now = datetime.now(timezone.utc)
        
        # Test properties with single values
        snapshot_single = MarketDepthSnapshot(
            symbol="EURUSD",
            timestamp=now,
            bid_prices=[1.2340],
            bid_volumes=[1000.0],
            ask_prices=[1.2341],
            ask_volumes=[800.0]
        )
        
        # Test best_bid property (line 193-195)
        assert snapshot_single.best_bid == 1.2340
        
        # Test best_ask property (line 200-202)
        assert snapshot_single.best_ask == 1.2341
        
        # Test spread calculation
        expected_spread = 1.2341 - 1.2340
        assert abs(snapshot_single.spread - expected_spread) < 1e-10
        
        # Test mid_price calculation
        expected_mid = (1.2340 + 1.2341) / 2
        assert abs(snapshot_single.mid_price - expected_mid) < 1e-10
        
        # Test total volumes
        assert snapshot_single.total_bid_volume == 1000.0
        assert snapshot_single.total_ask_volume == 800.0
        
        # Test imbalance ratio with zero total volume (lines 222-225)
        snapshot_zero = MarketDepthSnapshot(
            symbol="GBPUSD",
            timestamp=now,
            bid_prices=[1.3000],
            bid_volumes=[0.0],  # Zero volume
            ask_prices=[1.3001],
            ask_volumes=[0.0]   # Zero volume
        )
        # Should return None for zero total volume
        assert snapshot_zero.imbalance_ratio is None

    def test_comprehensive_fallback_models(self):
        """Test comprehensive fallback models when pydantic is unavailable (lines 276-371)"""
        
        # Test with pydantic unavailable to trigger ALL fallback classes
        with patch('src.forex_bot.market_depth_visualizer.models.PYDANTIC_AVAILABLE', False):
            import importlib
            import src.forex_bot.market_depth_visualizer.models as models_module
            importlib.reload(models_module)
            
            now = datetime.now(timezone.utc)
            
            # Test fallback BaseModel class
            class TestModel(models_module.BaseModel):
                test_field: str = "test"
            
            test_model = TestModel(test_field="value")
            assert test_model.test_field == "value"
            
            # Test BaseModel dict method
            model_dict = test_model.dict()
            assert model_dict["test_field"] == "value"
            
            # Test all fallback enum classes
            assert models_module.VisualizationType.DEPTH_CHART is not None
            assert models_module.ColorScheme.DARK is not None
            
            # Test fallback DashboardSettings
            dashboard_settings = models_module.DashboardSettings(
                layout=[[models_module.VisualizationType.DEPTH_CHART]],
                refresh_interval=2000,
                show_title=False,
                show_timestamp=False,
                color_scheme=models_module.ColorScheme.LIGHT
            )
            assert dashboard_settings.refresh_interval == 2000
            assert dashboard_settings.show_title is False
            
            # Test DashboardSettings dict method
            settings_dict = dashboard_settings.dict()
            assert settings_dict["refresh_interval"] == 2000            
            # Test fallback VisualizationSettings
            viz_settings = models_module.VisualizationSettings(
                depth_chart={"levels": 10},
                heatmap={"resolution": "high"},
                volume_profile={"bins": 20}
            )
            assert viz_settings.depth_chart["levels"] == 10
            
            # Test VisualizationSettings dict method
            viz_dict = viz_settings.dict()
            assert viz_dict["depth_chart"]["levels"] == 10
            
            # Test fallback TradeEntry
            trade_entry = models_module.TradeEntry(
                timestamp=now,
                price=1.2345,
                volume=500.0,
                direction="sell"
            )
            assert trade_entry.price == 1.2345
            assert trade_entry.direction == "sell"
            
            # Test TradeEntry dict method
            trade_dict = trade_entry.dict()
            assert trade_dict["price"] == 1.2345
            
            # Test fallback MarketDepthSnapshot
            snapshot = models_module.MarketDepthSnapshot(
                symbol="GBPUSD",
                timestamp=now,
                bid_prices=[1.3000, 1.2999],
                bid_volumes=[2000.0, 2500.0],
                ask_prices=[1.3001, 1.3002],
                ask_volumes=[1800.0, 2200.0],
                trades=[trade_entry]
            )
            assert snapshot.symbol == "GBPUSD"
            assert len(snapshot.bid_prices) == 2
            assert len(snapshot.trades) == 1
            
            # Test MarketDepthSnapshot dict method
            snapshot_dict = snapshot.dict()
            assert snapshot_dict["symbol"] == "GBPUSD"
            
            # Test fallback MarketDepthVisualization
            visualization = models_module.MarketDepthVisualization(
                type=models_module.VisualizationType.HEATMAP,
                image_data=b"test_image_data",
                metadata={"width": 800, "height": 600}
            )
            assert visualization.type == models_module.VisualizationType.HEATMAP
            assert visualization.metadata["width"] == 800
            
            # Test MarketDepthVisualization dict method
            viz_dict = visualization.dict()
            assert viz_dict["metadata"]["width"] == 800
            
            # Test fallback MarketDepthDashboard
            dashboard = models_module.MarketDepthDashboard(
                symbol="USDJPY",
                timestamp=now,
                visualizations={
                    models_module.VisualizationType.HEATMAP: visualization
                },
                settings=dashboard_settings,
                snapshots=[snapshot]
            )
            assert dashboard.symbol == "USDJPY"
            assert len(dashboard.visualizations) == 1
            assert len(dashboard.snapshots) == 1
            
            # Test MarketDepthDashboard dict method
            dashboard_dict = dashboard.dict()
            assert dashboard_dict["symbol"] == "USDJPY"