"""
Targeted tests to push vwap/models.py to 100% coverage.
"""

import numpy as np
import pandas as pd
from src.forex_bot.vwap.models import VWAPResult, VWAPCrossover


def test_vwap_result_to_dataframe_with_2sd_bands():
    """Test to_dataframe method with 2SD bands - covers lines 55, 58."""
    timestamps = np.array([pd.Timestamp('2023-01-01 00:00:00')])
    vwap_values = np.array([1.2000])
    upper_band_2sd = np.array([1.2040])
    lower_band_2sd = np.array([1.1960])

    result = VWAPResult(
        timestamps=timestamps,
        vwap_values=vwap_values,
        symbol='EURUSD',
        timeframe=60,
        upper_band_2sd=upper_band_2sd,
        lower_band_2sd=lower_band_2sd
    )

    df = result.to_dataframe()
    assert isinstance(df, pd.DataFrame)
    assert 'upper_band_2sd' in df.columns
    assert 'lower_band_2sd' in df.columns
    assert len(df) == 1


def test_vwap_result_to_dataframe_with_1sd_bands():
    """Test to_dataframe method with 1SD bands - covers lines 49, 52."""
    timestamps = np.array([pd.Timestamp('2023-01-01 00:00:00')])
    vwap_values = np.array([1.2000])
    upper_band_1sd = np.array([1.2020])
    lower_band_1sd = np.array([1.1980])

    result = VWAPResult(
        timestamps=timestamps,
        vwap_values=vwap_values,
        symbol='EURUSD',
        timeframe=60,
        upper_band_1sd=upper_band_1sd,
        lower_band_1sd=lower_band_1sd
    )

    df = result.to_dataframe()
    assert isinstance(df, pd.DataFrame)
    assert 'upper_band_1sd' in df.columns
    assert 'lower_band_1sd' in df.columns
    assert len(df) == 1

def test_vwap_crossover_properties():
    """Test VWAPCrossover properties - covers lines 83, 93."""
    # Test bullish crossover
    bullish = VWAPCrossover(
        timestamp=pd.Timestamp('2023-01-01 00:00:00'),
        price=1.2020,
        vwap=1.2000,
        direction='above',
        strength=0.8
    )

    assert bullish.is_bullish is True
    assert bullish.is_bearish is False

    # Test bearish crossover
    bearish = VWAPCrossover(
        timestamp=pd.Timestamp('2023-01-01 00:00:00'),
        price=1.1980,
        vwap=1.2000,
        direction='below',
        strength=0.8
    )

    assert bearish.is_bullish is False
    assert bearish.is_bearish is True